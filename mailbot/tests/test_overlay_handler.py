from unittest import mock
from faker import Faker

from django.test import TestCase

from mailbot.utils.overlays.first_time_sender_overlay import FirstTimeSenderOverlay
from mailbot.utils.overlays.inbox_overlay import InboxOverlay
from mailbot.utils.overlays.lucene_alert import <PERSON>neAlert
from mailbot.utils.overlays.forward_inbox_secondary_to_primary import ForwardInboxSecondaryToPrimary
from mailbot.utils.overlays.forward_lucene_alert_to_primary import ForwardLuceneAlertToPrimary
from mailbot.utils.overlays.forward_first_time_sender_overlay_to_primary import ForwardFirstTimeSenderOverlayToPrimary
from mailbot.utils.overlays.mixins import ForwardToPrimaryMixin
from mailbot.utils.overlays.registry import OverlayRegistry
from mailbot.utils.defaults import InboxOverlayLabel
from mailbot.tests.factories import (
    UserMailBotProfileFactory,
    SenderProfileFactory,
    ParsedMessageFactory,
)
from accounts.tests.factories import UserFactory
from execfn import ApplicationTag
from mailbot.utils.overlays.first_time_sender_overlay import FirstTimeSenderOverlay
from mailbot.utils.defaults import MailBotMessageHeaders, MailBotMessageCategory

# Create a test-specific faker with a fixed seed for consistent test data
faker = Faker()


def get_test_sender():
    """Factory function to generate consistent sender information for tests"""
    return {"name": faker.name(), "email": faker.email()}


class BaseOverlayTestCase(TestCase):
    """
    Base test class for overlay handlers with common setup logic.
    """

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.user = UserFactory()
        cls.application_tag = ApplicationTag.MailBot.value

    def setUp(self):
        # Get consistent sender data using our factory function
        self.sender_data = get_test_sender()

        # Create required objects
        self.user_mailbot_profile = UserMailBotProfileFactory(user=self.user)

        # Create a SenderProfile with specific test values
        self.sender_profile = SenderProfileFactory(
            user_mailbot_profile=self.user_mailbot_profile,
            sender_email=self.sender_data["email"],
            normalized_sender_email=self.sender_data["email"],
            sender_name=self.sender_data["name"],
            metadata={},
        )

        # Create a ParsedMessage with sender information matching the SenderProfile
        self.parsed_message = ParsedMessageFactory(
            user_mailbot_profile_id=self.user_mailbot_profile.id,
            user_id=self.user.id,
            user_email=self.user.email,
            from_name_email=(self.sender_data["name"], self.sender_data["email"]),
            service_provider=self.user_mailbot_profile.service_provider,
        )

        # Setup mock service that can be used by all tests
        self.mock_service = mock.MagicMock()
        self.mock_service.user = self.user
        self.mock_service.email = self.user.email
        self.mock_service.user_mailbot_profile = self.user_mailbot_profile
        self.mock_service.get_attachments.return_value = []


class TestOverlayRegistry(TestCase):
    """Test the OverlayRegistry functionality."""

    def setUp(self):
        """Reset the registry before each test."""
        OverlayRegistry.reset()

    def tearDown(self):
        """Reset the registry after each test."""
        OverlayRegistry.reset()

    def test_registry_initialization(self):
        """Test that the registry can be initialized."""
        self.assertFalse(OverlayRegistry.is_initialized())

        OverlayRegistry.initialize()

        self.assertTrue(OverlayRegistry.is_initialized())

    def test_registry_discovers_handlers(self):
        """Test that the registry discovers overlay handlers."""
        OverlayRegistry.initialize()

        handlers = OverlayRegistry.get_all_handlers()

        # Should discover the existing overlay handlers
        expected_handlers = [
            "first_time_sender_overlay",
            "inbox_overlay",
            "lucene_alert",
            "forward_inbox_secondary_to_primary",
            "forward_lucene_alert_to_primary",
            "forward_first_time_sender_overlay_to_primary",
        ]

        for handler_key in expected_handlers:
            self.assertIn(handler_key, handlers, f"Handler {handler_key} not found in registry")

    def test_get_handler_returns_correct_class(self):
        """Test that get_handler returns the correct handler class."""
        OverlayRegistry.initialize()

        # Test getting a specific handler
        handler_class = OverlayRegistry.get_handler("first_time_sender_overlay")
        self.assertIsNotNone(handler_class)

        self.assertEqual(handler_class, FirstTimeSenderOverlay)

    def test_get_handler_returns_none_for_invalid_key(self):
        """Test that get_handler returns None for invalid keys."""
        OverlayRegistry.initialize()

        handler_class = OverlayRegistry.get_handler("nonexistent_handler")
        self.assertIsNone(handler_class)

    def test_registry_auto_initializes_if_not_initialized(self):
        """Test that the registry auto-initializes if accessed before initialization."""
        self.assertFalse(OverlayRegistry.is_initialized())

        # This should trigger auto-initialization
        handlers = OverlayRegistry.get_all_handlers()

        self.assertTrue(OverlayRegistry.is_initialized())
        self.assertGreater(len(handlers), 0)

    def test_registry_skips_duplicate_initialization(self):
        """Test that the registry doesn't re-initialize if already initialized."""
        OverlayRegistry.initialize()
        initial_handlers = OverlayRegistry.get_all_handlers()

        # Initialize again
        OverlayRegistry.initialize()
        second_handlers = OverlayRegistry.get_all_handlers()

        # Should be the same
        self.assertEqual(initial_handlers, second_handlers)

    @mock.patch("mailbot.utils.overlays.registry.logger")
    def test_registry_handles_import_errors_gracefully(self, mock_logger):
        """Test that the registry handles import errors gracefully."""
        # This test verifies that the registry doesn't crash on import errors
        # The actual import error handling is tested by the real discovery process
        OverlayRegistry.initialize()

        # Should still be initialized even if some modules fail to import
        self.assertTrue(OverlayRegistry.is_initialized())

    def test_reset_clears_registry(self):
        """Test that reset clears the registry."""
        OverlayRegistry.initialize()
        self.assertTrue(OverlayRegistry.is_initialized())
        self.assertGreater(len(OverlayRegistry.get_all_handlers()), 0)

        OverlayRegistry.reset()

        self.assertFalse(OverlayRegistry.is_initialized())
        # Note: get_all_handlers() will auto-initialize, so we check the internal state
        self.assertEqual(len(OverlayRegistry._handlers), 0)


class TestForwardToPrimaryMixin(TestCase):
    """
    Test the ForwardToPrimaryMixin functionality.
    """

    def setUp(self):
        """Set up test dependencies."""

        # Create a base class that the mixin can extend
        class BaseTestOverlay:
            def __init__(self):
                self.service = mock.MagicMock()
                self.service.email = "<EMAIL>"
                self.primary_user = mock.MagicMock()
                self.primary_user.email = "<EMAIL>"

            def get_headers(self):
                return {"base_header": "base_value"}

            def build_context(self):
                return {"base_context": "base_value"}

        # Create a test class that uses the mixin
        class TestOverlay(ForwardToPrimaryMixin, BaseTestOverlay):
            pass

        self.overlay = TestOverlay()

    def test_to_name_returns_primary_user_email(self):
        """Test that to_name property returns primary user email."""
        self.assertEqual(self.overlay.to_name, "<EMAIL>")

    def test_get_headers_adds_message_category(self):
        """Test that headers include the forward message category."""
        headers = self.overlay.get_headers()

        # Should include base headers
        self.assertEqual(headers["base_header"], "base_value")

        # Should add the message category header
        self.assertEqual(
            headers[MailBotMessageHeaders.MESSAGE_CATEGORY.value],
            MailBotMessageCategory.FORWARD_FROM_SECONDARY_MAILBOX.value,
        )

    def test_build_context_adds_secondary_account_info(self):
        """Test that context includes secondary account information."""
        context = self.overlay.build_context()

        # Should include base context
        self.assertEqual(context["base_context"], "base_value")

        # Should add secondary account context
        self.assertEqual(context["secondary_account_email"], "<EMAIL>")


class TestFirstTimeSenderOverlayHandler(BaseOverlayTestCase):
    """
    Test the send() method of FirstTimeSenderOverlay which is a concrete subclass of BaseOverlay.
    """

    @mock.patch("mailbot.utils.overlays.first_time_sender_overlay.DomainTraining.objects.get_or_create")
    @mock.patch("mailbot.utils.overlays.base.BaseOverlay.archive_original_message")
    def test_send_overlay_email(self, mock_archive_message, mock_domain_training_get_or_create):
        # Create a mock EmailTemplate and set up the send_email method
        mock_email_template = mock.MagicMock()
        expected_message_id = "sent-message-id-123"
        mock_email_template.send_email.return_value = expected_message_id

        # Generate overlay options using faker
        overlay_options = [faker.word(), faker.word()]

        # Mock EmailTemplate.objects.get to return our mock template
        with mock.patch("applications.models.EmailTemplate.objects.get", return_value=mock_email_template):
            # Create the overlay handler
            overlay_handler = FirstTimeSenderOverlay(
                service=self.mock_service,
                parsed_message=self.parsed_message,
                sender_profile=self.sender_profile,
                options_for_overlay=overlay_options,
            )

            # Define a test context
            test_context = {
                "frontend_base_url": faker.url(),
                "token_for_user_training": f"mock-jwt-token-{faker.uuid4()}",
                "token_for_domain_training": f"mock-jwt-token-{faker.uuid4()}",
                "token_for_enable_domain_alerts": f"mock-jwt-token-{faker.uuid4()}",
                "token_for_disable_domain_alerts": f"mock-jwt-token-{faker.uuid4()}",
                "message_subject": faker.sentence(),
                "message_body": faker.paragraph(nb_sentences=5),
                "sender_email": self.sender_data["email"],
                "sender_domain": faker.domain_name(),
                "options": overlay_options,
                "backend_base_url": faker.url(),
                "preview_text": faker.text(max_nb_chars=150),
                "other_recipients": faker.email(),
            }

            with mock.patch.object(overlay_handler, "build_context", return_value=test_context):
                # Act
                result_message_id = overlay_handler.send()

                # Assert
                # Verify that get_attachments was called
                self.mock_service.get_attachments.assert_called_once_with(self.parsed_message)

                # Verify that send_email was called with correct arguments
                mock_email_template.send_email.assert_called_once()
                args, kwargs = mock_email_template.send_email.call_args

                # Check that the send_email was called with the right parameters
                self.assertEqual(kwargs["user"], self.user)
                self.assertIn("context", kwargs)
                self.assertEqual(kwargs["context"], test_context)
                self.assertEqual(kwargs["to"], f"{self.user.get_full_name()} <{self.user.email}>")
                self.assertEqual(kwargs["from_name"], f"{self.sender_data['name']} (via EmailZap)")
                self.assertIn("headers", kwargs)
                self.assertIn("attachments", kwargs)

                # Check that the domain training was updated (this happens in post_send)
                self.assertIn("fts_overlay_sent_count", self.sender_profile.metadata)
                self.assertEqual(self.sender_profile.metadata["fts_overlay_sent_count"], 1)

                # Verify that the method returned the expected message ID
                self.assertEqual(result_message_id, expected_message_id)


class TestInboxOverlayHandler(BaseOverlayTestCase):
    """
    Test the send() method of InboxOverlay which is a concrete subclass of BaseOverlay.
    """

    @mock.patch("mailbot.utils.overlays.inbox_overlay.get_inbox_overlay_instance")
    @mock.patch("mailbot.utils.overlays.base.BaseOverlay.archive_original_message")
    def test_send_overlay_email(self, mock_archive_message, mock_get_inbox_overlay_instance):
        # Setup mock overlay instance and result
        mock_overlay_instance = mock.MagicMock()
        mock_overlay_result = mock.MagicMock()
        mock_overlay_result.label = InboxOverlayLabel.DO_NOW
        mock_overlay_result.action = "Do this now"
        mock_overlay_result.summary = "This is a test summary"
        mock_overlay_instance.get_result.return_value = mock_overlay_result
        mock_get_inbox_overlay_instance.return_value = mock_overlay_instance

        # Create a mock EmailTemplate and set up the send_email method
        mock_email_template = mock.MagicMock()
        expected_message_id = "inbox-overlay-id-456"
        mock_email_template.send_email.return_value = expected_message_id

        # Mock EmailTemplate.objects.get to return our mock template
        # Note: InboxOverlay sets TEMPLATE_TAG dynamically in build_context, so we need to handle that
        with mock.patch("applications.models.EmailTemplate.objects.get", return_value=mock_email_template):
            # Create the overlay handler with a specific label
            overlay_handler = InboxOverlay(
                service=self.mock_service,
                parsed_message=self.parsed_message,
                overlay_label=InboxOverlayLabel.DO_NOW.value,
            )

            # Act
            result_message_id = overlay_handler.send()

            # Assert
            # Verify that get_attachments was called
            self.mock_service.get_attachments.assert_called_once_with(self.parsed_message)

            # Verify that send_email was called
            mock_email_template.send_email.assert_called_once()
            args, kwargs = mock_email_template.send_email.call_args

            # Check that the send_email was called with the right parameters
            self.assertEqual(kwargs["user"], self.user)
            self.assertIn("context", kwargs)
            self.assertEqual(kwargs["to"], f"{self.user.get_full_name()} <{self.user.email}>")
            # The from_name doesn't have the "(via EmailZap)" suffix in InboxOverlay
            self.assertEqual(kwargs["from_name"], f"{self.sender_data['name']}")
            self.assertIn("headers", kwargs)
            self.assertIn("attachments", kwargs)

            # Verify that archive_original_message was called
            # since InboxOverlay.should_archive_original_message() returns True
            mock_archive_message.assert_called_once()

            # Verify that the method returned the expected message ID
            self.assertEqual(result_message_id, expected_message_id)

    def test_should_auto_archive(self):
        """Test that InboxOverlay.should_archive_original_message always returns True."""
        overlay_handler = InboxOverlay(
            service=self.mock_service,
            parsed_message=self.parsed_message,
        )
        self.assertTrue(overlay_handler.should_archive_original_message())


class TestLuceneAlertOverlayHandler(BaseOverlayTestCase):
    """
    Test the send() method of LuceneAlert which is a concrete subclass of BaseOverlay.
    """

    @mock.patch("mailbot.utils.overlays.base.BaseOverlay.archive_original_message")
    def test_send_overlay_email(self, mock_archive_message):
        """Test the basic send functionality of LuceneAlert."""
        # Create a mock EmailTemplate and set up the send_email method
        mock_email_template = mock.MagicMock()
        expected_message_id = "lucene-alert-id-123"
        mock_email_template.send_email.return_value = expected_message_id

        # Mock EmailTemplate.objects.get to return our mock template
        with mock.patch("applications.models.EmailTemplate.objects.get", return_value=mock_email_template):
            # Create the overlay handler
            overlay_handler = LuceneAlert(
                service=self.mock_service,
                parsed_message=self.parsed_message,
            )

            # Define a test context
            test_context = {
                "frontend_base_url": faker.url(),
                "message_subject": faker.sentence(),
                "message_body": faker.paragraph(nb_sentences=5),
                "sender_email": self.sender_data["email"],
                "general_domain": faker.boolean(),
                "skip_archive_token": f"mock-jwt-token-{faker.uuid4()}",
                "skip_archive_sender_token": f"mock-jwt-token-{faker.uuid4()}",
                "skip_archive_domain_token": f"mock-jwt-token-{faker.uuid4()}",
                "preview_text": faker.text(max_nb_chars=150),
                "other_recipients": faker.email(),
            }

            with mock.patch.object(overlay_handler, "build_context", return_value=test_context):
                # Act
                result_message_id = overlay_handler.send()

                # Assert
                # Verify that get_attachments was called
                self.mock_service.get_attachments.assert_called_once_with(self.parsed_message)

                # Verify that send_email was called with correct arguments
                mock_email_template.send_email.assert_called_once()
                args, kwargs = mock_email_template.send_email.call_args

                # Check that the send_email was called with the right parameters
                self.assertEqual(kwargs["user"], self.user)
                self.assertIn("context", kwargs)
                self.assertEqual(kwargs["context"], test_context)
                self.assertEqual(kwargs["to"], f"{self.user.get_full_name()} <{self.user.email}>")
                self.assertEqual(kwargs["from_name"], f"{self.sender_data['name']} (via EmailZap)")
                self.assertIn("headers", kwargs)
                self.assertIn("attachments", kwargs)

                # Check that archive_original_message was called (lucene alerts should be archived)
                mock_archive_message.assert_called_once()

    def test_should_auto_archive(self):
        """Test that LuceneAlert.should_archive_original_message always returns True."""
        overlay_handler = LuceneAlert(
            service=self.mock_service,
            parsed_message=self.parsed_message,
        )
        self.assertTrue(overlay_handler.should_archive_original_message())


class TestForwardInboxSecondaryToPrimaryHandler(BaseOverlayTestCase):
    """
    Test the send() method of ForwardInboxSecondaryToPrimary which handles forwarding emails
    from secondary accounts to primary accounts using the mixin pattern.
    """

    def setUp(self):
        super().setUp()
        # Create a primary user for tests
        self.primary_user = UserFactory(username=f"primary_user_{faker.random_int(min=1000, max=9999)}")
        # Create a mailbot profile for the primary user
        self.primary_user.user_mailbot_profile = UserMailBotProfileFactory(user=self.primary_user)

    def test_send_overlay_email(self):
        """Test the send method of ForwardInboxSecondaryToPrimary overlay with mixin pattern."""
        # Create a mock EmailTemplate and set up the send_email method
        mock_email_template = mock.MagicMock()
        expected_message_id = "forward-inbox-id-123"
        mock_email_template.send_email.return_value = expected_message_id

        # Create a mock Message with metadata that contains forwarded_overlay_message_id
        mock_thread_message = mock.MagicMock()
        mock_thread_message.metadata = {"forwarded_overlay_message_id": "some-overlay-id"}

        # Mock to return our message when filtering by thread_id
        mock_message_filter = mock.MagicMock()
        mock_message_filter.only.return_value.first.return_value = mock_thread_message

        # Mock EmailTemplate.objects.get to return our mock template
        with mock.patch("applications.models.EmailTemplate.objects.get", return_value=mock_email_template), mock.patch(
            "mailbot.utils.overlays.base.get_primary_user", return_value=self.primary_user
        ), mock.patch(
            "mailbot.utils.overlays.forward_inbox_secondary_to_primary.Message.objects.filter",
            return_value=mock_message_filter,
        ):
            # Set the thread_id to be different from message_id to test the is_thread_message flag
            self.parsed_message.thread_id = "different-thread-id"

            # Create the overlay handler
            overlay_handler = ForwardInboxSecondaryToPrimary(
                service=self.mock_service,
                parsed_message=self.parsed_message,
                db_message=mock.MagicMock(),
            )

            # Act
            result_message_id = overlay_handler.send()

            # Assert
            # Verify that get_attachments was called
            self.mock_service.get_attachments.assert_called_once_with(self.parsed_message)

            # Verify that send_email was called with correct arguments
            mock_email_template.send_email.assert_called_once()
            args, kwargs = mock_email_template.send_email.call_args

            # Check that the send_email was called with the right parameters
            self.assertEqual(kwargs["user"], self.user, "User should be the secondary account user")
            self.assertIn("context", kwargs, "Context should be included")

            # Verify that to_name is set to primary user's email (mixin behavior)
            self.assertEqual(kwargs["to"], self.primary_user.email, "To address should be primary user's email")

            # Verify that from_name is set to sender's name
            self.assertEqual(kwargs["from_name"], self.sender_data["name"], "From name should be sender's name")

            # Verify headers include the right message category
            self.assertIn("headers", kwargs, "Headers should be included")
            # Verify the context has display_overlay_text set to False
            # (since it's a thread message with forwarded_overlay_message_id in metadata)
            self.assertFalse(
                kwargs["context"].get("display_overlay_text", True),
                "display_overlay_text should be False for thread messages with forwarded_overlay_message_id",
            )

            # Verify that the method returned the expected message ID
            self.assertEqual(result_message_id, expected_message_id)

    def test_send_overlay_email_thread_without_forwarded_id(self):
        """Test thread message without forwarded_overlay_message_id in the first message."""
        # Create a mock EmailTemplate and set up the send_email method
        mock_email_template = mock.MagicMock()
        mock_email_template.send_email.return_value = "forward-inbox-id-789"

        # Create a mock Message without forwarded_overlay_message_id in metadata
        mock_thread_message = mock.MagicMock()
        mock_thread_message.metadata = {}  # No forwarded_overlay_message_id

        # Mock to return our message when filtering by thread_id
        mock_message_filter = mock.MagicMock()
        mock_message_filter.only.return_value.first.return_value = mock_thread_message

        # Mock EmailTemplate.objects.get to return our mock template
        with mock.patch("applications.models.EmailTemplate.objects.get", return_value=mock_email_template), mock.patch(
            "mailbot.utils.overlays.base.get_primary_user", return_value=self.primary_user
        ), mock.patch(
            "mailbot.utils.overlays.forward_inbox_secondary_to_primary.Message.objects.filter",
            return_value=mock_message_filter,
        ):
            # Set the thread_id to be different from message_id to test the is_thread_message flag
            self.parsed_message.thread_id = "different-thread-id"

            # Create the overlay handler
            overlay_handler = ForwardInboxSecondaryToPrimary(
                service=self.mock_service,
                parsed_message=self.parsed_message,
                db_message=mock.MagicMock(),
            )

            # Act
            overlay_handler.send()

            # Get the call arguments after send() has completed
            args, kwargs = mock_email_template.send_email.call_args

            # Verify the context has display_overlay_text set to True
            # (thread message without forwarded_overlay_message_id)
            self.assertTrue(
                kwargs["context"].get("display_overlay_text", False),
                "display_overlay_text should be True for thread messages without forwarded_overlay_message_id",
            )

    def test_send_overlay_email_non_thread_message(self):
        """Test the send method with a non-thread message (message_id equals thread_id)."""
        # Create a mock EmailTemplate and set up the send_email method
        mock_email_template = mock.MagicMock()
        expected_message_id = "forward-inbox-id-456"
        mock_email_template.send_email.return_value = expected_message_id

        # Mock EmailTemplate.objects.get to return our mock template
        with mock.patch("applications.models.EmailTemplate.objects.get", return_value=mock_email_template), mock.patch(
            "mailbot.utils.overlays.base.get_primary_user", return_value=self.primary_user
        ):
            # Set the thread_id to be the same as message_id to test non-thread message behavior
            self.parsed_message.thread_id = self.parsed_message.message_id

            # Create the overlay handler
            overlay_handler = ForwardInboxSecondaryToPrimary(
                service=self.mock_service,
                parsed_message=self.parsed_message,
                db_message=mock.MagicMock(),
            )

            # Act
            result = overlay_handler.send()

            # Get the call arguments after send() has completed
            args, kwargs = mock_email_template.send_email.call_args

            # Verify the context has display_overlay_text set to True (since it's not a thread message)
            self.assertTrue(
                kwargs["context"].get("display_overlay_text", False),
                "display_overlay_text should be True for non-thread messages",
            )


class TestForwardLuceneAlertToPrimaryHandler(BaseOverlayTestCase):
    """
    Test the send() method of ForwardLuceneAlertToPrimary which forwards
    lucene alerts from secondary accounts to primary accounts using the mixin pattern.
    """

    def setUp(self):
        super().setUp()
        # Create a primary user for tests
        self.primary_user = UserFactory(username=f"primary_user_{faker.random_int(min=1000, max=9999)}")
        self.primary_user.user_mailbot_profile = UserMailBotProfileFactory(user=self.primary_user)

    @mock.patch("mailbot.utils.overlays.base.get_primary_user")
    def test_send_overlay_email(self, mock_get_primary_user):
        """Test the send method of ForwardLuceneAlertToPrimary overlay with mixin pattern."""
        # Set up mocks
        mock_get_primary_user.return_value = self.primary_user

        # Create a mock EmailTemplate and set up the send_email method
        mock_email_template = mock.MagicMock()
        expected_message_id = "forward-lucene-alert-id-123"
        mock_email_template.send_email.return_value = expected_message_id

        # Mock EmailTemplate.objects.get to return our mock template
        with mock.patch("applications.models.EmailTemplate.objects.get", return_value=mock_email_template):
            # Create the overlay handler
            overlay_handler = ForwardLuceneAlertToPrimary(
                service=self.mock_service,
                parsed_message=self.parsed_message,
                db_message=mock.MagicMock(),
            )

            # Mock the build_context method to return a test context
            test_context = {
                "message_subject": self.parsed_message.subject,
                "message_body": self.parsed_message.text_body,
                "sender_email": self.sender_data["email"],
                "sender_domain": faker.domain_name(),
                "skip_archive_token": f"mock-jwt-token-{faker.uuid4()}",
                "secondary_account_email": self.user.email,
            }

            with mock.patch.object(overlay_handler, "build_context", return_value=test_context):
                # Act
                result_message_id = overlay_handler.send()

                # Assert
                # Verify that get_attachments was called
                self.mock_service.get_attachments.assert_called_once_with(self.parsed_message)

                # Verify that send_email was called with correct arguments
                mock_email_template.send_email.assert_called_once()
                args, kwargs = mock_email_template.send_email.call_args

                # Check that the send_email was called with the right parameters
                self.assertEqual(kwargs["user"], self.user)
                self.assertIn("context", kwargs)
                self.assertEqual(kwargs["to"], self.primary_user.email)  # Mixin behavior
                self.assertEqual(kwargs["from_name"], f"{self.sender_data['name']} (via EmailZap)")

                # Verify headers include the right message category
                self.assertIn("headers", kwargs)
                # Verify context includes secondary account email from mixin
                self.assertEqual(
                    kwargs["context"]["secondary_account_email"],
                    self.user.email,
                    "Context should include secondary account email from mixin",
                )

                # Verify that the method returned the expected message ID
                self.assertEqual(result_message_id, expected_message_id)


class TestForwardFirstTimeSenderOverlayToPrimaryHandler(BaseOverlayTestCase):
    """
    Test the send() method of ForwardFirstTimeSenderOverlayToPrimary which forwards
    first-time sender overlays from secondary accounts to primary accounts using the mixin pattern.
    """

    def setUp(self):
        super().setUp()
        # Create a primary user for tests
        self.primary_user = UserFactory(username=f"primary_user_{faker.random_int(min=1000, max=9999)}")
        self.primary_user.user_mailbot_profile = UserMailBotProfileFactory(user=self.primary_user)

    @mock.patch("mailbot.utils.overlays.base.get_primary_user")
    @mock.patch("mailbot.utils.overlays.first_time_sender_overlay.DomainTraining.objects.get_or_create")
    def test_send_overlay_email(self, mock_domain_training_get_or_create, mock_get_primary_user):
        """Test the send method of ForwardFirstTimeSenderOverlayToPrimary overlay with mixin pattern."""
        # Set up mocks
        mock_get_primary_user.return_value = self.primary_user

        # Generate overlay options using faker
        overlay_options = [faker.word(), faker.word()]

        # Create a mock EmailTemplate and set up the send_email method
        mock_email_template = mock.MagicMock()
        expected_message_id = "forward-fts-overlay-id-123"
        mock_email_template.send_email.return_value = expected_message_id

        # Mock EmailTemplate.objects.get to return our mock template
        with mock.patch("applications.models.EmailTemplate.objects.get", return_value=mock_email_template):
            # Create the overlay handler
            overlay_handler = ForwardFirstTimeSenderOverlayToPrimary(
                service=self.mock_service,
                parsed_message=self.parsed_message,
                db_message=mock.MagicMock(),
                sender_profile=self.sender_profile,
                options_for_overlay=overlay_options,
            )

            # Mock the build_context method to return a test context
            test_context = {
                "message_subject": self.parsed_message.subject,
                "message_body": self.parsed_message.text_body,
                "sender_email": self.sender_data["email"],
                "sender_domain": faker.domain_name(),
                "options": overlay_options,
                "token_for_user_training": f"mock-jwt-token-{faker.uuid4()}",
                "secondary_account_email": self.user.email,
            }

            with mock.patch.object(overlay_handler, "build_context", return_value=test_context):
                # Act
                result_message_id = overlay_handler.send()

                # Assert
                # Verify that get_attachments was called
                self.mock_service.get_attachments.assert_called_once_with(self.parsed_message)

                # Verify that send_email was called with correct arguments
                mock_email_template.send_email.assert_called_once()
                args, kwargs = mock_email_template.send_email.call_args

                # Check that the send_email was called with the right parameters
                self.assertEqual(kwargs["user"], self.user)
                self.assertIn("context", kwargs)
                self.assertEqual(kwargs["to"], self.primary_user.email)  # Mixin behavior
                self.assertEqual(kwargs["from_name"], f"{self.sender_data['name']} (via EmailZap)")

                # Verify headers include the right message category
                self.assertIn("headers", kwargs)

                # Verify context includes secondary account email from mixin
                self.assertEqual(
                    kwargs["context"]["secondary_account_email"],
                    self.user.email,
                    "Context should include secondary account email from mixin",
                )

                # Verify that the method returned the expected message ID
                self.assertEqual(result_message_id, expected_message_id)
