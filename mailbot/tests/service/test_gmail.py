from unittest import mock

from django.db.models.signals import post_save
from django.test import TestCase
from faker import Factory
from google.auth.exceptions import RefreshError

from accounts.tests.factories import UserFactory
from mailbot.models import MailBotGenericLabel, Message
from mailbot.receivers import schedule_archival, increment_processed_count
from mailbot.service.gmail import GmailService
from mailbot.tests.factories import UserMailBotProfileFactory, MessageFactory
from mailbot.utils.defaults import GmailKnownLabelName, GMAIL_LABELS
from mailbot.utils.exceptions import ModifyMessageLabelsError

faker = Factory.create()


@mock.patch(
    "mailbot.service.gmail.GMailBox",
    side_effect=RefreshError("API service not available for test cases"),
)  # must be imported from module under testing
class GmailServiceTest(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.user = UserFactory()
        cls.user_mailbot_profile = UserMailBotProfileFactory(user=cls.user)
        cls.zapped_label_id = faker.uuid4()
        cls.user_mailbot_profile.label_mappings.update(
            {
                cls.zapped_label_id: GMAIL_LABELS[MailBotGenericLabel.ZAPPED.value],
            }
        )

    def setUp(self):
        post_save.disconnect(receiver=schedule_archival, sender=Message)
        post_save.disconnect(receiver=increment_processed_count, sender=Message)

    def tearDown(self) -> None:
        post_save.connect(receiver=schedule_archival, sender=Message)
        post_save.connect(receiver=increment_processed_count, sender=Message)

    def test_is_mailbot_present__FalseForTestCases(self, *args):
        """
        Test that Gmail API service is disabled by default for all test cases.
        """
        # Given-When
        service = GmailService(user_mailbot_profile=self.user_mailbot_profile)
        # Then
        self.assertFalse(service.is_mailbox_present)

    def test_is_sender_stats_enabled__LabelIdsContainsTrash__ReturnsFalse(self, *args):
        """
        Test that sender stats should be disabled when an email is moved to trash.
        """
        # Given
        label_ids = [GmailKnownLabelName.TRASH.value, GmailKnownLabelName.SENT.value, GmailKnownLabelName.INBOX.value]
        service = GmailService(user_mailbot_profile=self.user_mailbot_profile)
        # When
        is_sender_stats_enabled = service.is_sender_stats_enabled(label_ids=label_ids)
        # Then
        self.assertFalse(is_sender_stats_enabled)

    def test___prepare_sets_for_delta_change__CorrectResult(self, *args):
        """
        Test that sets calculated for delta change in labels are correct.
        """
        # Given
        service = GmailService(user_mailbot_profile=self.user_mailbot_profile)
        stored_label_ids = [
            self.zapped_label_id,
            GmailKnownLabelName.IMPORTANT.value,
            GmailKnownLabelName.UNREAD.value,
            GmailKnownLabelName.CATEGORY_PERSONAL.value,
        ]
        # User reads the email and moved it to inbox
        final_label_ids = [
            GmailKnownLabelName.INBOX.value,
            self.zapped_label_id,
            GmailKnownLabelName.IMPORTANT.value,
            GmailKnownLabelName.CATEGORY_PERSONAL.value,
        ]
        # When
        (
            stored_label_ids_set,
            stored_mailbot_label_ids_set,
            final_label_ids_set,
            final_mailbot_label_ids_set,
            added_label_ids_set,
            added_mailbot_label_ids_set,
            removed_label_ids_set,
            _,
        ) = service._prepare_sets_for_delta_change(stored_label_ids=stored_label_ids, final_label_ids=final_label_ids)
        # Then
        self.assertSetEqual(set(stored_label_ids), stored_label_ids_set)
        self.assertSetEqual({self.zapped_label_id}, stored_mailbot_label_ids_set)
        self.assertSetEqual(set(final_label_ids), final_label_ids_set)
        self.assertSetEqual({GmailKnownLabelName.INBOX.value, self.zapped_label_id}, final_mailbot_label_ids_set)
        self.assertSetEqual({GmailKnownLabelName.INBOX.value}, added_label_ids_set)
        self.assertSetEqual({GmailKnownLabelName.INBOX.value}, added_mailbot_label_ids_set)
        self.assertSetEqual({GmailKnownLabelName.UNREAD.value}, removed_label_ids_set)

    def test_multi_mailbot_labels_pre_check__MoveFromMailbotLabelToInbox__RemoveOldMailbotLabel(self, MockGmailBox):
        """
        Test that when mailbot labelled message is moved to inbox, old mailbot label is removed by emailzap and multi_mailbot_labels_pre_check pass.
        """
        # Given
        MockGmailBox.side_effect = lambda user_mailbot_profile: mock.Mock()
        service = GmailService(user_mailbot_profile=self.user_mailbot_profile)
        message_id = faker.uuid4()
        stored_label_ids = [
            self.zapped_label_id,
            GmailKnownLabelName.IMPORTANT.value,
            GmailKnownLabelName.UNREAD.value,
            GmailKnownLabelName.CATEGORY_PERSONAL.value,
        ]
        # User reads the email and moved it to inbox
        final_label_ids = [
            GmailKnownLabelName.INBOX.value,
            self.zapped_label_id,
            GmailKnownLabelName.IMPORTANT.value,
            GmailKnownLabelName.CATEGORY_PERSONAL.value,
        ]
        (
            stored_label_ids_set,
            stored_mailbot_label_ids_set,
            final_label_ids_set,
            final_mailbot_label_ids_set,
            added_label_ids_set,
            added_mailbot_label_ids_set,
            removed_label_ids_set,
            _,
        ) = service._prepare_sets_for_delta_change(stored_label_ids=stored_label_ids, final_label_ids=final_label_ids)
        # When
        is_multi_mailbot_label_pre_check_passed = service._multi_mailbot_labels_pre_check(
            message_id=message_id,
            final_label_ids=final_label_ids,
            added_mailbot_label_ids_set=added_mailbot_label_ids_set,
            added_label_ids_set=added_label_ids_set,
            removed_label_ids_set=removed_label_ids_set,
            final_mailbot_label_ids_set=final_mailbot_label_ids_set,
            stored_mailbot_label_ids_set=stored_mailbot_label_ids_set,
        )
        # Then
        self.assertTrue(is_multi_mailbot_label_pre_check_passed)
        service.mailbox.update_labels.assert_called_once_with(
            message_id, remove_label_ids=[self.zapped_label_id], add_label_ids=None
        )

    def test__move_by_bot__OnUpdateLabelsAPIError__RollbackDatabase(self, MockGmailBox):
        """
        Test that when mailbot moves some message and error is raised by API for some reason, database remains consistent.
        """
        # Given
        label_ids = [self.zapped_label_id, GmailKnownLabelName.UNREAD.value]
        generic_label = MailBotGenericLabel.ZAPPED.value
        message = MessageFactory(
            user_mailbot_profile=self.user_mailbot_profile,
            metadata={"label_ids": label_ids},
            generic_label=generic_label,
        )
        added_label_ids = {GmailKnownLabelName.INBOX.value}
        removed_label_ids = {self.zapped_label_id}
        MockGmailBox.side_effect = lambda user_mailbot_profile: mock.Mock()
        service = GmailService(user_mailbot_profile=self.user_mailbot_profile)
        service.mailbox.update_labels.side_effect = ModifyMessageLabelsError(
            message_id=message.message_id,
            added_label_ids=added_label_ids,
            removed_label_ids=removed_label_ids,
            reason="",
            status_code="",
        )
        # When
        with self.assertRaises(ModifyMessageLabelsError):
            service._move_by_bot(message.message_id, remove_label_ids=removed_label_ids, add_label_ids=added_label_ids)
        # Then
        message.refresh_from_db()
        self.assertListEqual(label_ids, message.metadata["label_ids"])
        self.assertEqual(generic_label, message.generic_label)
