import base64

from django.conf import settings
from django.core.exceptions import ValidationError
from django.test import TestCase
from faker import Factory

from accounts.tests.factories import UserFactory
from mailbot.tests.factories import UserMailBotProfileFactory
from mailbot.utils.defaults import GmailKnown<PERSON>abelName
from mailbot.utils.message_parser import parse_message

faker = Factory.create()


class MessageParserTestCase(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.user = UserFactory()
        cls.user_mailbot_profile = UserMailBotProfileFactory(user=cls.user)
        cls.user_mailbot_profile.service_provider = settings.SERVICE_PROVIDER_GOOGLE

    def setUp(self):
        self.message_id = faker.hexify("^" * 16)
        self.thread_id = faker.hexify("^" * 16)
        # Subject and body of message
        self.subject = faker.sentence()
        self.text_body = faker.paragraph(nb_sentences=10, variable_nb_sentences=False)
        # Most common HTML alternative of Text email is just wrapping it in div
        self.html_body = f'<div dir="ltr">{self.text_body}</div>'
        # We receive message body of Text and Html in base64 encoded form
        self.encoded_text_body = base64.urlsafe_b64encode(self.text_body.encode()).decode()
        self.encoded_html_body = base64.urlsafe_b64encode(self.html_body.encode()).decode()
        # Name and email of the sender
        self.from_email = faker.email()
        self.from_name = faker.name()
        # Name and email of 1st blind carbon copy recipient
        self.bcc_name_email__0__0 = faker.name()
        self.bcc_name_email__0__1 = faker.email()
        # Name and email of 2nd blind carbon copy recipient
        self.bcc_name_email__1__0 = faker.name()
        self.bcc_name_email__1__1 = faker.email()
        # Bcc header that we receive in message payload is comma separated string
        self.bcc_name_email = f"{self.bcc_name_email__0__0} <{self.bcc_name_email__0__1}>, {self.bcc_name_email__1__0} <{self.bcc_name_email__1__1}>"

        self.gmail_api_message = {
            "id": self.message_id,
            "threadId": self.thread_id,
            "labelIds": [GmailKnownLabelName.SENT.value],
            "payload": {
                "partId": "",
                "mimeType": "multipart/alternative",
                "filename": "",
                "headers": [
                    {"name": "Date", "value": "Tue, 13 Feb 2024 19:44:18 +0530"},
                    {"name": "Bcc", "value": self.bcc_name_email},
                    {"name": "Subject", "value": self.subject},
                    {"name": "From", "value": f"{self.from_name} <{self.from_email}>"},
                ],
                "body": {"size": 0},
                "parts": [
                    {
                        "partId": "0",
                        "mimeType": "text/plain",
                        "filename": "",
                        "headers": [{"name": "Content-Type", "value": 'text/plain; charset="UTF-8"'}],
                        "body": {"size": len(self.text_body), "data": self.encoded_text_body},
                    },
                    {
                        "partId": "1",
                        "mimeType": "text/html",
                        "filename": "",
                        "headers": [{"name": "Content-Type", "value": 'text/html; charset="UTF-8"'}],
                        "body": {"size": len(self.html_body), "data": self.encoded_html_body},
                    },
                ],
            },
            "historyId": "895145",
            "internalDate": "1707833658000",
        }

    def test_parse_gmail_message__Success(self):
        """
        Test that parsed gmail message works as expected.
        """
        # Given-When
        parsed_message = parse_message(user_mailbot_profile=self.user_mailbot_profile, message=self.gmail_api_message)
        # Then
        self.assertEqual(self.message_id, parsed_message.message_id)
        self.assertEqual(self.thread_id, parsed_message.thread_id)
        self.assertEqual(self.html_body, parsed_message.html_body)
        self.assertEqual(self.text_body, parsed_message.text_body)
        self.assertEqual(self.from_name, parsed_message.from_name_email[0])
        self.assertEqual(self.from_email, parsed_message.from_name_email[1])
        self.assertEqual(self.bcc_name_email__0__0, parsed_message.bcc_name_email[0][0])
        self.assertEqual(self.bcc_name_email__0__1, parsed_message.bcc_name_email[0][1])
        self.assertEqual(self.bcc_name_email__1__0, parsed_message.bcc_name_email[1][0])
        self.assertEqual(self.bcc_name_email__1__1, parsed_message.bcc_name_email[1][1])

    def test_parse_gmail_message__SkipUnDisclosedRecipients(self):
        """
        Test that parsed gmail message skips undisclosed recipients in `To`
        """
        # Given
        self.gmail_api_message["payload"]["headers"] = [
            {"name": "From", "value": f"{self.from_name} <{self.from_email}>"},
            {"name": "To", "value": "undisclosed-recipients:;"},
        ]
        # When
        parsed_message = parse_message(user_mailbot_profile=self.user_mailbot_profile, message=self.gmail_api_message)
        # Then
        expected_result = []
        self.assertListEqual(expected_result, parsed_message.to_name_email)

    def test_parse_gmail_message__InvalidEmailInFromHeader__RaisesValidationError(self):
        """
        Test that parsed gmail message raises invalidation error when wrong email is used in `From` header
        """
        # Given
        self.gmail_api_message["payload"]["headers"] = [
            {"name": "From", "value": '"kmorgan@<EMAIL>" <kmorgan@<EMAIL>>'},
        ]
        # When-Then
        with self.assertRaises(ValidationError):
            parse_message(user_mailbot_profile=self.user_mailbot_profile, message=self.gmail_api_message)

    def test_parse_gmail_message__InvalidEmailInToHeader__ReturnsAllValidEmailRecipients(self):
        """
        Test that even if some emails in `To` header are invalid, all other valid recipients are returned
        """
        # Given
        self.gmail_api_message["payload"]["headers"] = [
            {"name": "From", "value": f"{self.from_name} <{self.from_email}>"},
            {
                "name": "To",
                "value": '"<EMAIL>" <<EMAIL>>, "kmorgan@<EMAIL>" <kmorgan@<EMAIL>>, "Hagemann, Robert" <<EMAIL>>, "<EMAIL>" <"<EMAIL>"@unknown.email>',
            },
        ]
        # When
        parsed_gmail_message = parse_message(
            user_mailbot_profile=self.user_mailbot_profile, message=self.gmail_api_message
        )
        # Then
        expected_result = [
            ("<EMAIL>", "<EMAIL>"),
            ("Hagemann, Robert", "<EMAIL>"),
            ("<EMAIL>", '"<EMAIL>"@unknown.email'),
        ]
        self.assertListEqual(
            expected_result,
            parsed_gmail_message.to_name_email,
        )

    def test_parse_gmail_message__NormalizedEmail__ValidatedEmailRegex(self):
        """
        Test that an invalid un-normalized email does not raise an exception when it's corresponding normalized email is valid email regex.
        """
        # Given
        self.gmail_api_message["payload"]["headers"] = [
            {"name": "From", "value": "Lex <<EMAIL>>"}
        ]
        # When
        parsed_gmail_message = parse_message(
            user_mailbot_profile=self.user_mailbot_profile, message=self.gmail_api_message
        )
        # Then
        expected_result = "<EMAIL>"
        self.assertEqual(expected_result, parsed_gmail_message.from_name_email[1])
