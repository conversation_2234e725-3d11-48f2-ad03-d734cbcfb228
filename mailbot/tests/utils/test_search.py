import uuid

from django.test.testcases import SimpleTestCase

from mailbot.utils.search import search_phrases


class SearchPhrasesTestCase(SimpleTestCase):
    def setUp(self) -> None:
        self.document = {
            "id": str(uuid.uuid4()),
            "body": "The quick brown fox jumps over the lazy dog",
            "subject": "A journey of a thousand miles begins with a single step",
        }

    def test_search_phrases__DocumentWithoutId__RaisesAssertionError(self):
        """
        test search phrases raises AssertionError when `id` is not defined in the document
        """
        # Given
        self.document.pop("id")
        # When
        with self.assertRaises(AssertionError) as e:
            search_phrases(self.document, "subject", ["jumping fox"])
        # Then
        self.assertEqual("Document to be searched must define a unique identifier field 'id'", str(e.exception))

    def test_search_phrases__SinglePhraseMatch__CorrectResult(self):
        """
        test search phrases result True when a single phrase matches the document
        """
        # Given-When
        result = search_phrases(self.document, "body", ["jumping fox"])
        # Then
        self.assertTrue(result)

    def test_search_phrases__SinglePhraseNoMatch__CorrectResult(self):
        """
        test search phrases result False when a single phrase does not match the document
        """
        # Given-When
        result = search_phrases(self.document, "body", ["jumping ox"])
        # Then
        self.assertFalse(result)

    def test_search_phrases__MultiplePhraseMatch__CorrectResult(self):
        """
        test search phrases result True when a multiple phrases match the document
        """
        # Given-When
        result = search_phrases(self.document, "subject", ["jumping ox", "journey begins", "miles"])
        # Then
        self.assertTrue(result)

    def test_search_phrases__MultiplePhraseNoMatch__CorrectResult(self):
        """
        test search phrases result False when a multiple phrases don't match the document
        """
        # Given-When
        result = search_phrases(self.document, "subject", ["jumping fox", "lazy dog"])
        # Then
        self.assertFalse(result)
