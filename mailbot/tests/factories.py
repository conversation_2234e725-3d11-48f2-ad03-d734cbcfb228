import factory
from django.utils import timezone
from django.conf import settings
from factory.django import DjangoModelFactory
from faker import Faker

from accounts.tests.factories import UserFactory
from mailbot.models import UserMailBotProfile, Message, SenderProfile
from mailbot.utils.defaults import GmailKnownLabelName
from mailbot.utils.message_parser import ParsedMessage

faker = Faker()


class UserMailBotProfileFactory(DjangoModelFactory):
    user = factory.SubFactory(UserFactory)
    label_mappings = factory.Dict({m.name: m.value for m in GmailKnownLabelName})
    service_provider = settings.SERVICE_PROVIDER_GOOGLE

    class Meta:
        model = UserMailBotProfile


class MessageFactory(DjangoModelFactory):
    user_mailbot_profile = factory.SubFactory(UserMailBotProfileFactory)
    message_id = factory.Faker("uuid4")
    thread_id = factory.Faker("uuid4")
    received_at = factory.LazyFunction(timezone.now)

    class Meta:
        model = Message


class SenderProfileFactory(DjangoModelFactory):
    user_mailbot_profile = factory.SubFactory(UserMailBotProfileFactory)
    sender_email = factory.Faker("email")
    sender_name = factory.Faker("name")
    read_count = factory.Faker("random_int", min=0, max=100)
    total_count = factory.Faker("random_int", min=0, max=100)
    oldest_timestamp = factory.LazyFunction(timezone.now)
    recent_timestamp = factory.LazyFunction(timezone.now)
    metadata = factory.Dict({})

    class Meta:
        model = SenderProfile


class ParsedMessageFactory(factory.Factory):
    """
    Factory for ParsedMessage objects.

    This factory provides default values for all required fields in the ParsedMessage class.
    Tests should explicitly provide values for any fields that are important to the test.
    """

    message_id = factory.LazyFunction(lambda: faker.hexify("^" * 16))
    thread_id = factory.SelfAttribute("message_id")
    user_email = factory.Faker("email")
    label_ids = factory.List([])
    body_preview = factory.Faker("paragraph")
    is_read = False
    is_sent = False
    received_at = factory.LazyFunction(timezone.now)
    message_headers = factory.Dict({})
    subject = factory.Faker("sentence")
    attachments = factory.List([])
    from_name_email = factory.LazyFunction(lambda: (faker.name(), faker.email()))
    to_name_email = factory.List([])
    cc_name_email = factory.List([])
    bcc_name_email = factory.List([])
    html_body = factory.LazyAttribute(lambda o: f'<div dir="ltr">{o.body_preview}</div>')
    text_body = factory.SelfAttribute("body_preview")
    unsubscribe_link = ""
    unsubscribe_link_mail_to = ""
    unsubscribe_link_mail_to_parsed = factory.LazyFunction(lambda: ("", "", ""))
    unsubscribe_link_one_click = False

    class Meta:
        model = ParsedMessage
