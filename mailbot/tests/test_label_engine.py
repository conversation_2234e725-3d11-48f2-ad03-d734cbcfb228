import base64
from copy import copy
from unittest import mock

from django.conf import settings
from django.test import TestCase
from django.utils import timezone
from faker import Factory

from accounts.tests.factories import UserFactory
from applications.models import ExceptionList
from applications.tests.factories import ApplicationFactory, ExceptionListFactory
from execfn import ApplicationTag
from mailbot.models import MailBotGenericLabel, SenderProfile, Message
from mailbot.service.factory import MessageServiceFactory
from mailbot.tests.factories import UserMailBotProfileFactory, SenderProfileFactory, MessageFactory
from mailbot.utils.core import MailBotMessageCore
from mailbot.utils.defaults import (
    MailBotMessageHeaders,
    MailBotMessageCategory,
    MailBotMessageLabelReason,
    FirstTimeSenderTreatment,
    MailBotProfileMetadataKey,
    MAILBOT_DEFAULT_PREFERENCES,
)
from mailbot.utils.message_parser import ParsedMessage
from nlp.models import LuceneFilter
from nlp.tests.factories import LuceneFilterFactory

faker = Factory.create()


@mock.patch("mailbot.service.gmail.GMailBox")
class LabelEngineTestCase(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.user = UserFactory()
        preferences = copy(MAILBOT_DEFAULT_PREFERENCES)
        preferences["mailbot_enabled"] = True
        cls.user_mailbot_profile = UserMailBotProfileFactory(
            user=cls.user,
            service_provider=settings.SERVICE_PROVIDER_GOOGLE,
            metadata={MailBotProfileMetadataKey.ALL_SCOPES_GRANTED.value: True},
            preferences=preferences,
        )
        cls.application = ApplicationFactory(tag=ApplicationTag.MailBot.value)
        # Keep prompt_template blank to avoid llm calls in test cases
        LuceneFilterFactory(tag=LuceneFilter.OTP, phrases=["otp, 4", "confirm your email", "verify your email"])
        LuceneFilterFactory(tag=LuceneFilter.PAYMENT_ALERT, phrases=["card, credited", "bank, credited", "card, spent"])
        LuceneFilterFactory(
            tag=LuceneFilter.PASSWORD, phrases=["password, reset", "password, change", "password, resetting"]
        )
        LuceneFilterFactory(
            tag=LuceneFilter.SECURITY, phrases=["new device, login", "sign in, new device", "unusual activity, account"]
        )
        LuceneFilterFactory(
            tag=LuceneFilter.ORDER_TICKET, phrases=["ticket, booking", "ticket, order id", "ticket, amount"]
        )
        LuceneFilterFactory(
            tag=LuceneFilter.BANK_STATEMENT, phrases=["bank, statement", "card, statement", "account, statement"]
        )

    def setUp(self):
        self.message_id = faker.hexify("^" * 16)
        # Subject and body of message
        self.subject = faker.sentence()
        self.text_body = faker.paragraph(nb_sentences=10, variable_nb_sentences=False)
        # Most common HTML alternative of Text email is just wrapping it in div
        self.html_body = f'<div dir="ltr">{self.text_body}</div>'
        # We receive message body of Text and Html in base64 encoded form
        self.encoded_text_body = base64.urlsafe_b64encode(self.text_body.encode()).decode()
        self.encoded_html_body = base64.urlsafe_b64encode(self.html_body.encode()).decode()
        # Name and email of the sender
        self.from_email = faker.email()
        self.from_name = faker.name()
        # Name and email of 1st blind carbon copy recipient
        self.bcc_name_email__0__0 = faker.name()
        self.bcc_name_email__0__1 = faker.email()
        # Name and email of 2nd blind carbon copy recipient
        self.bcc_name_email__1__0 = faker.name()
        self.bcc_name_email__1__1 = faker.email()
        # Bcc header that we receive in message payload is comma separated string
        self.bcc_name_email = f"{self.bcc_name_email__0__0} <{self.bcc_name_email__0__1}>, {self.bcc_name_email__1__0} <{self.bcc_name_email__1__1}>"
        self.sender_profile = SenderProfileFactory(
            normalized_sender_email=self.from_email,
            user_mailbot_profile=self.user_mailbot_profile,
            sender_email=self.from_email,
            read_count=0,
            total_count=1,
            sent_count=0,
            scanned_count=1,
        )
        self.parsed_message = ParsedMessage(
            message_id=self.message_id,
            thread_id=self.message_id,
            user_mailbot_profile_id=self.user_mailbot_profile.id,
            user_id=self.user.id,
            service_provider=settings.SERVICE_PROVIDER_GOOGLE,
            user_email=self.user.email,
            label_ids=[],
            body_preview=self.text_body,
            is_read=False,
            is_sent=False,
            received_at=timezone.now(),
            message_headers={},
            subject=self.subject,
            attachments=[],
            from_name_email=(self.from_name, self.from_email),
            to_name_email=[],
            cc_name_email=[],
            bcc_name_email=[],
            html_body=self.html_body,
            text_body=self.text_body,
            unsubscribe_link="",
            unsubscribe_link_mail_to="",
            unsubscribe_link_mail_to_parsed=("", "", ""),
            unsubscribe_link_one_click=False,
        )

    def test_WhenInternalMessage__AssignWhitelistLabel(self, *args):
        """
        Test that parsed internal message retains the whitelist label
        """
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.parsed_message.message_headers[
            MailBotMessageHeaders.MESSAGE_CATEGORY.value
        ] = MailBotMessageCategory.DIGEST.value

        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()

        # Then
        self.assertEqual(MailBotGenericLabel.WHITE_LIST.value, new_label_response["label_name"])
        self.assertEqual(
            MailBotMessageLabelReason.INTERNAL_MESSAGE_FILTER.value, new_label_response["message_labelled_due_to"]
        )

    def test_WhenUserDefinedArchiveAction__AssignArchive(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.sender_profile.user_action = SenderProfile.USER_ACTION_ARCHIVE
        self.sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_RULE_SELECTED
        self.sender_profile.save()
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()

        # Then
        self.assertEqual(Message.GENERIC_LABEL_ARCHIVE, new_label_response["label_name"])
        self.assertEqual(SenderProfile.USER_ACTION_REASON_RULE_SELECTED, new_label_response["message_labelled_due_to"])

    def test_WhenUserDefinedSpamAction__AssignSpam(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.sender_profile.user_action = SenderProfile.USER_ACTION_MARK_AS_SPAM
        self.sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_RULE_SELECTED
        self.sender_profile.save()
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()

        # Then
        self.assertEqual(Message.GENERIC_LABEL_SPAM, new_label_response["label_name"])
        self.assertEqual(SenderProfile.USER_ACTION_REASON_RULE_SELECTED, new_label_response["message_labelled_due_to"])

    def test_WhenUserDefinedReadAction__AssignRead(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.sender_profile.user_action = SenderProfile.USER_ACTION_MARK_AS_READ
        self.sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_RULE_SELECTED
        self.sender_profile.save()
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()

        # Then
        self.assertTrue(new_label_response["mark_as_read"])
        self.assertEqual(SenderProfile.USER_ACTION_REASON_RULE_SELECTED, new_label_response["message_labelled_due_to"])

    def test_WhenUserDefinedStarredAction__AssignStarred(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.sender_profile.user_action = SenderProfile.USER_ACTION_MARK_AS_STARRED
        self.sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_RULE_SELECTED
        self.sender_profile.save()
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()

        # Then
        self.assertTrue(new_label_response["mark_as_starred"])
        self.assertEqual(SenderProfile.USER_ACTION_REASON_RULE_SELECTED, new_label_response["message_labelled_due_to"])

    def test_WhenUserDefinedUnsubscribeAction__AssignTrashLabel(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.sender_profile.user_action = SenderProfile.USER_ACTION_MOVE_TO_TRASH
        self.sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_UNSUBSCRIBED
        self.sender_profile.save()
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()

        # Then
        self.assertEqual(Message.GENERIC_LABEL_TRASH, new_label_response["label_name"])
        self.assertEqual(SenderProfile.USER_ACTION_REASON_UNSUBSCRIBED, new_label_response["message_labelled_due_to"])

    def test_WhenUserDefinedDeletedActionAutoInbox__AssignWhiteListLabel(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.sender_profile.user_action = SenderProfile.USER_ACTION_MOVE_TO_INBOX
        self.sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_MARKED_DELETED
        self.sender_profile.save()
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()

        # Then
        self.assertEqual(MailBotGenericLabel.WHITE_LIST.value, new_label_response["label_name"])
        self.assertEqual(SenderProfile.USER_ACTION_REASON_MARKED_DELETED, new_label_response["message_labelled_due_to"])

    def test_WhenImportantEmailFromZappedSender__AssignWhiteListLabel(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.sender_profile.user_training = MailBotGenericLabel.ZAPPED.value
        self.sender_profile.save()
        self.parsed_message.subject = "[GitHub] Please verify your email address."
        self.parsed_message.text_body = (
            f"To secure your GitHub account, we just need to verify your email address: {self.user.email}"
        )
        self.parsed_message.html_body = f"<html><p>To secure your GitHub account, we just need to verify your email address: {self.user.email}</p></html>"
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()
        # Then
        self.assertEqual(MailBotGenericLabel.WHITE_LIST.value, new_label_response["label_name"])
        self.assertEqual(MailBotMessageLabelReason.LUCENE_FILTER.value, new_label_response["message_labelled_due_to"])
        self.assertListEqual([LuceneFilter.OTP], new_label_response["category_tags"])

    def test_WhenSenderTrainedToZapped__AssignZappedLabel(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.sender_profile.user_training = MailBotGenericLabel.ZAPPED.value
        self.sender_profile.save()
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()
        # Then
        self.assertEqual(MailBotGenericLabel.ZAPPED.value, new_label_response["label_name"])
        self.assertEqual(
            MailBotMessageLabelReason.USER_TRAINING_FILTER.value, new_label_response["message_labelled_due_to"]
        )

    def test_WhenSenderInSent__AssignWhiteList(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.parsed_message.from_name_email = ("John Doe", "<EMAIL>")
        self.sender_profile.sender_email = "<EMAIL>"
        self.sender_profile.normalized_sender_email = "<EMAIL>"
        self.sender_profile.sent_count = 1
        self.sender_profile.save()
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()
        # Then
        self.assertEqual(MailBotGenericLabel.WHITE_LIST.value, new_label_response["label_name"])
        self.assertEqual(
            MailBotMessageLabelReason.SENT_MESSAGE_FILTER.value, new_label_response["message_labelled_due_to"]
        )

    def test_WhenSenderInExceptionList__AssignExceptionLabel(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.parsed_message.from_name_email = ("Uber", "<EMAIL>")
        self.sender_profile.sender_email = "<EMAIL>"
        self.sender_profile.normalized_sender_email = "<EMAIL>"
        self.sender_profile.save()
        ExceptionListFactory(
            type=ExceptionList.TYPE_EMAIL,
            label_name=MailBotGenericLabel.WHITE_LIST.value,
            exception_address="<EMAIL>",
        )
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()
        # Then
        self.assertEqual(MailBotGenericLabel.WHITE_LIST.value, new_label_response["label_name"])
        self.assertEqual(MailBotMessageLabelReason.EXCEPTION_LIST.value, new_label_response["message_labelled_due_to"])

    def test_WhenSenderDomainInExceptionList__AssignExceptionLabel(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.parsed_message.from_name_email = ("Uber", "<EMAIL>")
        self.sender_profile.sender_email = "<EMAIL>"
        self.sender_profile.normalized_sender_email = "<EMAIL>"
        self.sender_profile.save()
        ExceptionListFactory(
            type=ExceptionList.TYPE_DOMAIN,
            label_name=MailBotGenericLabel.WHITE_LIST.value,
            exception_address="uber.com",
        )
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()
        # Then
        self.assertEqual(MailBotGenericLabel.WHITE_LIST.value, new_label_response["label_name"])
        self.assertEqual(MailBotMessageLabelReason.EXCEPTION_LIST.value, new_label_response["message_labelled_due_to"])

    def test_WhenSenderDomainExtensionInExceptionList__AssignExceptionLabel(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.parsed_message.from_name_email = ("Uber", "<EMAIL>")
        self.sender_profile.sender_email = "<EMAIL>"
        self.sender_profile.normalized_sender_email = "<EMAIL>"
        self.sender_profile.save()
        ExceptionListFactory(
            type=ExceptionList.TYPE_DOMAIN_EXTENSION,
            label_name=MailBotGenericLabel.WHITE_LIST.value,
            exception_address="com",
        )
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()
        # Then
        self.assertEqual(MailBotGenericLabel.WHITE_LIST.value, new_label_response["label_name"])
        self.assertEqual(MailBotMessageLabelReason.EXCEPTION_LIST.value, new_label_response["message_labelled_due_to"])

    def test_WhenFirstTimeSenderAndWhitelistPreference__AssignWhiteListLabel(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.user_mailbot_profile.preferences["first_time_sender_treatment"] = FirstTimeSenderTreatment.INBOX.value
        self.user_mailbot_profile.save()
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()
        # Then
        self.assertEqual(MailBotGenericLabel.WHITE_LIST.value, new_label_response["label_name"])
        self.assertEqual(
            MailBotMessageLabelReason.FIRST_TIME_SENDER_FILTER.value, new_label_response["message_labelled_due_to"]
        )

    def test_WhenFirstTimeSenderAndZappedPreference__AssignZappedLabel(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()
        # Then
        self.assertEqual(MailBotGenericLabel.ZAPPED.value, new_label_response["label_name"])
        self.assertEqual(
            MailBotMessageLabelReason.FIRST_TIME_SENDER_FILTER.value, new_label_response["message_labelled_due_to"]
        )

    def test_WhenReadFractionLow__AssignZappedLabel(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.sender_profile.scanned_count = 10
        self.sender_profile.total_count = 10
        self.sender_profile.read_count = 5
        self.sender_profile.save()
        # When
        new_label_response = MailBotMessageCore(
            service=service, parsed_message=self.parsed_message
        ).get_label_for_new_mail()
        # Then
        self.assertEqual(MailBotGenericLabel.ZAPPED.value, new_label_response["label_name"])
        self.assertEqual(
            MailBotMessageLabelReason.SENDER_PROFILE_FILTER.value, new_label_response["message_labelled_due_to"]
        )

    def test_NewThreadMessageWhenOriginalInZapped__GoesToZapped(self, *args):
        # Given
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.sender_profile.scanned_count = 2
        self.sender_profile.total_count = 2
        self.sender_profile.read_count = 1
        self.sender_profile.save()
        db_message =MessageFactory(
            message_id=self.parsed_message.message_id,
            thread_id=self.parsed_message.thread_id,
            user_mailbot_profile=self.user_mailbot_profile,
            generic_label=MailBotGenericLabel.ZAPPED.value,
            received_at=self.parsed_message.received_at,
        )
        new_message = copy(self.parsed_message)
        new_message.message_id = faker.hexify("^" * 16)
        new_message.thread_id = self.parsed_message.thread_id
        # When
        new_label_response = MailBotMessageCore(service=service, parsed_message=new_message, db_message=db_message).get_label_for_new_mail()
        # Then
        self.assertEqual(MailBotGenericLabel.ZAPPED.value, new_label_response["label_name"])
        self.assertEqual(
            MailBotMessageLabelReason.SAME_THREAD_FILTER.value, new_label_response["message_labelled_due_to"]
        )
