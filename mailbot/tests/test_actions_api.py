from unittest.mock import patch, MagicMock

from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from mailbot.models import (
    SenderProfile,
    UserActionLog,
    SenderUnsubscribeDetail,
)
from mailbot.utils.actions import (
    ActionProcessor,
    DeleteAction,
    UnsubscribeAction,
    ManageSenderAction,
    BaseAction,
)
from mailbot.utils.defaults import ActionTypes, AnalyticsStatisticsKey
from .factories import UserMailBotProfileFactory, SenderProfileFactory

User = get_user_model()


# Minimal concrete implementation of BaseAction for testing its concrete methods
class ConcreteTestAction(BaseAction):
    def execute(self, data) -> dict:
        pass  # Not used in BaseActionTests for set_future_action

    def bulk_execute(self, data) -> dict:
        pass  # Not used in BaseActionTests for set_future_action


class BaseAPITestCase(APITestCase):
    def setUp(self):
        self.client = APIClient()
        self.user_mailbot_profile = UserMailBotProfileFactory()
        self.user = self.user_mailbot_profile.user
        self.client.force_authenticate(user=self.user)
        self.url = reverse("mailbot:handle_actions")


class ActionRequestHandlerAPIViewTests(BaseAPITestCase):

    @patch("mailbot.api.views.ActionProcessor.process")
    def test_successful_action_request(self, mock_process):
        mock_process.return_value = {"status": "success"}
        payload = {
            "action_type": ActionTypes.DELETE.value,
            "details": {"sender_profile_ids": [1, 2]},
        }
        response = self.client.post(self.url, payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["message"], "Action executed successfully")
        self.assertEqual(response.data["result"], {"status": "success"})
        mock_process.assert_called_once_with(data=payload, user=self.user)

    def test_invalid_action_type_request(self):
        payload = {
            "action_type": "INVALID_ACTION",
            "details": {"sender_profile_ids": [1, 2]},
        }
        response = self.client.post(self.url, payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("action_type", response.data)

    def test_missing_details_request(self):
        payload = {"action_type": ActionTypes.DELETE.value}
        response = self.client.post(self.url, payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("details", response.data)

    @patch("mailbot.api.views.ActionProcessor.process")
    def test_action_processor_exception(self, mock_process):
        mock_process.side_effect = Exception("Test exception")
        payload = {
            "action_type": ActionTypes.DELETE.value,
            "details": {"sender_profile_ids": [1, 2]},
        }
        response = self.client.post(self.url, payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertEqual(
            response.data["error"],
            "An unexpected error occurred while processing your request.",
        )


class ActionProcessorTests(APITestCase):
    def setUp(self):
        self.user_mailbot_profile = UserMailBotProfileFactory()
        self.user = self.user_mailbot_profile.user
        self.processor = ActionProcessor()

    def test_load_action_valid_types(self):
        self.assertIsInstance(self.processor._load_action(ActionTypes.DELETE.value), DeleteAction)
        self.assertIsInstance(
            self.processor._load_action(ActionTypes.UNSUBSCRIBE.value),
            UnsubscribeAction,
        )
        self.assertIsInstance(
            self.processor._load_action(ActionTypes.MANAGE_SENDER.value),
            ManageSenderAction,
        )

    def test_load_action_invalid_type(self):
        with self.assertRaises(ValueError) as context:
            self.processor._load_action("INVALID_ACTION")
        self.assertEqual(str(context.exception), "Unsupported action_type: INVALID_ACTION")

    @patch.object(DeleteAction, "bulk_execute")
    def test_process_calls_bulk_execute_for_delete(self, mock_bulk_execute):
        mock_bulk_execute.return_value = {"status": "delete_success"}
        action_data = {
            "action_type": ActionTypes.DELETE.value,
            "details": {"sender_profile_ids": [1]},
        }
        result = self.processor.process(data=action_data, user=self.user)
        self.assertEqual(result, {"status": "delete_success"})
        mock_bulk_execute.assert_called_once_with(action_data.get("details"))

    @patch.object(UnsubscribeAction, "bulk_execute")
    def test_process_calls_bulk_execute_for_unsubscribe(self, mock_bulk_execute):
        mock_bulk_execute.return_value = {"status": "unsubscribe_success"}
        action_data = {
            "action_type": ActionTypes.UNSUBSCRIBE.value,
            "details": {"sender_profile_ids": [1]},
        }
        result = self.processor.process(data=action_data, user=self.user)
        self.assertEqual(result, {"status": "unsubscribe_success"})
        mock_bulk_execute.assert_called_once_with(action_data.get("details"))

    @patch.object(ManageSenderAction, "bulk_execute")
    def test_process_calls_bulk_execute_for_manage_sender(self, mock_bulk_execute):
        mock_bulk_execute.return_value = {"status": "manage_success"}
        action_data = {
            "action_type": ActionTypes.MANAGE_SENDER.value,
            "details": {"sender_profile_ids": [1], "user_action": "keep"},
        }
        result = self.processor.process(data=action_data, user=self.user)
        self.assertEqual(result, {"status": "manage_success"})
        mock_bulk_execute.assert_called_once_with(action_data.get("details"))

    @patch("mailbot.utils.actions.logger")
    def test_process_logs_info_and_errors(self, mock_logger):
        action_data = {
            "action_type": ActionTypes.DELETE.value,
            "details": {"sender_profile_ids": [1]},
        }
        # Mock the action class and its bulk_execute method
        with patch.object(DeleteAction, "bulk_execute") as mock_bulk_execute:
            mock_bulk_execute.return_value = {"status": "success"}
            self.processor.process(data=action_data, user=self.user)
            mock_logger.info.assert_any_call(f"Processing action: {ActionTypes.DELETE.value}, user: {self.user.email}")
            mock_logger.info.assert_any_call(f"Action {ActionTypes.DELETE.value} executed successfully.")

        # Test exception logging
        with patch.object(DeleteAction, "bulk_execute") as mock_bulk_execute_error:
            mock_bulk_execute_error.side_effect = ValueError("Test Error")
            with self.assertRaises(ValueError):
                self.processor.process(data=action_data, user=self.user)
            mock_logger.error.assert_called_once_with(
                f"Unexpected error during action {ActionTypes.DELETE.value}: Test Error",
                exc_info=True,
            )


class BaseActionTests(APITestCase):
    def setUp(self):
        self.user_mailbot_profile = UserMailBotProfileFactory()
        self.sender_profile = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile)
        self.action = ConcreteTestAction()  # Use the concrete test action

    def test_set_future_action_specific_action(self):
        user_action = "delete"
        user_action_reason = "test_reason"
        self.action.set_future_action(user_action, user_action_reason, self.sender_profile)
        self.sender_profile.refresh_from_db()
        self.assertEqual(self.sender_profile.user_action, user_action)
        self.assertTrue(
            UserActionLog.objects.filter(
                sender_profile=self.sender_profile,
                user_action=user_action,
                user_action_reason=user_action_reason,
            ).exists()
        )

    def test_set_future_action_algo_decide(self):
        user_action = "algo_decide"
        user_action_reason = "test_reason_algo"
        self.action.set_future_action(user_action, user_action_reason, self.sender_profile)
        self.sender_profile.refresh_from_db()
        self.assertIsNone(self.sender_profile.user_action)
        self.assertTrue(
            UserActionLog.objects.filter(
                sender_profile=self.sender_profile,
                user_action="algo_decide",
                user_action_reason=user_action_reason,
            ).exists()
        )


class DeleteActionTests(APITestCase):
    def setUp(self):
        self.user_mailbot_profile = UserMailBotProfileFactory()
        self.action = DeleteAction()

    @patch("mailbot.utils.actions.MessageServiceFactory")
    @patch("mailbot.utils.actions.update_statistics_in_analytics")
    def test_execute_delete_first_time(self, mock_update_stats, mock_message_service_factory):
        sender_profile = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile, metadata={})
        mock_message_service = MagicMock()
        mock_message_service_factory.get_message_service.return_value = mock_message_service
        mock_message_service.trash_historical_messages.return_value = 5

        result = self.action.execute(sender_profile)

        sender_profile.refresh_from_db()
        self.assertTrue(sender_profile.metadata.get("deleted"))
        self.assertEqual(
            sender_profile.user_action_reason,
            SenderProfile.USER_ACTION_REASON_MARKED_DELETED,
        )
        mock_message_service_factory.get_message_service.assert_called_once_with(sender_profile.user_mailbot_profile)
        mock_message_service.trash_historical_messages.assert_called_once_with(sender_profile.sender_email)
        mock_update_stats.assert_called_once_with(
            user_mailbot_profile=sender_profile.user_mailbot_profile,
            key=AnalyticsStatisticsKey.TRASHED_COUNT.value,
            value=5,
        )
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["message"], "Messages deleted successfully")
        self.assertEqual(result["trashed_count"], 5)

    def test_execute_delete_already_marked_deleted(self):
        sender_profile = SenderProfileFactory(
            user_mailbot_profile=self.user_mailbot_profile,
            metadata={"deleted": True},
        )
        result = self.action.execute(sender_profile)
        self.assertEqual(result["status"], "success")
        self.assertEqual(
            result["message"],
            "Future preference for delete action updated successfully",
        )

    @patch.object(DeleteAction, "execute")
    @patch.object(DeleteAction, "set_future_action")
    def test_bulk_execute_delete(self, mock_set_future_action, mock_execute):
        sender_profile1 = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile)
        sender_profile2 = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile)
        data = {
            "sender_profile_ids": [sender_profile1.id, sender_profile2.id],
            "user_action": "delete_later",
        }

        result = self.action.bulk_execute(data)

        self.assertEqual(mock_execute.call_count, 2)
        # Ensure execute is called with the correct SenderProfile instances
        mock_execute.assert_any_call(sender_profile1)
        mock_execute.assert_any_call(sender_profile2)

        self.assertEqual(mock_set_future_action.call_count, 2)
        mock_set_future_action.assert_any_call(
            "delete_later",
            SenderProfile.USER_ACTION_REASON_MARKED_DELETED,
            sender_profile1,
        )
        mock_set_future_action.assert_any_call(
            "delete_later",
            SenderProfile.USER_ACTION_REASON_MARKED_DELETED,
            sender_profile2,
        )
        self.assertEqual(result["overall_status"], "completed")

    @patch.object(DeleteAction, "execute")
    @patch.object(DeleteAction, "set_future_action")
    def test_bulk_execute_delete_no_future_action(self, mock_set_future_action, mock_execute):
        sender_profile1 = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile)
        data = {
            "sender_profile_ids": [sender_profile1.id],
            "user_action": None,  # No future action
        }
        self.action.bulk_execute(data)
        mock_execute.assert_called_once()
        mock_set_future_action.assert_not_called()


class UnsubscribeActionTests(APITestCase):
    def setUp(self):
        self.user_mailbot_profile = UserMailBotProfileFactory()
        self.action = UnsubscribeAction()

    @patch("mailbot.utils.actions.unsubscribe_sender.delay")
    @patch("mailbot.utils.actions.update_statistics_in_analytics")
    def test_execute_unsubscribe_not_yet_unsubscribed(self, mock_update_stats, mock_unsubscribe_task):
        sender_profile = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile)
        # Ensure no SenderUnsubscribeDetail exists or it's not unsubscribed
        SenderUnsubscribeDetail.objects.filter(sender_profile=sender_profile).delete()

        self.action.execute(sender_profile)

        sender_profile.refresh_from_db()
        self.assertEqual(
            sender_profile.user_action_reason,
            SenderProfile.USER_ACTION_REASON_UNSUBSCRIBED,
        )
        mock_unsubscribe_task.assert_called_once_with(sender_profile.id)
        mock_update_stats.assert_called_once_with(
            user_mailbot_profile=sender_profile.user_mailbot_profile,
            key=AnalyticsStatisticsKey.SENDERS_UNSUBSCRIBED.value,
            value=1,
        )

    @patch.object(UnsubscribeAction, "execute")
    @patch.object(UnsubscribeAction, "set_future_action")
    def test_bulk_execute_unsubscribe(self, mock_set_future_action, mock_execute):
        sender_profile1 = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile)
        sender_profile2 = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile)
        data = {
            "sender_profile_ids": [sender_profile1.id, sender_profile2.id],
            "user_action": "unsubscribe_later",
        }

        result = self.action.bulk_execute(data)

        self.assertEqual(mock_execute.call_count, 2)
        mock_execute.assert_any_call(sender_profile1)
        mock_execute.assert_any_call(sender_profile2)

        self.assertEqual(mock_set_future_action.call_count, 2)
        mock_set_future_action.assert_any_call(
            "unsubscribe_later",
            SenderProfile.USER_ACTION_REASON_UNSUBSCRIBED,
            sender_profile1,
        )
        mock_set_future_action.assert_any_call(
            "unsubscribe_later",
            SenderProfile.USER_ACTION_REASON_UNSUBSCRIBED,
            sender_profile2,
        )
        self.assertEqual(result["overall_status"], "completed")


class ManageSenderActionTests(APITestCase):
    def setUp(self):
        self.user_mailbot_profile = UserMailBotProfileFactory()
        self.action = ManageSenderAction()

    def test_execute_manage_sender(self):
        sender_profile = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile)
        try:
            self.action.execute({})
        except Exception as e:
            self.fail(f"ManageSenderAction.execute raised an exception: {e}")

    @patch.object(ManageSenderAction, "set_future_action")
    def test_bulk_execute_manage_sender(self, mock_set_future_action):
        sender_profile1 = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile)
        sender_profile2 = SenderProfileFactory(user_mailbot_profile=self.user_mailbot_profile)
        data = {
            "sender_profile_ids": [sender_profile1.id, sender_profile2.id],
            "user_action": "keep_in_inbox",
        }

        result = self.action.bulk_execute(data)

        self.assertEqual(mock_set_future_action.call_count, 2)
        mock_set_future_action.assert_any_call(
            "keep_in_inbox",
            SenderProfile.USER_ACTION_REASON_MANAGE_SENDER,
            sender_profile1,
        )
        mock_set_future_action.assert_any_call(
            "keep_in_inbox",
            SenderProfile.USER_ACTION_REASON_MANAGE_SENDER,
            sender_profile2,
        )

        sender_profile1.refresh_from_db()
        sender_profile2.refresh_from_db()
        self.assertEqual(
            sender_profile1.user_action_reason,
            SenderProfile.USER_ACTION_REASON_MANAGE_SENDER,
        )
        self.assertEqual(
            sender_profile2.user_action_reason,
            SenderProfile.USER_ACTION_REASON_MANAGE_SENDER,
        )
        self.assertEqual(result["overall_status"], "completed")
