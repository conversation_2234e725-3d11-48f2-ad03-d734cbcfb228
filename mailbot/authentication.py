import base64
import json
import logging
import urllib.parse

import google_auth_httplib2
import httplib2
import jwt
from django.conf import settings
from django.contrib.auth import get_user_model
from google.auth.exceptions import GoogleAuthError
from google.oauth2.id_token import verify_oauth2_token
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed

from execfn.settings import APP_ENV_LOCAL
from mailbot.models import UserMailBotProfile, MailSubscription
from mailbot.utils.jwt import jwt_decode

logger = logging.getLogger(__name__)
User = get_user_model()


class GmailWebhookAuthentication(BaseAuthentication):
    """
    Google's Gmail API webhook authentication.
    This authentication is used for receiving push notifications from Gmail API.
    It attaches the user and history_id to the request object if the authentication is successful.

    References:
        authenticate-push-subscriptions: https://cloud.google.com/pubsub/docs/authenticate-push-subscriptions#validate_tokens
    """

    def _verify_oauth2_token(self, id_token):
        """
        Verifies an ID Token issued by Google's OAuth 2.0 authorization server.
        """
        try:
            # `verify_oauth2_token` verifies the JWT signature, the `aud` claim, and the `exp` claim
            claim = verify_oauth2_token(
                id_token=id_token,
                request=google_auth_httplib2.Request(httplib2.Http()),
                audience="localhost" if settings.APP_ENV == APP_ENV_LOCAL else settings.BACKEND_DOMAIN,
            )
        except GoogleAuthError as exc:
            logger.exception(
                "Invalid issuer while verifying Google Oauth2 token for Pub/Sub push subscription",
                extra={"exception_details": exc},
            )
        except ValueError as value_error:
            logger.exception(
                "Token verification failed for Pub/Sub push subscription",
                extra={"exception_details": value_error},
            )
        else:
            return claim

    def authenticate(self, request):
        # Verify that the request originates from the application
        if request.query_params.get("token") != settings.PUBSUB_VERIFICATION_TOKEN:
            raise AuthenticationFailed("Received Pub/Sub request's query params does not have matching token")
        subscription = request.data.get("subscription")
        if subscription != settings.GOOGLE_PUSH_SERVER_SUBSCRIPTION_NAME:
            raise AuthenticationFailed("Received Pub/Sub push subscription is different from configured one")
        # Verify that the push request originates from Cloud Pub/Sub.
        # Get the Cloud Pub/Sub-generated JWT in the "Authorization" header.
        bearer_token = request.headers.get("Authorization")
        if not bearer_token:
            raise AuthenticationFailed("Received Pub/Sub push subscription has missing authorization header")
        id_token = bearer_token.split()
        if len(id_token) != 2:
            raise AuthenticationFailed("Received Pub/Sub push subscription has invalid authorization header format")
        claim = self._verify_oauth2_token(id_token[1])
        if not claim:
            raise AuthenticationFailed()
        is_valid_client_email = claim.get("email") == settings.GOOGLE_SERVICE_ACCOUNT.get("client_email") and claim.get(
            "email_verified"
        )
        if is_valid_client_email:
            message = request.data.get("message", {})
            data = message.get("data")
            data = json.loads(base64.urlsafe_b64decode(data))
            email = data.get("emailAddress")
            if not email:
                raise AuthenticationFailed("Email address missing from Pub/Sub push subscription's decoded data")
            else:
                try:
                    user = User.objects.get(email=email)
                except User.DoesNotExist:
                    raise AuthenticationFailed("User does not exist for Pub/Sub push subscription's email")
                else:
                    request.history_id = data.get("historyId")
                    return user, None
        else:
            logger.exception(
                "Invalid client email received for Pub/Sub push subscription",
                extra={"client_email": claim.get("email"), "email_verified": claim.get("email_verified")},
            )
            raise AuthenticationFailed()

    def authenticate_header(self, request):
        return "mail-subscription"


class AMPEmailEventAuthentication(BaseAuthentication):
    """
    AMP Email event authentication. This authentication is used for receiving AMP Email events.
    It attaches the user_mailbot_profile and payload to the request object if the authentication is successful.
    """

    def _jwt_decode(self, jwt_token):
        if jwt_token:
            try:
                payload = jwt_decode(jwt_token=jwt_token)
            except jwt.exceptions.DecodeError:
                raise AuthenticationFailed("Invalid Token. Please try again")
            except jwt.exceptions.ExpiredSignatureError:
                raise AuthenticationFailed(
                    "You cannot perform this operation on older emails. Try this action on a latest email"
                )
            else:
                return payload
        else:
            raise AuthenticationFailed("JWT token missing")

    def authenticate(self, request):
        amp_email_sender = request.headers.get("Amp-Email-Sender")
        origin = request.headers.get("Origin")
        if origin in settings.AMP_ALLOWED_ORIGINS and amp_email_sender == settings.SES_FROM_EMAIL:
            body = request.body
            if isinstance(body, bytes):
                body = body.decode()
            parsed_body = dict(urllib.parse.parse_qsl(body, strict_parsing=True, keep_blank_values=True))
            payload = self._jwt_decode(parsed_body.get("jwt_token"))
            user_mailbot_profile_id = payload.get("user_mailbot_profile_id")
            if not user_mailbot_profile_id:
                raise AuthenticationFailed(
                    "UserMailBotProfile ID must be passed in AMP Email tokens for authenticating user"
                )
            try:
                user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
            except UserMailBotProfile.DoesNotExist:
                raise AuthenticationFailed("UserMailBotProfile deleted for user before interacting with AMP email")
            else:
                request.user_mailbot_profile = user_mailbot_profile
                request.payload = payload
                request.parsed_body = parsed_body
                return user_mailbot_profile.user, None
        else:
            raise AuthenticationFailed("Invalid origin or AMP sender email")

    def authenticate_header(self, request):
        return "mail-token"


class HTMLEmailEventAuthentication(BaseAuthentication):
    """
    HTML Email event authentication. This authentication is used for receiving HTML Email events.
    It attaches the user_mailbot_profile and payload to the request object if the authentication is successful.
    """

    def authenticate(self, request):
        jwt_token = request.data.get("token")
        try:
            payload = jwt_decode(jwt_token=jwt_token)
        except jwt.exceptions.DecodeError:
            logger.info("Decode error while decoding HTML Email token")
            raise AuthenticationFailed("Invalid Token. Please try again.")
        except jwt.exceptions.ExpiredSignatureError:
            logger.info("Expired signature error while decoding HTML Email token")
            raise AuthenticationFailed(
                "You cannot perform this operation on older emails. Try this action on a latest email."
            )
        else:
            user_mailbot_profile_id = payload.get("user_mailbot_profile_id")
            if not user_mailbot_profile_id:
                raise AuthenticationFailed("Operation not allowed on this email. Please try again with a latest email.")
            try:
                user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
            except UserMailBotProfile.DoesNotExist:
                raise AuthenticationFailed("Your account is deleted with Emailzap.")
            else:
                request.payload = payload
                request.user_mailbot_profile = user_mailbot_profile
                return user_mailbot_profile.user, None

    def authenticate_header(self, request):
        return "mail-token"


class OutlookWebhookAuthentication(BaseAuthentication):
    """
    Microsoft's Outlook API webhook authentication.

    References:
        https://learn.microsoft.com/en-us/graph/change-notifications-delivery-webhooks
    """

    def authenticate(self, request):
        validation_token = request.query_params.get("validationToken")
        if validation_token:
            return None, None
        # We assume that only one user's data is returned in one request
        data = request.data.get("value", [])
        if not data:
            raise AuthenticationFailed("Received outlook webhook data is empty")
        client_state = data[0].get("clientState")
        mail_subscription_id = data[0].get("subscriptionId")
        # Check if the client state we set while subscribing matches
        if client_state != settings.MICROSOFT.get("client_state"):
            raise AuthenticationFailed("Client state does not match for outlook webhook data")
        if not mail_subscription_id:
            raise AuthenticationFailed("Subscription id is missing from outlook webhook data")
        try:
            mail_subscription = MailSubscription.objects.select_related("user_mailbot_profile__user").get(
                subscription_id=mail_subscription_id
            )
        except MailSubscription.DoesNotExist:
            raise AuthenticationFailed(f"Subscription does not exist for subscription ID {mail_subscription_id}")
        else:
            request.mail_subscription_id = mail_subscription_id
            return mail_subscription.user_mailbot_profile.user, None

    def authenticate_header(self, request):
        return "outlook-mail-subscription"
