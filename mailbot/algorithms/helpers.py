import logging
from typing import List
from mailbot.models import Message, MessageCategory
from django.contrib.postgres.aggregates import <PERSON><PERSON>y<PERSON><PERSON>
from constance import config as constance_config

logger = logging.getLogger(__name__)


def get_category_messages(sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]):
    cat_ids = sorted([category.id for category in message_categories])
    return (
        Message.objects.filter(sender_profile=sender_profile, user_mailbot_profile_id=mailbot_profile_id)
        .annotate(cat_ids=ArrayAgg("categories", ordering="categories__id"))
        .filter(cat_ids=cat_ids)
    )


def is_fts_treatment_disabled(user_preferences):
    treatment = user_preferences.get("first_time_sender_treatment", "low_priority_with_overlay")
    return treatment != "low_priority_with_overlay"


def should_show_fts_overlay(sender_profile, user_preferences):
    sent_count = sender_profile.metadata.get("fts_overlay_sent_count", 0)
    global_limit = constance_config.FIRST_TIME_SENDER_OVERLAY_THRESHOLD
    user_limit = user_preferences.get("first_time_sender_overlay_limit", "no-limit")
    try:
        if user_limit == "no-limit" or int(user_limit) > sent_count:
            return sent_count < global_limit
    except ValueError as e:
        logger.error(f"Error in first time sender filter: {e}")
    return False
