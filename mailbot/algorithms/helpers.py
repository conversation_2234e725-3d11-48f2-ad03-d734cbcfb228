import pytz
import logging
from datetime import datetime
from typing import List
from execfn.common.utils.datetime import get_utc_datetime
from mailbot.models import Message, MessageCategory, UserMailBotProfile
from django.contrib.postgres.aggregates import ArrayAgg
from constance import config as constance_config


from mailbot.utils.defaults import MailBotProfileMetadataKey

logger = logging.getLogger(__name__)


def get_category_messages(sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]):
    cat_ids = sorted([category.id for category in message_categories])
    return (
        Message.objects.filter(sender_profile=sender_profile, user_mailbot_profile_id=mailbot_profile_id)
        .annotate(cat_ids=ArrayAgg("categories", ordering="categories__id"))
        .filter(cat_ids=cat_ids)
    )


def get_onboarding_completed_at(user_mailbot_profile: UserMailBotProfile):
    onboarding_completed_at_key = MailBotProfileMetadataKey.ONBOARDING_COMPLETED_AT.value
    onboarding_completed_at_value = user_mailbot_profile.metadata.get(onboarding_completed_at_key)
    parsed_onboarding_date = None
    if onboarding_completed_at_value:
        try:
            if isinstance(onboarding_completed_at_value, datetime):
                parsed_onboarding_date = onboarding_completed_at_value
            elif isinstance(onboarding_completed_at_value, str):
                time_zone = pytz.timezone("UTC")
                parsed_onboarding_date = get_utc_datetime(onboarding_completed_at_value, time_zone)
        except (ValueError, TypeError) as e:
            logger.error(
                f"Error parsing onboarding_completed_at ('{onboarding_completed_at_value}') "
                f"for UserMailBotProfile ID {user_mailbot_profile.id}: {e}"
            )
    return parsed_onboarding_date


def is_onboarding_invalid(sender_profile) -> bool:
    user_mailbot_profile = sender_profile.user_mailbot_profile
    onboarding_completed_at = get_onboarding_completed_at(user_mailbot_profile)
    is_invalid = not onboarding_completed_at or sender_profile.created <= onboarding_completed_at
    if is_invalid:
        logger.info(
            f"first_time_sender_filter: returning false, invalid onboarding or early profile creation "
            f"(created: {sender_profile.created}, onboarding: {onboarding_completed_at})"
        )
    return is_invalid


def is_fts_treatment_disabled(user_preferences):
    treatment = user_preferences.get("first_time_sender_treatment", "low_priority_with_overlay")
    return treatment != "low_priority_with_overlay"


def should_show_fts_overlay(sender_profile, user_preferences):
    sent_count = sender_profile.metadata.get("fts_overlay_sent_count", 0)
    global_limit = constance_config.FIRST_TIME_SENDER_OVERLAY_THRESHOLD
    user_limit = user_preferences.get("first_time_sender_overlay_limit", "no-limit")
    try:
        if user_limit == "no-limit" or int(user_limit) > sent_count:
            return sent_count < global_limit
    except ValueError as e:
        logger.error(f"Error in first time sender filter: {e}")
    return False
