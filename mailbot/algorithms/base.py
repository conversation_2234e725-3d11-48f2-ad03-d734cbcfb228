from abc import ABC
from typing import List, Optional, Tuple
from mailbot.models import (
    AlgorithmCategoryScore,
    AlgorithmDefinition,
    MailBotGenericLabel,
    MessageCategory,
    SenderProfile,
    UserMailBotProfile,
)


class BaseAlgorithm(ABC):
    def __init__(self, algo_def: AlgorithmDefinition):
        self.algo_def = algo_def

    @property
    def version(self) -> int:
        return self.algo_def.version

    def _has_important_categories(self, message_categories: List[MessageCategory]) -> bool:
        return self._get_total_category_score(message_categories) >= 2

    def _get_total_category_score(self, message_categories: List[MessageCategory]) -> int:
        cat_ids = [cat.id for cat in message_categories]
        scores = AlgorithmCategoryScore.objects.filter(algorithmDefinition=self.algo_def, category_id__in=cat_ids)
        score_map = {s.category_id: s.score for s in scores}
        return sum(score_map.get(cat.id, cat.score) for cat in message_categories)

    def get_total_message_counts(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> int:
        return 0

    def should_send_fts_overlay(self, sender_profile: SenderProfile) -> bool:
        return False

    def should_show_critical_alert_overlay(self, message_categories: List["MessageCategory"]) -> bool:
        return False

    def default_action_label(
        self, user_mailbot_profile: UserMailBotProfile, message_categories: List[MessageCategory]
    ) -> bool:
        return MailBotGenericLabel.ZAPPED

    def should_whitelist_read_fraction_action(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> Tuple[bool, Optional[bool]]:
        return False, False

    def get_open_rate(self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]) -> float:
        return 0
