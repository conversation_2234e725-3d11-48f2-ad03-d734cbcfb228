import logging
from typing import Optional, Type
from mailbot.algorithms.base import BaseAlgorithm
from mailbot.algorithms.constants import ALGORITHM_MAP, DEFAULT_ALGORITHM_VERSION
from mailbot.models import AlgorithmDefinition, UserMailBotProfile

logger = logging.getLogger(__name__)


def get_algorithm_klass(user_profile: UserMailBotProfile) -> Type[BaseAlgorithm]:
    algo_def_to_use = user_profile.algorithm_definition
    algorithm_klass: Optional[Type[BaseAlgorithm]] = None

    if algo_def_to_use and algo_def_to_use.is_active and algo_def_to_use.version in ALGORITHM_MAP:
        algorithm_klass = ALGORITHM_MAP[algo_def_to_use.version]
    else:
        warning_message = f"Algorithm version '{algo_def_to_use.version if algo_def_to_use else 'None'}' "
        warning_message += "is inactive." if algo_def_to_use and not algo_def_to_use.is_active else "not found."
        logger.warning(f"Warning: Falling back to default algorithm: {DEFAULT_ALGORITHM_VERSION} {warning_message}")
        algorithm_klass = ALGORITHM_MAP[DEFAULT_ALGORITHM_VERSION]
        algo_def_to_use = get_default_active_algorithm()
    return algorithm_klass


def get_default_active_algorithm() -> AlgorithmDefinition:
    try:
        return AlgorithmDefinition.objects.get(version=DEFAULT_ALGORITHM_VERSION, is_active=True)
    except AlgorithmDefinition.DoesNotExist:
        logger.warning(f"Warning: Default active algorithm '{DEFAULT_ALGORITHM_VERSION}' not found.")
        raise ValueError(f"Default active algorithm definition '{DEFAULT_ALGORITHM_VERSION}' not found.")
