from typing import List, Optional, Tuple
from mailbot.algorithms.helpers import (
    get_category_messages,
    is_fts_treatment_disabled,
    is_onboarding_invalid,
    should_show_fts_overlay,
)
from mailbot.models import MailBotGenericLabel, MessageCategory, SenderProfile, UserMailBotProfile
from mailbot.algorithms.base import BaseAlgorithm
from django.db.models import Count, Q


class V3Algorithm(BaseAlgorithm):
    def should_send_fts_overlay(self, sender_profile: SenderProfile = None) -> bool:
        if sender_profile:
            user_preferences = sender_profile.user_mailbot_profile.preferences
            if is_onboarding_invalid(sender_profile):
                return False
            elif is_fts_treatment_disabled(user_preferences):
                return False
            elif should_show_fts_overlay(sender_profile, user_preferences):
                return True
            else:
                return False
        return True

    def should_show_critical_alert_overlay(self, message_categories: List[MessageCategory]) -> bool:
        return any(category.name == "Critical Account/Security Alert" for category in message_categories)

    def default_action_label(
        self, user_mailbot_profile: UserMailBotProfile, message_categories: List[MessageCategory]
    ) -> MailBotGenericLabel:
        return (
            MailBotGenericLabel.WHITE_LIST
            if self._has_important_categories(message_categories)
            else MailBotGenericLabel.ZAPPED
        )

    def should_whitelist_read_fraction_action(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> Tuple[bool, Optional[bool]]:
        open_rate = self.get_open_rate(sender_profile, mailbot_profile_id, message_categories)
        score = self._get_total_category_score(message_categories)
        should_whitelist = score >= 4 or (score >= 2 and open_rate >= 0.5)
        include_in_digest = open_rate >= 0.5 and not should_whitelist
        return should_whitelist, include_in_digest

    def get_total_message_counts(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> int:
        return get_category_messages(sender_profile, mailbot_profile_id, message_categories).distinct().count()

    def get_open_rate(self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]) -> float:
        counts = get_category_messages(sender_profile, mailbot_profile_id, message_categories).aggregate(
            total_count=Count("id"), total_read=Count("id", filter=Q(is_read=True))
        )
        return counts["total_read"] / counts["total_count"] if counts["total_count"] > 0 else 0
