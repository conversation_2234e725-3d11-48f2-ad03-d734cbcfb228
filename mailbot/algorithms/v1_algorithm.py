from typing import List
from mailbot.algorithms.base import BaseAlgorithm
from mailbot.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserMailBotProfile, MailBotGenericLabel


class V1Algorithm(BaseAlgorithm):
    def default_action_label(
        self, user_mailbot_profile: UserMailBotProfile, message_categories: List[MessageCategory]
    ) -> MailBotGenericLabel:
        first_time_sender_treatment = user_mailbot_profile.preferences["first_time_sender_treatment"]
        return MailBotGenericLabel.WHITE_LIST if first_time_sender_treatment == "inbox" else MailBotGenericLabel.ZAPPED

    def should_show_critical_alert_overlay(self, message_categories: List[MessageCategory]) -> bool:
        return True

    def should_whitelist_read_fraction_action(self, sender_profile, mailbot_profile_id, message_categories):
        open_rate = self.get_open_rate(sender_profile, mailbot_profile_id, message_categories)
        should_whitelist = open_rate >= 0.9
        include_in_digest = open_rate >= 0.3 and not should_whitelist
        return should_whitelist, include_in_digest

    def get_total_message_counts(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> int:
        return sender_profile.total_count

    def get_open_rate(self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]) -> float:
        ## To not count the current message and total_count will always be > 1
        return sender_profile.read_count / (sender_profile.total_count - 1)
