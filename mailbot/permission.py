from rest_framework.permissions import BasePermission
from rest_framework.exceptions import ValidationError
from applications.models import Application
from execfn import ApplicationTag
from mailbot.models import SecondaryMailBotProfilesThrough
from mailbot.utils.check import is_associated_mailbot_profile
from mailbot.utils.defaults import MailBotProfileMetadataKey


class IsPrimaryProfile(BasePermission):
    def has_permission(self, request, view):
        user_mailbot_profile = request.user.user_mailbot_profile
        is_secondary_profile = SecondaryMailBotProfilesThrough.objects.filter(
            secondary_mailbot_profile_id=user_mailbot_profile.id
        ).exists()
        if is_secondary_profile:
            return False
        else:
            return True


class LinkedProfilesLimitNotExceeded(BasePermission):
    def has_permission(self, request, view):
        user_mailbot_profile = request.user.user_mailbot_profile
        # count of secondary profiles + primary profile
        linked_profile_count = (
            SecondaryMailBotProfilesThrough.objects.filter(primary_mailbot_profile=user_mailbot_profile).count() + 1
        )
        linked_profile_max_count = Application.objects.get(tag=ApplicationTag.MailBot.value).linked_accounts_max_count
        if linked_profile_count >= linked_profile_max_count:
            return False
        else:
            return True


class IsAssociatedProfileUser(BasePermission):
    """
    Allows access only if the 'mailbot_profile_id_to_update' provided in the request data
    corresponds to the requesting user's own mailbot profile ID or the ID of any
    mailbot profile associated with them (either primary or secondary).

    This ensures that the user can only perform the action on profiles they
    are linked to.
    """

    def has_permission(self, request, view):
        mailbot_profile_id_to_update = request.data.get("mailbot_profile_id_to_update")
        return is_associated_mailbot_profile(request.user.user_mailbot_profile.id, mailbot_profile_id_to_update)


class IsAssociatedMailbotProfile(BasePermission):
    """
    Custom permission to only allow access if all requested mailbot_profile_ids
    are associated with the current user.
    Executes if the query param 'mailbot_profile_ids' is provided.
    """

    message = "You do not have permission to access data for one or more of the requested mailbot profiles."

    def has_permission(self, request, view):
        requested_profile_ids_str = request.query_params.getlist("mailbot_profile_ids", [])

        if not requested_profile_ids_str:
            return True

        try:
            requested_profile_ids = set(map(int, requested_profile_ids_str))
        except ValueError:
            raise ValidationError("Invalid mailbot_profile_ids provided.")

        current_user_mailbot_profile = request.user.user_mailbot_profile

        allowed_profile_ids = {current_user_mailbot_profile.id}
        associated_ids = SecondaryMailBotProfilesThrough.get_associated_mailbot_profile_ids(
            user_mailbot_profile_id=current_user_mailbot_profile.id
        )
        allowed_profile_ids.update(associated_ids)

        if not requested_profile_ids.issubset(allowed_profile_ids):
            return False

        return True


class HasAllScopesPermission(BasePermission):
    """
    Custom permission to only allow access if all scopes are granted.
    """

    def has_permission(self, request, view):
        # TODO: Implement a more robust method to verify if all required scopes are present
        return request.user.user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.ALL_SCOPES_GRANTED.value, False)
