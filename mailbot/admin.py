from django.conf import settings
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

from mailbot.models import UserMailBotProfile, MailBotLoginLink, MessageCategory, SecondaryMailBotProfilesThrough
from mailbot.utils.base import activate_mailbot as activate_mailbot_util, deactivate_mailbot as deactivate_mailbot_util


class MailbotEnabledListFilter(admin.SimpleListFilter):
    title = _("mailbot enabled")

    parameter_name = "mailbot_enabled"

    def lookups(self, request, model_admin):
        """
        Returns a list of tuples. The first element in each
        tuple is the coded value for the option that will
        appear in the URL query. The second element is the
        human-readable name for the option that will appear
        in the right sidebar.
        """
        return [
            (True, _("Yes")),
            (False, _("No")),
        ]

    def queryset(self, request, queryset):
        """
        Returns the filtered queryset based on the value
        provided in the query string and retrievable via
        `self.value()`.
        """
        if self.value() is not None:
            mailbot_enabled_filter = True if self.value() == "True" else False
            return queryset.filter(preferences__mailbot_enabled=mailbot_enabled_filter)


@admin.action(description="Activate MailBot")
def activate_mailbot(modeladmin, request, queryset):
    for user_mailbot_profile in queryset:
        if user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_GOOGLE:
            activate_mailbot_util(user_mailbot_profile)


@admin.action(description="Deactivate MailBot")
def deactivate_mailbot(modeladmin, request, queryset):
    for user_mailbot_profile in queryset:
        if user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_GOOGLE:
            deactivate_mailbot_util(user_mailbot_profile)


@admin.register(UserMailBotProfile)
class UserMailBotProfileAdmin(admin.ModelAdmin):
    list_filter = ["service_provider", MailbotEnabledListFilter]
    search_fields = ["user__email"]
    actions = [activate_mailbot, deactivate_mailbot]

    def get_queryset(self, request):
        return UserMailBotProfile.all_objects.all()


@admin.register(MailBotLoginLink)
class MailBotLoginLinkAdmin(admin.ModelAdmin):
    search_fields = ("user__email",)
    readonly_fields = ("link", "times_redeemed")
    exclude = ("code", "metadata")

    def link(self, obj):
        return obj.link

    def get_readonly_fields(self, request, obj):
        if obj:  # This means the object is being edited
            return self.readonly_fields + ("subscription_expires_at",)
        return self.readonly_fields


@admin.register(MessageCategory)
class MessageCategoryAdmin(admin.ModelAdmin):
    pass


@admin.register(SecondaryMailBotProfilesThrough)
class SecondaryMailBotProfilesThroughAdmin(admin.ModelAdmin):
    list_display = ["id", "primary_profile_email", "secondary_profile_email", "created"]
    search_fields = ["primary_mailbot_profile__user__email", "secondary_mailbot_profile__user__email"]

    def primary_profile_email(self, obj):
        return obj.primary_mailbot_profile.user.email

    def secondary_profile_email(self, obj):
        return obj.secondary_mailbot_profile.user.email

    primary_profile_email.short_description = "Primary Profile"
    secondary_profile_email.short_description = "Secondary Profile"
