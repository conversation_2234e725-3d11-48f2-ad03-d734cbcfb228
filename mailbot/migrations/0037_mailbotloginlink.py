# Generated by Django 4.2.5 on 2024-11-14 06:22

from django.db import migrations, models
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0036_senderprofile_user_training_reason"),
    ]

    operations = [
        migrations.CreateModel(
            name="MailBotLoginLink",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                (
                    "user_email",
                    models.EmailField(
                        blank=True,
                        help_text="User's email who can use this login link to signup. Default to blank for anyone to use.",
                        max_length=254,
                        null=True,
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Datetime at which login link should be expired. Default to blank for no expiry.",
                        null=True,
                    ),
                ),
                ("code", models.<PERSON>r<PERSON>ield(max_length=64, unique=True)),
                (
                    "max_redemptions",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Maximum number of unique users that can signup using the link. Default to blank for unlimited.",
                        null=True,
                    ),
                ),
                (
                    "times_redeemed",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of unique users used this link to signup."
                    ),
                ),
                (
                    "subscription_expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Datetime at which subscription should end. Default to blank for forever.",
                        null=True,
                    ),
                ),
                ("metadata", models.JSONField(default=dict)),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
    ]
