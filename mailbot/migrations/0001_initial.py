# Generated by Django 4.2.5 on 2023-11-03 07:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UserMailBotProfile",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                (
                    "service_provider",
                    models.CharField(
                        choices=[("google", "Google"), ("microsoft", "Microsoft")], db_index=True, max_length=16
                    ),
                ),
                ("preferences", models.J<PERSON><PERSON>ield(default=dict)),
                ("granted_authentication", models.JSONField(default=dict)),
                ("label_mappings", models.JSONField(default=dict)),
                ("watch_event_subscription_id", models.CharField(null=True, unique=True)),
                ("user", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name="SenderProfile",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("sender_email", models.EmailField(max_length=254)),
                ("sender_name", models.CharField(max_length=128, null=True)),
                ("read_count", models.IntegerField()),
                ("total_count", models.IntegerField()),
                ("oldest_timestamp", models.DateTimeField()),
                ("recent_timestamp", models.DateTimeField()),
                (
                    "user_mailbot_profile",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.usermailbotprofile"),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Message",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("message_id", models.CharField(max_length=256, unique=True)),
                ("thread_id", models.CharField(max_length=256)),
                ("received_at", models.DateTimeField()),
                ("metadata", models.JSONField(default=dict)),
                (
                    "sender_profile",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.senderprofile"),
                ),
                (
                    "user_mailbot_profile",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.usermailbotprofile"),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="MailBotServiceWebhookLog",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("webhook_log", models.JSONField(default=list)),
                ("message", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.message")),
                (
                    "user_mailbot_profile",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.usermailbotprofile"),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
        migrations.AddConstraint(
            model_name="usermailbotprofile",
            constraint=models.UniqueConstraint(
                fields=("user", "service_provider"), name="user_service_provider_unique_constraint"
            ),
        ),
    ]
