# Generated by Django 4.2.5 on 2023-11-16 04:56

from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0003_usermailbotprofile_watch_event_expire_at"),
    ]

    operations = [
        migrations.CreateModel(
            name="MailSubscription",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("subscription_id", models.CharField(db_index=True, null=True, unique=True)),
                ("label_name", models.Char<PERSON>ield(max_length=64)),
                ("expire_at", models.DateTime<PERSON>ield(null=True)),
            ],
        ),
        migrations.RemoveField(
            model_name="mailbotservicewebhooklog",
            name="message",
        ),
        migrations.RemoveField(
            model_name="mailbotservicewebhooklog",
            name="user_mailbot_profile",
        ),
        migrations.RemoveConstraint(
            model_name="senderprofile",
            name="user_mailbot_profile__sender_email__unique_constraint",
        ),
        migrations.RemoveField(
            model_name="usermailbotprofile",
            name="watch_event_expire_at",
        ),
        migrations.RemoveField(
            model_name="usermailbotprofile",
            name="watch_event_subscription_id",
        ),
        migrations.AddField(
            model_name="senderprofile",
            name="user_training",
            field=models.CharField(blank=True, default="", max_length=128),
        ),
        migrations.AddConstraint(
            model_name="senderprofile",
            constraint=models.UniqueConstraint(
                fields=("user_mailbot_profile", "sender_email"), name="sender_profile_unique_constraint"
            ),
        ),
        migrations.DeleteModel(
            name="MailBotServiceWebhookLog",
        ),
        migrations.AddField(
            model_name="mailsubscription",
            name="user_mailbot_profile",
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.usermailbotprofile"),
        ),
        migrations.AddConstraint(
            model_name="mailsubscription",
            constraint=models.UniqueConstraint(
                fields=("user_mailbot_profile", "label_name"), name="message_webhook_unique_constraint"
            ),
        ),
    ]
