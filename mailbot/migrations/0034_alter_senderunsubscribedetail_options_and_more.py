# Generated by Django 4.2.5 on 2024-10-23 15:06

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0033_senderprofile_scanned_count"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="senderunsubscribedetail",
            options={},
        ),
        migrations.AlterField(
            model_name="domaintraining",
            name="label_name",
            field=models.CharField(
                blank=True, choices=[("white_list", "White List"), ("zapped", "Zapped")], max_length=32, null=True
            ),
        ),
        migrations.AlterField(
            model_name="mailsubscription",
            name="label_name",
            field=models.CharField(choices=[("white_list", "White List"), ("zapped", "Zapped")], max_length=32),
        ),
        migrations.AlterField(
            model_name="message",
            name="generic_label",
            field=models.Char<PERSON>ield(
                choices=[
                    ("white_list", "White List"),
                    ("zapped", "Zapped"),
                    ("sent", "Sent"),
                    ("archive", "Archive"),
                    ("trash", "Trash"),
                    ("spam", "Spam"),
                ],
                db_index=True,
                max_length=256,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="senderprofile",
            name="user_training",
            field=models.CharField(
                choices=[("white_list", "White List"), ("zapped", "Zapped")], max_length=32, null=True
            ),
        ),
        migrations.AlterField(
            model_name="senderunsubscribedetail",
            name="message_id",
            field=models.CharField(max_length=256, unique=True),
        ),
        migrations.AlterField(
            model_name="senderunsubscribedetail",
            name="sender_profile",
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.senderprofile"),
        ),
    ]
