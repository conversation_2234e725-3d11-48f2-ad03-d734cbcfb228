# Generated by Django 4.2.5 on 2024-11-06 11:09

from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0034_alter_senderunsubscribedetail_options_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="MailBotUsageLog",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("subscription_period_start", models.DateTimeField()),
                ("metadata", models.JSONField(default=dict)),
            ],
        ),
        migrations.AddConstraint(
            model_name="senderunsubscribedetail",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("unsubscribe_link__isnull", False), ("unsubscribe_mail_to__isnull", False), _connector="OR"
                ),
                name="unsubscribe_link_or_mail_to",
            ),
        ),
        migrations.AddField(
            model_name="mailbotusagelog",
            name="user_mailbot_profile",
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.usermailbotprofile"),
        ),
        migrations.AddConstraint(
            model_name="mailbotusagelog",
            constraint=models.UniqueConstraint(
                fields=("user_mailbot_profile", "subscription_period_start"),
                name="mailbot_usage_log_unique_constraint",
            ),
        ),
    ]
