# Generated by Django 4.2.5 on 2024-10-11 10:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0030_alter_usermailbotprofile_user_picture"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="senderunsubscribedetail",
            options={"get_latest_by": "modified"},
        ),
        migrations.RemoveConstraint(
            model_name="senderunsubscribedetail",
            name="sender_unsubscribe_detail_unique_constraint",
        ),
        migrations.RemoveField(
            model_name="senderprofile",
            name="unsubscribed",
        ),
        migrations.RemoveField(
            model_name="senderunsubscribedetail",
            name="sender_profiles",
        ),
        migrations.AddField(
            model_name="senderprofile",
            name="user_action",
            field=models.CharField(
                choices=[
                    ("mark_as_spam", "Mark as Spam"),
                    ("mark_as_read", "Mark as Read"),
                    ("mark_as_starred", "Mark as Starred"),
                    ("archive", "Archive"),
                    ("move_to_trash", "Move to Trash"),
                    ("move_to_inbox", "Move to Inbox"),
                ],
                max_length=32,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="senderprofile",
            name="user_action_reason",
            field=models.CharField(
                choices=[
                    ("unsubscribed", "Unsubscribed"),
                    ("marked_deleted", "Marked Deleted"),
                    ("rule_selected", "Rule Selected"),
                ],
                max_length=32,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="senderunsubscribedetail",
            name="sender_profile",
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to="mailbot.senderprofile"),
        ),
    ]
