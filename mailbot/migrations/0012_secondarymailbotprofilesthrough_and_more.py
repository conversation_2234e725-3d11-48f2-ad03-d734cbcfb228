# Generated by Django 4.2.5 on 2024-01-22 08:09

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("mailbot", "0011_senderprofile_normalized_sender_email"),
    ]

    operations = [
        migrations.CreateModel(
            name="SecondaryMailBotProfilesThrough",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
            ],
        ),
        migrations.AlterModelOptions(
            name="usermailbotprofile",
            options={"get_latest_by": "modified"},
        ),
        migrations.RemoveConstraint(
            model_name="usermailbotprofile",
            name="user_service_provider_unique_constraint",
        ),
        migrations.AlterField(
            model_name="usermailbotprofile",
            name="user",
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name="secondarymailbotprofilesthrough",
            name="primary_mailbot_profile",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="secondary_mailbot_profiles_through",
                to="mailbot.usermailbotprofile",
            ),
        ),
        migrations.AddField(
            model_name="secondarymailbotprofilesthrough",
            name="secondary_mailbot_profile",
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to="mailbot.usermailbotprofile"),
        ),
        migrations.AddField(
            model_name="usermailbotprofile",
            name="secondary_mailbot_profiles",
            field=models.ManyToManyField(
                through="mailbot.SecondaryMailBotProfilesThrough", to="mailbot.usermailbotprofile"
            ),
        ),
        migrations.AddConstraint(
            model_name="secondarymailbotprofilesthrough",
            constraint=models.CheckConstraint(
                check=models.Q(("primary_mailbot_profile", models.F("secondary_mailbot_profile")), _negated=True),
                name="profile_cannot_be_both_primary_and_secondary_constraint",
            ),
        ),
    ]
