# Generated by Django 4.2.5 on 2023-12-05 01:47

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0007_senderprofile_metadata"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="domaintraining",
            name="label_name",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[("white_list", "White List"), ("grey_list", "Grey List"), ("black_list", "Black List")],
                max_length=32,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="mailsubscription",
            name="label_name",
            field=models.Char<PERSON>ield(
                choices=[("white_list", "White List"), ("grey_list", "Grey List"), ("black_list", "Black List")],
                max_length=32,
            ),
        ),
        migrations.AlterField(
            model_name="senderprofile",
            name="user_training",
            field=models.CharField(
                blank=True,
                choices=[("white_list", "White List"), ("grey_list", "Grey List"), ("black_list", "Black List")],
                max_length=32,
                null=True,
            ),
        ),
    ]
