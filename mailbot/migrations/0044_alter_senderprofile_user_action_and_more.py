# Generated by Django 4.2.5 on 2025-05-20 13:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mailbot', '0043_alter_senderprofile_user_action_reason_useractionlog'),
    ]

    operations = [
        migrations.AlterField(
            model_name='senderprofile',
            name='user_action',
            field=models.CharField(choices=[('mark_as_spam', 'Mark as Spam'), ('mark_as_read', 'Mark as Read'), ('mark_as_starred', 'Mark as Starred'), ('archive', 'Archive'), ('move_to_trash', 'Move to Trash'), ('move_to_inbox', 'Move to Inbox'), ('move_to_zapped', 'Move to Zapped'), ('auto_delete', 'Auto Delete')], max_length=32, null=True),
        ),
        migrations.AlterField(
            model_name='senderprofile',
            name='user_action_reason',
            field=models.Char<PERSON>ield(choices=[('unsubscribed', 'Unsubscribed'), ('marked_deleted', 'Marked Deleted'), ('rule_selected', 'Rule Selected'), ('manage_sender', 'Manage Sender')], max_length=32, null=True),
        ),
        migrations.AlterField(
            model_name='useractionlog',
            name='user_action',
            field=models.CharField(choices=[('mark_as_spam', 'Mark as Spam'), ('mark_as_read', 'Mark as Read'), ('mark_as_starred', 'Mark as Starred'), ('archive', 'Archive'), ('move_to_trash', 'Move to Trash'), ('move_to_inbox', 'Move to Inbox'), ('move_to_zapped', 'Move to Zapped'), ('auto_delete', 'Auto Delete')], max_length=32),
        ),
        migrations.AlterField(
            model_name='useractionlog',
            name='user_action_reason',
            field=models.CharField(choices=[('unsubscribed', 'Unsubscribed'), ('marked_deleted', 'Marked Deleted'), ('rule_selected', 'Rule Selected'), ('manage_sender', 'Manage Sender')], max_length=32),
        ),
    ]
