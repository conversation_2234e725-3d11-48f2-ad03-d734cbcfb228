# Generated by Django 4.2.5 on 2025-04-21 06:46

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0039_message_is_read_message_sender_profile_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="domaintraining",
            name="label_name",
            field=models.CharField(
                blank=True, choices=[("white_list", "White List"), ("zapped", "Zapped")], max_length=32, null=True
            ),
        ),
        migrations.AlterField(
            model_name="mailsubscription",
            name="label_name",
            field=models.CharField(choices=[("white_list", "White List"), ("zapped", "Zapped")], max_length=32),
        ),
        migrations.AlterField(
            model_name="message",
            name="generic_label",
            field=models.Char<PERSON>ield(
                choices=[
                    ("white_list", "White List"),
                    ("zapped", "Zapped"),
                    ("sent", "Sent"),
                    ("archive", "Archive"),
                    ("trash", "Trash"),
                    ("spam", "Spam"),
                ],
                db_index=True,
                max_length=256,
                null=True,
            ),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="senderprofile",
            name="user_training",
            field=models.Char<PERSON>ield(
                choices=[("white_list", "White List"), ("zapped", "Zapped")], max_length=32, null=True
            ),
        ),
    ]
