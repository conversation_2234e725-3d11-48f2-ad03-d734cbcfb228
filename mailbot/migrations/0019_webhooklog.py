# Generated by Django 4.2.5 on 2024-02-27 14:13

import django.db.models.deletion
import django_extensions.db.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0018_alter_usermailbotprofile_user"),
    ]

    operations = [
        migrations.CreateModel(
            name="WebhookLog",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("scheduled", "Scheduled"), ("processing", "Processing"), ("processed", "Processed")],
                        max_length=32,
                    ),
                ),
                ("received_at", models.DateTime<PERSON>ield(auto_now_add=True)),
                ("started_at", models.DateTime<PERSON>ield(null=True)),
                ("finished_at", models.DateTimeField(null=True)),
                ("metadata", models.J<PERSON><PERSON>ield(default=dict)),
                (
                    "user_mailbot_profile",
                    models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to="mailbot.usermailbotprofile"),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
    ]
