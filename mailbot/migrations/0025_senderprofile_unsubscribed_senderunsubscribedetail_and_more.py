# Generated by Django 4.2.5 on 2024-09-18 06:30

from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0024_alter_senderprofile_sender_email"),
    ]

    operations = [
        migrations.AddField(
            model_name="senderprofile",
            name="unsubscribed",
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name="SenderUnsubscribeDetail",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("unsubscribe_link", models.URLField(max_length=1024, null=True)),
                ("unsubscribe_mail_to", models.TextField(null=True)),
                ("unsubscribe_link_one_click", models.BooleanField(default=False)),
                ("unsubscribed", models.BooleanField(default=False)),
                ("metadata", models.JSONField(default=dict)),
                ("sender_profiles", models.ManyToManyField(to="mailbot.senderprofile")),
                (
                    "user_mailbot_profile",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.usermailbotprofile"),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="senderunsubscribedetail",
            constraint=models.UniqueConstraint(
                fields=("user_mailbot_profile", "unsubscribe_link", "unsubscribe_mail_to"),
                name="sender_unsubscribe_detail_unique_constraint",
            ),
        ),
    ]
