# Generated by Django 4.2.5 on 2025-04-08 09:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0037_mailbotloginlink"),
    ]

    operations = [
        migrations.CreateModel(
            name="MessageCategory",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "name",
                    models.CharField(help_text="Unique name for the message category", max_length=100, unique=True),
                ),
                ("slug", models.SlugField(help_text="Slug for the message category", max_length=120, unique=True)),
                (
                    "is_important",
                    models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text="Flag to mark if this category is important"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MessageCategoryThrough",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "category",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.messagecategory"),
                ),
                ("message", models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.message")),
            ],
            options={
                "unique_together": {("message", "category")},
            },
        ),
        migrations.AddField(
            model_name="message",
            name="categories",
            field=models.ManyToManyField(
                blank=True,
                related_name="messages",
                through="mailbot.MessageCategoryThrough",
                to="mailbot.messagecategory",
            ),
        ),
    ]
