# Generated by Django 4.2.5 on 2024-10-07 15:41

from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0028_alter_senderprofile_sender_name"),
    ]

    operations = [
        migrations.AddField(
            model_name="usermailbotprofile",
            name="user_picture",
            field=models.URLField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name="UserMailBotAnalytics",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("statistics", models.<PERSON><PERSON><PERSON><PERSON>(default=dict)),
                (
                    "user_mailbot_profile",
                    models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to="mailbot.usermailbotprofile"),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
    ]
