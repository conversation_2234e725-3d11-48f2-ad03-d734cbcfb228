# Generated by Django 4.2.5 on 2023-11-28 00:51

from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0005_senderprofile_user_trained_at"),
    ]

    operations = [
        migrations.CreateModel(
            name="DomainTraining",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("sender_domain", models.CharField(max_length=128)),
                ("label_name", models.CharField(blank=True, max_length=128, null=True)),
                ("trained_at", models.DateTime<PERSON>ield(null=True)),
                ("metadata", models.J<PERSON><PERSON>ield(default=dict)),
                (
                    "user_mailbot_profile",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.usermailbotprofile"),
                ),
            ],
        ),
        migrations.AddConstraint(
            model_name="domaintraining",
            constraint=models.UniqueConstraint(
                fields=("user_mailbot_profile", "sender_domain"), name="domain_training_unique_constraint"
            ),
        ),
    ]
