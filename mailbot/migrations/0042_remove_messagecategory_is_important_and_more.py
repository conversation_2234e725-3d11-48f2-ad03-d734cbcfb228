# Generated by Django 4.2.5 on 2025-05-09 08:26

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0041_usermailbotprofile_secondary_profile_preferences"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="messagecategory",
            name="is_important",
        ),
        migrations.AddField(
            model_name="messagecategory",
            name="score",
            field=models.PositiveSmallIntegerField(
                default=0,
                help_text="Score for the message category (0 to 2)",
                validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(2)],
            ),
        ),
    ]
