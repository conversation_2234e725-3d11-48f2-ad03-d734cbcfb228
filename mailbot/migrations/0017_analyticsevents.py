# Generated by Django 4.2.5 on 2024-02-20 11:11

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0016_alter_message_thread_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="AnalyticsEvents",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("lucene_filter", "Lucene Filter"),
                            ("mail_operation_executed", "Mail Operation Executed"),
                        ],
                        db_index=True,
                        max_length=256,
                    ),
                ),
                ("user_email", models.EmailField(db_index=True, max_length=254)),
                ("metadata", models.JSONField()),
            ],
        ),
    ]
