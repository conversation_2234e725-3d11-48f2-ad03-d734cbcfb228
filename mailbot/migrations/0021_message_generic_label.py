# Generated by Django 4.2.5 on 2024-07-12 07:01

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0020_alter_webhooklog_user_mailbot_profile"),
    ]

    operations = [
        migrations.AddField(
            model_name="message",
            name="generic_label",
            field=models.CharField(
                choices=[
                    ("white_list", "White List"),
                    ("grey_list", "Grey List"),
                    ("black_list", "Black List"),
                    ("sent", "Sent"),
                    ("archive", "Archive"),
                    ("trash", "Trash"),
                    ("spam", "Spam"),
                ],
                db_index=True,
                max_length=256,
                null=True,
            ),
        ),
    ]
