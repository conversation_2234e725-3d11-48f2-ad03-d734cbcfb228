# Generated by Django 4.2.5 on 2025-05-13 18:03

from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ("mailbot", "0042_remove_messagecategory_is_important_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="senderprofile",
            name="user_action_reason",
            field=models.CharField(
                choices=[
                    ("unsubscribed", "Unsubscribed"),
                    ("marked_deleted", "Marked Deleted"),
                    ("rule_selected", "Rule Selected"),
                    ("archive", "Archived"),
                    ("zapped", "Zapped"),
                    ("white_list", "White Listed"),
                ],
                max_length=32,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="UserActionLog",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                (
                    "user_action",
                    models.CharField(
                        choices=[
                            ("mark_as_spam", "Mark as Spam"),
                            ("mark_as_read", "Mark as Read"),
                            ("mark_as_starred", "Mark as Starred"),
                            ("archive", "Archive"),
                            ("move_to_trash", "Move to Trash"),
                            ("move_to_inbox", "Move to Inbox"),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    "user_action_reason",
                    models.CharField(
                        choices=[
                            ("unsubscribed", "Unsubscribed"),
                            ("marked_deleted", "Marked Deleted"),
                            ("rule_selected", "Rule Selected"),
                            ("archive", "Archived"),
                            ("zapped", "Zapped"),
                            ("white_list", "White Listed"),
                        ],
                        max_length=32,
                    ),
                ),
                ("metadata", models.JSONField(default=dict)),
                (
                    "sender_profile",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.senderprofile"),
                ),
                (
                    "user_mailbot_profile",
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to="mailbot.usermailbotprofile"),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
    ]
