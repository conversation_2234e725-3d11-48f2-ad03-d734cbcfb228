# Generated by Django 4.2.5 on 2024-09-02 11:52

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0022_usermailbotprofile_encrypted_access_token_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="domaintraining",
            name="label_name",
            field=models.CharField(
                blank=True,
                choices=[
                    ("white_list", "White List"),
                    ("grey_list", "Grey List"),
                    ("black_list", "Black List"),
                    ("zapped", "Zapped"),
                ],
                max_length=32,
                null=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="mailsubscription",
            name="label_name",
            field=models.Char<PERSON>ield(
                choices=[
                    ("white_list", "White List"),
                    ("grey_list", "Grey List"),
                    ("black_list", "Black List"),
                    ("zapped", "Zapped"),
                ],
                max_length=32,
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="message",
            name="generic_label",
            field=models.Char<PERSON><PERSON>(
                choices=[
                    ("white_list", "White List"),
                    ("grey_list", "Grey List"),
                    ("black_list", "Black List"),
                    ("zapped", "Zapped"),
                    ("sent", "Sent"),
                    ("archive", "Archive"),
                    ("trash", "Trash"),
                    ("spam", "Spam"),
                ],
                db_index=True,
                max_length=256,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="senderprofile",
            name="user_training",
            field=models.CharField(
                blank=True,
                choices=[
                    ("white_list", "White List"),
                    ("grey_list", "Grey List"),
                    ("black_list", "Black List"),
                    ("zapped", "Zapped"),
                ],
                max_length=32,
                null=True,
            ),
        ),
    ]
