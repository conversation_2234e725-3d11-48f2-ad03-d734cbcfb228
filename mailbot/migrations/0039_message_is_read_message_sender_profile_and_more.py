# Generated by Django 4.2.5 on 2025-04-09 11:39

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("mailbot", "0038_messagecategory_messagecategorythrough_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="message",
            name="is_read",
            field=models.BooleanField(
                db_index=True, default=False, help_text="Flag indicating if the message has been read", null=True
            ),
        ),
        migrations.AddField(
            model_name="message",
            name="sender_profile",
            field=models.ForeignKey(
                help_text="The sender profile associated with this message",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="messages",
                to="mailbot.senderprofile",
            ),
        ),
        migrations.AddField(
            model_name="usermailbotprofile",
            name="algo_version",
            field=models.CharField(
                choices=[("v1", "Version 1 (Old)"), ("v2", "Version 2 (New)")],
                default="v1",
                help_text="Specifies which algorithm version to use for this user.",
                max_length=10,
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name="domaintraining",
            name="label_name",
            field=models.CharField(
                blank=True,
                choices=[("white_list", "White List"), ("zapped", "Zapped"), ("junk", "Junk")],
                max_length=32,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="mailsubscription",
            name="label_name",
            field=models.CharField(
                choices=[("white_list", "White List"), ("zapped", "Zapped"), ("junk", "Junk")], max_length=32
            ),
        ),
        migrations.AlterField(
            model_name="message",
            name="generic_label",
            field=models.CharField(
                choices=[
                    ("white_list", "White List"),
                    ("zapped", "Zapped"),
                    ("junk", "Junk"),
                    ("sent", "Sent"),
                    ("archive", "Archive"),
                    ("trash", "Trash"),
                    ("spam", "Spam"),
                ],
                db_index=True,
                max_length=256,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="senderprofile",
            name="user_training",
            field=models.CharField(
                choices=[("white_list", "White List"), ("zapped", "Zapped"), ("junk", "Junk")], max_length=32, null=True
            ),
        ),
    ]
