import logging
from typing import List, Dict
from uuid import uuid4

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import models, transaction
from django.db.models import CheckConstraint, Index
from django.db.models.fields.related_descriptors import ReverseManyToOneDescriptor, ReverseOneToOneDescriptor
from django.utils import timezone
from django_extensions.db.models import TimeStampedModel
from django.utils.text import slugify
from applications.models import ScheduledTask, SentEmails
from applications.utils.email import get_email_domain
from execfn.common.utils.cryptography import decrypt_secret, encrypt_secret
from execfn.settings import APP_ENV_LOCAL
from jarvis.models import Workflow
from mailbot.managers import UserMailBotProfileManager
from mailbot.signals import post_app_profile_delete
from mailbot.utils.defaults import MailBotMessageHeaders
from django.core.validators import MinValueValidator, MaxValueValidator

User = get_user_model()
logger = logging.getLogger(__name__)


class MailBotGenericLabel(models.TextChoices):
    """
    The values for following are injected from constance config for each service provider separately.
    """

    WHITE_LIST = "white_list"
    ZAPPED = "zapped"


class UserTrainingReason(models.TextChoices):
    FROM_UI = "from_ui"
    THROUGH_DIGEST = "through_digest"
    THROUGH_FIRST_TIME_SENDER_OVERLAY = "through_first_time_sender_overlay"
    MESSAGE_MOVED_BY_USER = "message_moved_by_user"
    MESSAGE_MOVED_BY_USER_CC_SAME_DOMAIN = "message_moved_by_user_cc_same_domain"
    FIRST_TIME_SENDER_OVERLAY_READ_FRACTION = "first_time_sender_overlay_read_fraction"


class AlgorithmDefinition(TimeStampedModel):
    VERSION_CHOICES = [
        (1, "Version 1"),
        (2, "Version 2"),
        (3, "Version 3"),
    ]

    version = models.PositiveIntegerField(unique=True, choices=VERSION_CHOICES)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, help_text="Optional detailed description of the algorithm.")
    is_active = models.BooleanField(default=True, help_text="Whether this algorithm is active or not")

    def __str__(self):
        return f"{self.name} ({self.version})"


class UserMailBotProfile(TimeStampedModel):
    SERVICE_PROVIDER_CHOICES = (
        (settings.SERVICE_PROVIDER_GOOGLE, "Google"),
        (settings.SERVICE_PROVIDER_MICROSOFT, "Microsoft"),
    )
    FORWARD_IMPORTANT = "forward_important"
    SEND_DIGEST = "send_digest"
    NO_FORWARDING = "no_forwarding"

    FORWARDING_CHOICES = [
        (FORWARD_IMPORTANT, "Auto-forward important emails"),
        (SEND_DIGEST, "Send digest of important emails"),
        (NO_FORWARDING, "Don't forward anything"),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="user_mailbot_profile")
    secondary_mailbot_profiles = models.ManyToManyField("self", through="SecondaryMailBotProfilesThrough")
    service_provider = models.CharField(max_length=16, choices=SERVICE_PROVIDER_CHOICES, db_index=True)
    preferences = models.JSONField(default=dict)
    secondary_profile_preferences = models.JSONField(default=dict, blank=True)
    granted_authentication = models.JSONField(default=dict)
    encrypted_access_token = models.BinaryField(null=True, blank=True, editable=False)
    encrypted_refresh_token = models.BinaryField(null=True, blank=True, editable=False)
    label_mappings = models.JSONField(default=dict)
    user_picture = models.URLField(null=True, blank=True, max_length=2048)
    metadata = models.JSONField(default=dict)
    algorithm_definition = models.ForeignKey(
        AlgorithmDefinition,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Specifies which algorithm to use for this user.",
    )
    deleted = models.DateTimeField(null=True, blank=True)

    objects = UserMailBotProfileManager()
    all_objects = models.Manager()

    def __str__(self):
        display_str = f"{self.user.get_full_name()} [{self.user.email}] : {self.service_provider.capitalize()}"
        if self.deleted:
            display_str += ": deleted"
        return display_str

    def clean(self):
        """Ensure algo_version is always set to the latest version if None."""
        if not self.algo_version:
            self.algo_version = self.get_latest_algo_version()

    @staticmethod
    def get_latest_algo_version():
        """Returns the latest available algorithm version."""
        latest_active_algorithm = AlgorithmDefinition.objects.filter(is_active=True).order_by("-version").first()
        if latest_active_algorithm:
            return latest_active_algorithm
        else:
            raise LookupError({"algorithm": "No active algorithm is available to assign automatically."})

    @property
    def access_token(self) -> str:
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        return decrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=self.encrypted_access_token,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @access_token.setter
    def access_token(self, value: str):
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        self.encrypted_access_token = encrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=value,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @property
    def refresh_token(self) -> str:
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        return decrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=self.encrypted_refresh_token,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @refresh_token.setter
    def refresh_token(self, value: str):
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        self.encrypted_refresh_token = encrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=value,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    def soft_delete(self):
        """Soft deletes the mailbot profile by setting is_deleted to True and updating deleted_at."""
        with transaction.atomic():
            self.delete_related_objects()
            ScheduledTask.objects.filter(user=self.user).delete()
            Workflow.objects.filter(user=self.user).delete()
            self.deleted = timezone.now()
            self.save()
            transaction.on_commit(lambda: post_app_profile_delete.send(user=self.user, sender=self))

    def get_related_objects(self):
        """Retrieves all related objects for reverse foreign keys and reverse one-to-one relationships."""
        attributes = vars(self._meta.model)
        reverse_attributes = [
            a for a, b in attributes.items() if type(b) in [ReverseManyToOneDescriptor, ReverseOneToOneDescriptor]
        ]
        related_objects = []
        for reverse_attribute in reverse_attributes:
            try:
                related_objects.extend(list(getattr(self, reverse_attribute).all()))
            except:
                pass
        return related_objects

    def delete_related_objects(self):
        """Deletes all related objects for reverse foreign keys and reverse one-to-one relationships."""
        for obj in self.get_related_objects():
            obj.delete()


class SecondaryMailBotProfilesThrough(TimeStampedModel):
    primary_mailbot_profile = models.ForeignKey(
        UserMailBotProfile, on_delete=models.CASCADE, related_name="secondary_mailbot_profiles_through"
    )
    secondary_mailbot_profile = models.OneToOneField(UserMailBotProfile, on_delete=models.CASCADE)

    class Meta:
        constraints = [
            CheckConstraint(
                name="profile_cannot_be_both_primary_and_secondary_constraint",
                check=~models.Q(primary_mailbot_profile=models.F("secondary_mailbot_profile")),
            )
        ]

    @classmethod
    def get_associated_mailbot_profile_ids(cls, user_mailbot_profile_id: int) -> List:
        """
        Get a list of associated mailbot profile IDs
        1. If the given profile is primary, list all secondary profiles
        2. If the given profile is secondary, list the primary profile and all other secondary profiles
        3. Else return an empty list

        Args:
            user_mailbot_profile_id: ID of the mailbot profile for which we need to find associations

        Returns:
            list of associated mailbot profiles
        """
        is_primary_profile = cls.objects.filter(primary_mailbot_profile_id=user_mailbot_profile_id).exists()
        is_secondary_profile = cls.objects.filter(secondary_mailbot_profile_id=user_mailbot_profile_id).exists()
        if is_primary_profile:
            associated_mailbot_profile_ids = list(
                cls.objects.filter(primary_mailbot_profile_id=user_mailbot_profile_id).values_list(
                    "secondary_mailbot_profile_id", flat=True
                )
            )
        elif is_secondary_profile:
            primary_mailbot_profile_id = cls.objects.get(
                secondary_mailbot_profile_id=user_mailbot_profile_id
            ).primary_mailbot_profile_id
            other_secondary_mailbot_profile_ids = list(
                SecondaryMailBotProfilesThrough.objects.filter(primary_mailbot_profile_id=primary_mailbot_profile_id)
                .exclude(secondary_mailbot_profile_id=user_mailbot_profile_id)
                .values_list("secondary_mailbot_profile_id", flat=True)
            )
            associated_mailbot_profile_ids = [primary_mailbot_profile_id, *other_secondary_mailbot_profile_ids]
        else:
            associated_mailbot_profile_ids = []
        return associated_mailbot_profile_ids


class MailSubscription(TimeStampedModel):
    user_mailbot_profile = models.ForeignKey(UserMailBotProfile, on_delete=models.CASCADE)
    subscription_id = models.CharField(unique=True, null=True, db_index=True)
    label_name = models.CharField(max_length=32, choices=MailBotGenericLabel.choices)
    expire_at = models.DateTimeField(null=True)

    class Meta:
        constraints = [
            models.constraints.UniqueConstraint(
                fields=["user_mailbot_profile", "label_name"], name="message_webhook_unique_constraint"
            )
        ]


class DomainTraining(TimeStampedModel):
    user_mailbot_profile = models.ForeignKey(UserMailBotProfile, on_delete=models.CASCADE)
    sender_domain = models.CharField(max_length=128)
    label_name = models.CharField(max_length=32, choices=MailBotGenericLabel.choices, blank=True, null=True)
    trained_at = models.DateTimeField(null=True)
    metadata = models.JSONField(default=dict)

    class Meta:
        constraints = [
            models.constraints.UniqueConstraint(
                fields=("user_mailbot_profile", "sender_domain"), name="domain_training_unique_constraint"
            )
        ]


class SenderProfile(TimeStampedModel):
    USER_ACTION_MARK_AS_SPAM = "mark_as_spam"
    USER_ACTION_MARK_AS_READ = "mark_as_read"
    USER_ACTION_MARK_AS_STARRED = "mark_as_starred"
    USER_ACTION_ARCHIVE = "archive"
    USER_ACTION_MOVE_TO_TRASH = "move_to_trash"
    USER_ACTION_MOVE_TO_INBOX = "move_to_inbox"
    USER_ACTION_MOVE_TO_ZAPPED = "move_to_zapped"
    USER_ACTION_AUTO_DELETE = "auto_delete"
    USER_ACTION_CHOICES = (
        (USER_ACTION_MARK_AS_SPAM, "Mark as Spam"),
        (USER_ACTION_MARK_AS_READ, "Mark as Read"),
        (USER_ACTION_MARK_AS_STARRED, "Mark as Starred"),
        (USER_ACTION_ARCHIVE, "Archive"),
        (USER_ACTION_MOVE_TO_TRASH, "Move to Trash"),
        (USER_ACTION_MOVE_TO_INBOX, "Move to Inbox"),
        (USER_ACTION_MOVE_TO_ZAPPED, "Move to Zapped"),
        (USER_ACTION_AUTO_DELETE, "Auto Delete"),
    )
    USER_ACTION_REASON_UNSUBSCRIBED = "unsubscribed"
    USER_ACTION_REASON_MARKED_DELETED = "marked_deleted"
    USER_ACTION_REASON_RULE_SELECTED = "rule_selected"
    USER_ACTION_REASON_MANAGE_SENDER = "manage_sender"
    USER_ACTION_REASON_CHOICES = (
        (USER_ACTION_REASON_UNSUBSCRIBED, "Unsubscribed"),
        (USER_ACTION_REASON_MARKED_DELETED, "Marked Deleted"),
        (USER_ACTION_REASON_RULE_SELECTED, "Rule Selected"),
        (USER_ACTION_REASON_MANAGE_SENDER, "Manage Sender"),
    )
    user_mailbot_profile = models.ForeignKey(UserMailBotProfile, on_delete=models.CASCADE)
    sender_email = models.EmailField(db_index=True)
    normalized_sender_email = models.EmailField(null=True)
    sender_name = models.CharField(max_length=128, blank=True, null=True)
    read_count = models.IntegerField()
    total_count = models.IntegerField()
    sent_count = models.IntegerField(default=0)
    scanned_count = models.IntegerField(default=0)
    oldest_timestamp = models.DateTimeField()
    recent_timestamp = models.DateTimeField()
    user_training = models.CharField(max_length=32, choices=MailBotGenericLabel.choices, null=True)
    user_trained_at = models.DateTimeField(null=True)
    user_training_reason = models.CharField(max_length=64, choices=UserTrainingReason.choices, null=True)
    user_action = models.CharField(max_length=32, choices=USER_ACTION_CHOICES, null=True)
    user_action_reason = models.CharField(max_length=32, choices=USER_ACTION_REASON_CHOICES, null=True)
    metadata = models.JSONField(default=dict)

    def __str__(self) -> str:
        return f"{self.sender_email} ({self.total_count})"

    class Meta:
        constraints = [
            models.constraints.UniqueConstraint(
                fields=["user_mailbot_profile", "normalized_sender_email"],
                name="normalized_sender_email_unique_constraint",
            ),
        ]

    @property
    def sender_domain(self):
        return get_email_domain(self.sender_email)


class MessageCategory(models.Model):
    """
    Model to store message categories with their names, and importance.
    Categories can be used to classify messages and can be marked as important.
    """

    name = models.CharField(max_length=100, unique=True, help_text="Unique name for the message category")
    slug = models.SlugField(max_length=120, unique=True, help_text="Slug for the message category")
    score = models.PositiveSmallIntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(2)],
        help_text="Default score for the message category (0 to 2). Algorithms can override this.",
    )

    def __str__(self):
        return f"{self.name}(Default Score:{self.score})"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)


class AlgorithmCategoryScore(TimeStampedModel):
    algorithm_definition = models.ForeignKey(AlgorithmDefinition, on_delete=models.CASCADE)
    category = models.ForeignKey(MessageCategory, on_delete=models.CASCADE)
    score = models.IntegerField(help_text="The specific score this algorithm assigns to this category.")

    class Meta:
        constraints = [models.UniqueConstraint(fields=["algorithm", "category"], name="unique_algo_category_score")]
        ordering = ["category"]

    def __str__(self):
        return f"{self.algorithm.name} - {self.category.name}: {self.score}"


class Message(TimeStampedModel):
    GENERIC_LABEL_SENT = "sent"
    # Archive means not in inbox, sent, trash, spam, or any mailbot label
    GENERIC_LABEL_ARCHIVE = "archive"
    GENERIC_LABEL_TRASH = "trash"
    GENERIC_LABEL_SPAM = "spam"

    GENERIC_LABEL_CHOICES = MailBotGenericLabel.choices + [
        (GENERIC_LABEL_SENT, "Sent"),
        (GENERIC_LABEL_ARCHIVE, "Archive"),
        (GENERIC_LABEL_TRASH, "Trash"),
        (GENERIC_LABEL_SPAM, "Spam"),
    ]

    user_mailbot_profile = models.ForeignKey(UserMailBotProfile, on_delete=models.CASCADE)
    # TODO: Remove `null=True` from `sender_profile` and `is_read` after migrating existing messages.
    sender_profile = models.ForeignKey(
        SenderProfile,
        on_delete=models.CASCADE,
        related_name="messages",
        help_text="The sender profile associated with this message",
        null=True,
    )
    message_id = models.CharField(max_length=256, unique=True)
    thread_id = models.CharField(max_length=256, db_index=True)
    generic_label = models.CharField(max_length=256, choices=GENERIC_LABEL_CHOICES, db_index=True, null=True)
    received_at = models.DateTimeField()
    is_read = models.BooleanField(
        default=False, null=True, db_index=True, help_text="Flag indicating if the message has been read"
    )
    metadata = models.JSONField(default=dict)
    categories = models.ManyToManyField(
        MessageCategory,
        through="MessageCategoryThrough",
        related_name="messages",
        blank=True,
    )

    class Meta:
        indexes = [Index("created", "user_mailbot_profile", name="message_created_user_profile")]

    @property
    def subject(self):
        return self.metadata.get("subject")

    def fetch_header(self, header_name: str):
        return self.metadata.get("message_headers", {}).get(header_name)

    @property
    def internal_message_category(self):
        """
        Category of the current email. None if current email is not sent by emailzap.
        """
        return self.metadata.get(MailBotMessageHeaders.MESSAGE_CATEGORY.name)


class MessageCategoryThrough(models.Model):
    message = models.ForeignKey(Message, on_delete=models.CASCADE)
    category = models.ForeignKey(MessageCategory, on_delete=models.CASCADE)

    class Meta:
        unique_together = (("message", "category"),)


# NOTE : This is a temporary model to store analytics events for mailbot. We will likely move this to a separate service
class AnalyticsEvents(models.Model):
    EVENT_TYPE_LUCENE_FILTER = "lucene_filter"
    EVENT_TYPE_MAIL_OPERATION_EXECUTED = "mail_operation_executed"

    EVENT_TYPE_CHOICES = (
        (EVENT_TYPE_LUCENE_FILTER, "Lucene Filter"),
        (EVENT_TYPE_MAIL_OPERATION_EXECUTED, "Mail Operation Executed"),
    )
    event_type = models.CharField(max_length=256, choices=EVENT_TYPE_CHOICES, db_index=True)
    user_email = models.EmailField(db_index=True)
    metadata = models.JSONField()

    @classmethod
    def create_analytics_event(cls, event_type: str, user_mailbot_profile_id: int, metadata: Dict):
        """
        Create analytics event

        Args:
            event_type : name of the event
            user_mailbot_profile_id : user's mailbot profile ID
            metadata : meta info about the event

        Returns:
            created analytics event
        """
        valid_event_type = [event[0] for event in cls.EVENT_TYPE_CHOICES]
        analytics_event = None
        try:
            assert event_type in valid_event_type
            user_mailbot_profile = UserMailBotProfile.all_objects.select_related("user").get(id=user_mailbot_profile_id)
            user_email = user_mailbot_profile.user.email
        except AssertionError:
            logger.info("Failed to sync analytics event.", {"reason": "Invalid event type."})
        except UserMailBotProfile.DoesNotExist:
            logger.info("Failed to sync analytics event.", {"reason": "User mailbot profile not found."})
        else:
            analytics_event = cls.objects.create(event_type=event_type, user_email=user_email, metadata=metadata)
            logger.info(f"Analytics event created.", {"user_email": user_email, "event_type": event_type})
        return analytics_event


class WebhookLog(TimeStampedModel):
    SCHEDULED = "scheduled"
    PROCESSING = "processing"
    PROCESSED = "processed"

    STATUS_CHOICES = (
        (SCHEDULED, "Scheduled"),
        (PROCESSING, "Processing"),
        (PROCESSED, "Processed"),
    )
    user_mailbot_profile = models.ForeignKey(UserMailBotProfile, on_delete=models.SET_NULL, null=True)
    status = models.CharField(max_length=32, choices=STATUS_CHOICES)
    received_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True)
    finished_at = models.DateTimeField(null=True)
    metadata = models.JSONField(default=dict)

    def mark_as_processing(self):
        self.status = WebhookLog.PROCESSING
        self.started_at = timezone.now()
        self.save(update_fields=["status", "started_at"])

    def mark_as_processed(self):
        self.status = WebhookLog.PROCESSED
        self.finished_at = timezone.now()
        self.save(update_fields=["status", "finished_at"])

    def add_metadata(self, metadata):
        self.metadata.update(metadata)
        self.save(update_fields=["metadata"])

    def __str__(self):
        return f"{self.user_mailbot_profile} - {self.status}"


class SenderUnsubscribeDetail(TimeStampedModel):
    user_mailbot_profile = models.ForeignKey(UserMailBotProfile, on_delete=models.CASCADE)
    message_id = models.CharField(max_length=256, unique=True)
    sender_profile = models.ForeignKey(SenderProfile, on_delete=models.CASCADE)
    unsubscribe_link = models.TextField(null=True)
    unsubscribe_mail_to = models.TextField(null=True)
    unsubscribe_link_one_click = models.BooleanField(default=False)
    unsubscribed = models.BooleanField(default=False)
    metadata = models.JSONField(default=dict)

    class Meta:
        constraints = [
            CheckConstraint(
                name="unsubscribe_link_or_mail_to",
                check=models.Q(unsubscribe_link__isnull=False) | models.Q(unsubscribe_mail_to__isnull=False),
            )
        ]


class UserMailBotAnalytics(TimeStampedModel):
    user_mailbot_profile = models.OneToOneField(UserMailBotProfile, on_delete=models.CASCADE)
    statistics = models.JSONField(default=dict)


class MailBotUsageLog(TimeStampedModel):
    user_mailbot_profile = models.ForeignKey(UserMailBotProfile, on_delete=models.CASCADE)
    subscription_period_start = models.DateTimeField()
    metadata = models.JSONField(default=dict)

    class Meta:
        constraints = [
            models.constraints.UniqueConstraint(
                fields=["user_mailbot_profile", "subscription_period_start"],
                name="mailbot_usage_log_unique_constraint",
            )
        ]


class MailBotLoginLink(TimeStampedModel):
    user_email = models.EmailField(
        null=True,
        blank=True,
        help_text="User's email who can use this login link to signup. Default to blank for anyone to use.",
    )
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Datetime at which login link should be expired. Default to blank for no expiry.",
    )
    code = models.CharField(max_length=64, unique=True)
    max_redemptions = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum number of unique users that can signup using the link. Default to blank for unlimited.",
    )
    times_redeemed = models.PositiveIntegerField(
        default=0, help_text="Number of unique users used this link to signup."
    )
    subscription_expires_at = models.DateTimeField(
        null=True, blank=True, help_text="Datetime at which subscription should end. Default to blank for forever."
    )
    metadata = models.JSONField(default=dict)

    @property
    def link(self):
        if self.code:
            return f"{settings.FRONTEND_BASE_URL}/?signup_code={self.code}"
        return ""

    def save(self, **kwargs):
        if not self.code:
            self.code = uuid4()
        return super().save(**kwargs)

    def clean(self):
        if self.expires_at and self.expires_at <= timezone.now():
            raise ValidationError("The 'expires_at' date must be in the future.")
        if self.subscription_expires_at and self.subscription_expires_at <= timezone.now():
            raise ValidationError("The 'subscription_expires_at' date must be in the future.")
        if self.user_email and self.max_redemptions:
            raise ValidationError("Max redemptions can only be applied to links without user emails.")
        if self.max_redemptions == 0:
            raise ValidationError("Max redemptions should be non-zero")

    def __str__(self):
        return self.link


class UserActionLog(TimeStampedModel):
    user_mailbot_profile = models.ForeignKey(UserMailBotProfile, on_delete=models.CASCADE)
    sender_profile = models.ForeignKey(SenderProfile, on_delete=models.CASCADE)
    user_action = models.CharField(max_length=32, choices=SenderProfile.USER_ACTION_CHOICES)
    user_action_reason = models.CharField(max_length=32, choices=SenderProfile.USER_ACTION_REASON_CHOICES)
    metadata = models.JSONField(default=dict)
