import logging

from django.http import HttpRequest
from django.utils.deprecation import MiddlewareMixin

from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from execfn import ApplicationTag

logger = logging.getLogger(__name__)


class FeatureFlagMiddleware(MiddlewareMixin):
    def process_request(self, request: HttpRequest) -> None:
        if request.user and request.user.is_authenticated:
            request_path = request.get_full_path()
            if request_path.startswith("/api/v1/mailbot/google-auth") or request_path.startswith(
                "/api/v1/mailbot/microsoft-auth"
            ):
                return
            elif request_path.startswith("/api/v1/mailbot") and not check_feature(
                user_id=request.user.id,
                feature_flag=MailBotFeatureFlag.EMAILZAP,
                application_tag=ApplicationTag.MailBot,
            ):
                raise Exception("Mailbot application requests not allowed at the moment")
            elif request_path.startswith("/api/v1/jarvis") and not check_feature(
                user_id=request.user.id,
                feature_flag=MailBotFeatureFlag.JARVIS,
                application_tag=ApplicationTag.MailBot,
            ):
                raise Exception("Jarvis application requests not allowed at the moment")
