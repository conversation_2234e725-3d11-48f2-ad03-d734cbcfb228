import datetime
import json
import logging
import os
from typing import Dict, List

from constance import config as constance_config
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from django.db.models import F
from django.utils import timezone
from google.ads.googleads.client import GoogleAdsClient
from google.oauth2 import service_account
from stripe import InvalidRequestError
from django.db import transaction
from django.utils.text import slugify


from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from applications.models import EmailTemplate, ScheduledTask, Application, SentEmails
from execfn import ApplicationTag
from execfn.celery import app
from execfn.common.utils.aws import get_ssm_parameter
from execfn.common.utils.cache import cache_lock
from execfn.common.utils.sentry import SentryErrorTypes
from execfn.common.utils.slack import send_slack_message
from execfn.settings import APP_ENV_PROD
from mailbot.api.serializers import MessageWebhookSerializer
from mailbot.models import (
    SenderUnsubscribeDetail,
    UserMailBotProfile,
    MailSubscription,
    Message,
    MailBotGenericLabel,
    WebhookLog,
    MessageCategory,
)
from mailbot.service.factory import MessageServiceFactory
from mailbot.service.gmail import GmailService
from mailbot.service.outlook import OutlookService
from mailbot.utils.check import is_mailbot_active, active_subscription_exists, is_primary_profile
from mailbot.utils.profiles import get_primary_user
from mailbot.utils.digest import calculate_next_digest_hour
from mailbot.utils.core import MailBotMessageCore, MailBotUserCore
from mailbot.utils.defaults import (
    GMAIL_ACCESS_REVOKED_AFTER_DAYS,
    REAUTHORIZATION_REMINDER_EMAILS_SCHEDULE,
    MailBotMessageHeaders,
    MailBotMessageCategory,
    MailBotScheduledTasks,
    AUTO_RESPONDER_MAIL_VERIFICATION_URL,
    GmailKnownLabelName,
    MailBotTemplateTag,
    MAILBOT_INSIGHTS_SLACK_CHANNEL_ID,
    MailBotInsightsSlackMessageFormat,
    MailBotProfileMetadataKey,
    GOOGLE_ADS_CONVERSION_RECORDS_SLACK_CHANNEL_ID,
    GoogleAdsConversionRecordsSlackMessageFormat,
)
from mailbot.utils.email_context import (
    get_digest_context_for_zapped_label,
    get_digest_context_for_secondary_profile,
)
from mailbot.utils.email_scheduler import send_scheduled_email
from mailbot.utils.exceptions import MessageIdNotFoundError, MailBotHTTPError, LabelIdNotFoundError
from mailbot.utils.jarvis import execute_new_email_jarvis_workflows
from mailbot.utils.pusher import SenderProfileChannel, OnboardingEvents
from mailbot.utils.unsubscribe import unsubscribe_email
from mailbot.utils.writing import draft_gmail_message
from payments.models import StripeCustomer, StripeSubscription

from langchain_google_genai import ChatGoogleGenerativeAI
from mailbot.utils.email_analyzer import EmailAnalyzer, EmailAnalysisResult


logger = logging.getLogger(__name__)
User = get_user_model()


@app.task(bind=True)
def process_outlook_event(self, webhook_log_id):
    """Synchronize the webhook received from outlook with database and decide if it should be
    assigned label based on whether it is a new mail.
    Args:
        webhook_log_id: ID of webhook log used to identify this event
    """
    webhook_log = WebhookLog.objects.get(id=webhook_log_id)
    user_mailbot_profile_id = webhook_log.metadata["user_mailbot_profile_id"]
    mail_subscription_id = webhook_log.metadata["mail_subscription_id"]
    try:
        with cache_lock(
            key=f"outlook:webhook:{user_mailbot_profile_id}", lock_timeout=5 * 60, max_blocking_time=60
        ) as locked:
            if locked:
                webhook_log.mark_as_processing()
                try:
                    user_mailbot_profile = UserMailBotProfile.objects.get(id=user_mailbot_profile_id)
                    mail_subscription = MailSubscription.objects.get(subscription_id=mail_subscription_id)
                except UserMailBotProfile.DoesNotExist:
                    logger.exception(
                        "User mailbot profile does not exist while processing webhook event",
                        extra={"mailbot_profile_id": user_mailbot_profile_id},
                    )
                    return
                except MailSubscription.DoesNotExist:
                    logger.exception(
                        "Mail subscription does not exist while processing webhook event",
                        extra={"subscription_id": mail_subscription_id},
                    )
                    return
                else:
                    email = user_mailbot_profile.user.email
                    data = webhook_log.metadata["payload"].get("value", [])
                    change_serializer = MessageWebhookSerializer(data=data, many=True)
                    change_serializer.is_valid(raise_exception=True)
                    user_mailbot_profile.metadata[
                        MailBotProfileMetadataKey.LAST_EVENT_PROCESSED_AT.value
                    ] = timezone.now().isoformat()
                    user_mailbot_profile.save(update_fields=["metadata"])
                    for history in change_serializer.validated_data:
                        change_type = history.get("change_type")
                        message_id = history.get("resource_data", {}).get("id")
                        subscription_id = history.get("subscription_id")
                        if mail_subscription_id != subscription_id:
                            # Ideally this should never happen, but it is not clearly mentioned in the documentation
                            logger.exception(
                                "Mail subscription mismatch while processing outlook message",
                                extra={
                                    "expected_mail_subscription": mail_subscription_id,
                                    "actual_mail_subscription": subscription_id,
                                },
                            )
                            continue
                        label_name = mail_subscription.label_name
                        logger.info(
                            f"Processing outlook event for user {email}, message {message_id}, change type {change_type}"
                        )
                        outlook_service = OutlookService(user_mailbot_profile=user_mailbot_profile)
                        if label_name == MailBotGenericLabel.WHITE_LIST.value:
                            parsed_message = outlook_service.get_message(message_id)
                            db_message_exists = Message.objects.filter(message_id=message_id).exists()
                            if change_type != "created" and not db_message_exists:
                                # If change type is update, deleted then we enforce that message exists with us already
                                logger.info(
                                    f"Message {message_id} does not exist in database, skipping change type {change_type}"
                                )
                                continue
                            db_message = outlook_service.sync_with_database(parsed_message)()
                            sender_email = db_message.metadata["from"][1]
                            if change_type == "created":
                                # TODO: (ONEBO4MK) Currently identifying if mail is newly created or moved from other folder
                                #  is heuristic based on timestamp. Make it more deterministic.
                                is_new_message = timezone.now() - db_message.received_at <= timezone.timedelta(
                                    minutes=1
                                )
                                if is_new_message and not db_message_exists:
                                    logger.info(f"New message created in the inbox for user {email}")
                                    if not execute_new_email_jarvis_workflows(user_mailbot_profile, db_message):
                                        if user_mailbot_profile.preferences.get("label_engine_enabled", True):
                                            logger.info(f"Processing base rules.")
                                            MailBotMessageCore(
                                                service=outlook_service,
                                                parsed_message=parsed_message,
                                                db_message=db_message,
                                            ).process_new_inbox_mail()
                                else:
                                    total_conversation_count = Message.objects.filter(
                                        thread_id=db_message.thread_id
                                    ).count()
                                    if total_conversation_count == 1:
                                        logger.info(
                                            f"Updating user training for user {email}, message {parsed_message.message_id} to white_list"
                                        )
                                        MailBotUserCore(service=outlook_service).update_user_training(
                                            sender_email=sender_email,
                                            user_training=MailBotGenericLabel.WHITE_LIST.value,
                                        )
                                    else:
                                        logger.info(f"{message_id}: Skip user training due to message in Thread")
                        elif label_name == Message.GENERIC_LABEL_SENT:
                            parsed_message = outlook_service.get_message(message_id)
                            outlook_service.sync_with_database(parsed_message=parsed_message)()
                        else:
                            if label_name == MailBotGenericLabel.ZAPPED.value:
                                parsed_message = outlook_service.get_message(message_id)
                                db_message_exists = Message.objects.filter(message_id=message_id).exists()
                                if change_type != "created" and not db_message_exists:
                                    # If change type is update, deleted then we enforce that message exists with us already
                                    continue
                                db_message = outlook_service.sync_with_database(parsed_message)()
                                sender_email = db_message.metadata["from"][1]
                                if change_type == "created":
                                    # Check if message is moved by bot
                                    if parsed_message.single_value_extended_property__bot_assigned_label == label_name:
                                        logger.info(f"Mail moved by bot for {label_name}, skip training")
                                        # remove the extended property now so that if user move it back to same folder again
                                        # it's considered as user training
                                        outlook_service.add_extended_property(db_message.message_id, "")
                                        continue
                                    total_conversation_count = Message.objects.filter(
                                        thread_id=db_message.thread_id
                                    ).count()
                                    if total_conversation_count == 1:
                                        logger.info(
                                            f"Updating user training for user {email}, message {parsed_message.message_id} to {label_name}"
                                        )
                                        MailBotUserCore(service=outlook_service).update_user_training(
                                            sender_email=sender_email,
                                            user_training=label_name,
                                        )
                                    else:
                                        logger.info(f"{message_id}: Skip user training due to message in Thread")

                finally:
                    webhook_log.mark_as_processed()
            else:
                logger.info(f"Will retry outlook webhook event for mailbot profile ID {user_mailbot_profile_id}")
                # Lock is not acquired, so retry after 5 minutes
                # By default, max_retries is 3, so it will retry 3 more times
                self.retry(countdown=5 * 60, throw=False)
    except Exception:
        logger.exception("Single microsoft message failed to process")


@app.task
def initialise_onboarding_tasks(user_mailbot_profile_id: int):
    """Initialise first time mailbot onboarding tasks like scan of mailboxes,
    assigning default mailbot preferences, etc.

    Args:
        user_mailbot_profile_id: Mailbot profile id of user for whom to run first time onboarding tasks
    """
    user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
    service = MessageServiceFactory.get_message_service(user_mailbot_profile)
    try:
        user_mailbot_profile.metadata[
            MailBotProfileMetadataKey.ONBOARDING_STARTED_AT.value
        ] = timezone.now().isoformat()
        user_mailbot_profile.save(update_fields=["metadata"])
        MailBotUserCore(service).perform_onboarding_tasks()
    except Exception:
        logger.exception("Failed to onboard user", extra={"email": user_mailbot_profile.user.email})
        with transaction.atomic():
            user_mailbot_profile = UserMailBotProfile.objects.select_for_update(of=("self",)).get(
                id=user_mailbot_profile_id
            )
            user_mailbot_profile.metadata[MailBotProfileMetadataKey.ONBOARDING_FAILED.value] = True
            user_mailbot_profile.metadata[
                MailBotProfileMetadataKey.ONBOARDING_FAILED_AT.value
            ] = timezone.now().isoformat()
            user_mailbot_profile.save(update_fields=["metadata"])
            if settings.APP_ENV == APP_ENV_PROD:
                send_slack_message(
                    channel_id=MAILBOT_INSIGHTS_SLACK_CHANNEL_ID,
                    text=MailBotInsightsSlackMessageFormat.ONBOARDING_FAILED.value.format(
                        user_email=user_mailbot_profile.user.email,
                    ),
                )
    else:
        with transaction.atomic():
            user_mailbot_profile = UserMailBotProfile.objects.select_for_update(of=("self",)).get(
                id=user_mailbot_profile_id
            )
            user_mailbot_profile.metadata[MailBotProfileMetadataKey.ONBOARDING_COMPLETED.value] = True
            user_mailbot_profile.metadata[
                MailBotProfileMetadataKey.ONBOARDING_COMPLETED_AT.value
            ] = timezone.now().isoformat()
            user_mailbot_profile.save(update_fields=["metadata"])
            transaction.on_commit(
                lambda: SenderProfileChannel().trigger(
                    user_id=user_mailbot_profile.user_id, event_name=OnboardingEvents.COMPLETE
                )
            )


@app.task
def refresh_watch_channel():
    """
    Un-subscribe and re-subscribe the watch channels for users it is going to expire soon.
    """
    expire_in = timezone.now() + timezone.timedelta(days=1)
    profile_ids = (
        MailSubscription.objects.filter(
            expire_at__lte=expire_in,
            user_mailbot_profile__preferences__mailbot_enabled=True,
        )
        .distinct("user_mailbot_profile_id")
        .values_list("user_mailbot_profile_id", flat=True)
    )
    for profile_id in profile_ids:
        logger.info(f"Refreshing watch channel for user mailbot profile ID {profile_id}")
        watch_channels.delay(profile_id)


@app.task
def watch_channels(user_mailbot_profile_id: int):
    """
    Setup watch channels for user's mailbot.
    Args:
        user_mailbot_profile_id: Primary key of UserMailbotProfile for which to set up watch channel.
    """
    logger.info(f"Setting up watch channels for user mailbot profile ID {user_mailbot_profile_id}")
    try:
        user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
    except UserMailBotProfile.DoesNotExist:
        logger.exception(
            "User mailbot profile does not exist while watch mailbox channels setup",
            extra={"mailbot_profile_id": user_mailbot_profile_id},
        )
    else:
        if not is_mailbot_active(user_mailbot_profile):
            logger.info("User does not have mailbot enabled, not setting up watch channels")
            return
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=user_mailbot_profile)
        try:
            service.subscribe_watch_events()
        except MailBotHTTPError as e:
            if e.status_code == 400 and "Precondition check failed" in e.reason:
                pass
            else:
                raise e


@app.task
def unwatch_channels(user_mailbot_profile_id: int):
    """
    Unwatch already setup channels for user's mailbot for our application.
    Args:
        user_mailbot_profile_id: Primary key of UserMailbotProfile for which to unwatch channels.
    """
    try:
        user_mailbot_profile = UserMailBotProfile.all_objects.select_related("user").get(id=user_mailbot_profile_id)
    except UserMailBotProfile.DoesNotExist:
        logger.exception(
            "User mailbot profile does not exist while unwatch mailbox channels",
            extra={"mailbot_profile_id": user_mailbot_profile_id},
        )
    else:
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=user_mailbot_profile)
        service.unsubscribe_watch_events()


@app.task
def send_scheduled_auto_responder_mail(
    sender_email: str, sender_name: str, message_id: str, subject: str, user_id: int, service_provider: str
):
    """
    Checks status of ScheduledTask and send auto responder mail for single user.
    Args:
        sender_email: Email address of the sender
        sender_name: Name of the sender
        message_id: Message ID of the message
        subject: Subject of the message
        user_id: User ID of the current user
        service_provider: Service provider for the message
    """
    if not check_feature(
        user_id=user_id, feature_flag=MailBotFeatureFlag.AI_AUTO_RESPONDER, application_tag=ApplicationTag.MailBot
    ):
        logger.info(f"Skipped sending scheduled auto responder for user ID {user_id}, feature flag is disabled")
        return
    try:
        user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(
            user_id=user_id, service_provider=service_provider
        )
        email_template = EmailTemplate.objects.get(
            application__tag="mailbot", tag=MailBotTemplateTag.AUTO_RESPONDER.value
        )
        email_template.send_email(
            user=user_mailbot_profile.user,
            to=f"{sender_name} <{sender_email}>",
            context={
                "subject": subject,
                "user_email": user_mailbot_profile.user.email,
                "verify_url": AUTO_RESPONDER_MAIL_VERIFICATION_URL,
                "first_name": user_mailbot_profile.user.first_name,
            },
            headers={
                MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.AUTO_RESPONDER.value,
                MailBotMessageHeaders.MAILBOT_PROFILE_ID.value: user_mailbot_profile.id,
                MailBotMessageHeaders.ORIGINAL_MESSAGE_ID.value: message_id,
            },
        )
    except UserMailBotProfile.DoesNotExist:
        logger.exception("Auto responder mail delivery failed due to missing mailbot profile")
    except Exception as exc:
        logger.exception(
            "Auto responder mail delivery failed",
            extra={"user": user_mailbot_profile.user.email, "error_details": exc, "sender": sender_email},
        )
    else:
        logger.info(f"Auto responder mail sent for user {user_mailbot_profile.user.email}, sender {sender_email}")


@app.task
def schedule_mailbot_digests():
    """
    Send daily digest mails according to each user's preference timezone and timeslot.
    """
    current_datetime = timezone.now()
    timeslot = current_datetime.hour * 2 + current_datetime.minute // 30
    digest_user_profiles = UserMailBotProfile.objects.filter(
        preferences__digest_enabled=True, preferences__digest_timeslot=timeslot, preferences__mailbot_enabled=True
    )
    digest_mail_template_zapped = EmailTemplate.objects.get(
        application__tag=ApplicationTag.MailBot.value, tag=MailBotTemplateTag.DIGEST_ZAPPED.value
    )
    for digest_user_profile in digest_user_profiles:
        if check_feature(
            user_id=digest_user_profile.user_id,
            feature_flag=MailBotFeatureFlag.DIGEST_EMAIL,
            application_tag=ApplicationTag.MailBot,
        ) and is_mailbot_active(user_mailbot_profile=digest_user_profile):
            generate_and_send_mailbot_digest.delay(digest_user_profile.id, digest_mail_template_zapped.id)


@app.task
def generate_and_send_mailbot_digest(user_mailbot_profile_id, digest_mail_template_id):
    """
    Generate and send daily digest mail for individual user.
    Args:
        user_mailbot_profile_id: User Mailbot Profile primary key for whom to send digest
        digest_mail_template_id: Digest primary key using which to send digest
    """
    user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
    if not check_feature(
        user_id=user_mailbot_profile.user_id,
        feature_flag=MailBotFeatureFlag.DIGEST_EMAIL,
        application_tag=ApplicationTag.MailBot,
    ):
        logger.info(
            f"Skipped sending scheduled digest email for user mailbot profile ID {user_mailbot_profile_id}, feature flag is disabled"
        )
        return
    try:
        digest_mail = EmailTemplate.objects.get(id=digest_mail_template_id)
        context = get_digest_context_for_zapped_label(user_mailbot_profile=user_mailbot_profile)
        digest_mail.send_email(
            user=user_mailbot_profile.user,
            to=f"{user_mailbot_profile.user.get_full_name()} <{user_mailbot_profile.user.email}>",
            context=context,
            headers={MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.DIGEST.value},
        )
    except Exception as exc:
        logger.exception(
            "Mailbot digest mail delivery failed", extra={"user": user_mailbot_profile.user.email, "error_details": exc}
        )
    else:
        logger.info(f"Digest mail sent for user {user_mailbot_profile.user.email}")


@app.task
def schedule_secondary_profile_mailbot_digests():
    """
    Sends digest emails from secondary user profiles to their associated primary email addresses
    based on each user's configured preferences.

    This function performs the following:
    - Retrieves the current hour.
    - Fetches all secondary profiles that:
        * are linked to a primary user account,
        * have the forwarding policy set to SEND_DIGEST,
        * have the current hour as their scheduled digest time,
        * and have mailbot functionality enabled.
    - For each eligible secondary profile, generates and sends a digest email
      containing recent messages to the corresponding primary email.

    Intended to be triggered periodically (e.g., every hour) via a scheduled task.
    """
    # Get the current hour (0–23) to match against users' scheduled digest hours
    current_hour = timezone.now().hour

    # Fetch secondary profiles that match the criteria for digest sending
    user_mailbot_profiles = UserMailBotProfile.objects.filter(
        secondarymailbotprofilesthrough__isnull=False,
        secondary_profile_preferences__forwarding_policy=UserMailBotProfile.SEND_DIGEST,
        secondary_profile_preferences__digest_scheduled_hour=current_hour,
        preferences__mailbot_enabled=True,
    )
    for user_mailbot_profile in user_mailbot_profiles:
        try:
            generate_and_send_secondary_profile_digest.delay(secondary_user_mailbot_profile_id=user_mailbot_profile.id)
        except Exception as exc:
            logger.exception(
                "Secondary profile mailbot digest processing failed",
                extra={"user": user_mailbot_profile.user.email, "error_details": exc},
            )


@app.task
def generate_and_send_secondary_profile_digest(secondary_user_mailbot_profile_id):
    """
    Generate and send digest email from a secondary profile to the primary email.
    """
    try:
        secondary_user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(
            id=secondary_user_mailbot_profile_id
        )
        primary_user = get_primary_user(secondary_user_mailbot_profile)

        # Always update the next digest hour to prevent rescheduling at the same time if this digest is skipped
        digest_scheduled_hour = calculate_next_digest_hour(
            secondary_user_mailbot_profile.secondary_profile_preferences["digest_frequency"]
        )
        secondary_user_mailbot_profile.secondary_profile_preferences.update(
            {"digest_scheduled_hour": digest_scheduled_hour}
        )
        secondary_user_mailbot_profile.save(update_fields=["secondary_profile_preferences"])

        context = get_digest_context_for_secondary_profile(
            secondary_user_mailbot_profile=secondary_user_mailbot_profile,
            primary_user_mailbot_profile=primary_user.user_mailbot_profile,
        )

        if not context:
            logger.info(
                f"Skipped sending digest for secondary profile {secondary_user_mailbot_profile.user.email} due to no important emails"
            )
            return

        digest_mail_template = EmailTemplate.objects.get(
            application__tag=ApplicationTag.MailBot.value, tag=MailBotTemplateTag.SECONDARY_PROFILE_DIGEST.value
        )
        digest_mail_template.send_email(
            user=primary_user,
            to=f"{primary_user.get_full_name()} <{primary_user.email}>",
            context=context,
            headers={
                MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.DIGEST_FROM_SECONDARY_MAILBOX.value,
                MailBotMessageHeaders.MAILBOT_PROFILE_ID.value: secondary_user_mailbot_profile.id,
            },
        )
    except Exception as exc:
        logger.exception(
            "Mailbot digest mail delivery failed", extra={"user": primary_user.email, "error_details": exc}
        )
    else:
        logger.info(
            f"Secondary profile {secondary_user_mailbot_profile.user.email} digest mail sent for user {primary_user.email}"
        )


@app.task
def archive_scheduled_auto_archival_mail(message_id: str):
    """Archive scheduled auto archival mail for single user.

    Args:
        message_id (int): Message ID.
    """
    try:
        message = Message.objects.select_related("user_mailbot_profile").get(message_id=message_id)
    except Message.DoesNotExist:
        logger.info(f"Message with ID {message_id} does not exist for archiving")
    else:
        if not is_mailbot_active(user_mailbot_profile=message.user_mailbot_profile):
            logger.info("User does not have mailbot enabled, not archiving message")
            return
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=message.user_mailbot_profile)
        is_archived = service.archive_message(message.message_id)
        if is_archived:
            logger.info(f"Message with ID {message_id} archived successfully!")
        else:
            logger.exception(f"Message with ID {message_id} could not be archived.")


def process_gmail_draft_messages(user_mailbot_profile: UserMailBotProfile, draft_message_ids: List[str]):
    """
    Process draft messages to check if it contains a bot command and queue it for processing
    Args:
        user_mailbot_profile: user's mailbot profile
        draft_message_ids: list of draft message ids
    """
    gmail_service = GmailService(user_mailbot_profile=user_mailbot_profile)
    for draft_message_id in draft_message_ids:
        try:
            message_body = gmail_service.get_message_body(draft_message_id)
        except MessageIdNotFoundError:
            continue
        else:
            if any([x in message_body.lower() for x in settings.AI_BOT_IDENTIFIERS]):
                process_bot_command.delay(user_mailbot_profile.id, draft_message_id)


@app.task(bind=True)
def process_gmail_event(self, webhook_log_id):
    """
    Process gmail webhook event and sync with database.

    Args:
        webhook_log_id: ID of webhook log used to identify this event
    """
    webhook_log = WebhookLog.objects.get(id=webhook_log_id)
    mailbot_profile_id = webhook_log.metadata["user_mailbot_profile_id"]
    history_id = webhook_log.metadata["history_id"]
    with cache_lock(key=f"gmail:webhook:{mailbot_profile_id}", lock_timeout=5 * 60, max_blocking_time=60) as locked:
        if locked:
            webhook_log.mark_as_processing()
            try:
                user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=mailbot_profile_id)
            except UserMailBotProfile.DoesNotExist:
                logger.exception(
                    "User mailbot profile does not exist while processing webhook event",
                    extra={"mailbot_profile_id": mailbot_profile_id},
                )
                return
            else:
                email = user_mailbot_profile.user.email
                gmail_service = GmailService(user_mailbot_profile=user_mailbot_profile)
                if not gmail_service.is_mailbox_present:
                    # If refresh token is expired, then mailbox will not be present
                    # We disabled the mailbot for this case so future events will not be processed
                    logger.info(f"Gmail mailbox not present while processing webhook event for user {email}")
                    return
                start_history_id = user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.HISTORY_ID.value)
                if not start_history_id:
                    # Unsubscribe events as onboarding didn't complete successfully
                    gmail_service = GmailService(user_mailbot_profile=user_mailbot_profile)
                    logger.exception("History ID not found, unsubscribing events")
                    gmail_service.unsubscribe_watch_events()
                    return
                if history_id <= start_history_id:
                    logger.info(f"History ID {history_id} is already processed for user {email}")
                    return
                try:
                    latest_history_id = gmail_service.mailbox.latest_history_id
                except MailBotHTTPError as e:
                    if e.status_code == 400 and "Precondition check failed" in e.reason:
                        return
                    else:
                        raise e
                # Save latest history id in user mailbot profile
                user_mailbot_profile.metadata[MailBotProfileMetadataKey.HISTORY_ID.value] = latest_history_id
                user_mailbot_profile.metadata[
                    MailBotProfileMetadataKey.LAST_EVENT_PROCESSED_AT.value
                ] = timezone.now().isoformat()
                user_mailbot_profile.save(update_fields=["metadata"])
                if not check_feature(
                    user_id=user_mailbot_profile.user_id,
                    feature_flag=MailBotFeatureFlag.EMAILZAP,
                    application_tag=ApplicationTag.MailBot,
                ):
                    logger.info(f"EmailZap is disabled for user {user_mailbot_profile.user.email}")
                    return
                # Currently we process all the histories between [start_history_id, latest_history_id]
                # Hence, history_id just served the purpose of signalling that we need to process.
                logger.info(
                    f"Processing gmail events for histories between {start_history_id} and {latest_history_id}, email {email}"
                )
                # Get list of histories between [start_history_id, latest_history_id]
                histories, _ = gmail_service.list_history(start_history_id=start_history_id)
                webhook_log.add_metadata(metadata={"histories": histories})
                message_ids_sent = set()
                message_ids_modified = set()
                mailbot_label_ids = {
                    gmail_service.get_label_id(mailbot_label_name)
                    for mailbot_label_name in gmail_service.mailbot_labels.values()
                }
                draft_message_ids = list()
                for history in histories:
                    for message_added in history.get("messagesAdded", []):
                        try:
                            message = message_added["message"]
                            message_id = message["id"]
                            created_label_ids = message.get("labelIds", [])
                            if GmailKnownLabelName.SENT.value in created_label_ids:
                                message_ids_sent.add(message_id)
                                continue
                            # as message is created, labelIds must contain INBOX
                            if GmailKnownLabelName.INBOX.value not in created_label_ids:
                                if GmailKnownLabelName.DRAFT.value in created_label_ids:
                                    draft_message_ids.append(message_id)
                                # SENT and DRAFT mails
                                logger.warning(
                                    f"Ignoring message_id {message_id} as it was created outside inbox with label_ids {created_label_ids}"
                                )
                                continue
                            try:
                                parsed_message = gmail_service.get_message(message_id)
                            except MessageIdNotFoundError:
                                # Processing old history, message has been deleted
                                logger.warning(f"Message ID {message_id} does not exist in mailbox for user {email}")
                                continue
                            else:
                                logger.info(
                                    f"New message {message_id} created in inbox with label_ids {created_label_ids}"
                                )
                                db_message = gmail_service.sync_with_database(parsed_message=parsed_message)()

                                email_received_at = parsed_message.received_at
                                mailbot_last_enabled_at = timezone.datetime.fromisoformat(
                                    user_mailbot_profile.metadata[MailBotProfileMetadataKey.LAST_ENABLED_AT.value]
                                )
                                if email_received_at < mailbot_last_enabled_at:
                                    logger.info(
                                        f"We're not running the email through the label engine as it was "
                                        f"received when the mailbot was inactive. "
                                        f"ReceivedAt {email_received_at} | "
                                        f"BotEnabledAt {mailbot_last_enabled_at}"
                                    )
                                elif not execute_new_email_jarvis_workflows(user_mailbot_profile, db_message):
                                    if user_mailbot_profile.preferences.get("label_engine_enabled", True):
                                        logger.info(f"Processing base rules.")
                                        MailBotMessageCore(
                                            service=gmail_service, parsed_message=parsed_message, db_message=db_message
                                        ).process_new_inbox_mail()
                        except Exception:
                            logger.exception(
                                "Failed to process single gmail message in history",
                                extra={"email": email, "event": "message added", "message_id": message_id},
                            )
                    for message_modified in history.get("labelsAdded", []):
                        try:
                            message = message_modified["message"]
                            message_id = message["id"]
                            final_label_ids = set(message.get("labelIds", []))
                            added_label_ids = set(message_modified.get("labelIds", []))
                            added_mailbot_label_ids = added_label_ids.intersection(mailbot_label_ids)
                            final_mailbot_label_ids = final_label_ids.intersection(mailbot_label_ids)

                            unread_added_to_mailbot_label = (
                                GmailKnownLabelName.UNREAD.value in added_label_ids and final_mailbot_label_ids
                            )
                            trash_added_to_mailbot_label = (
                                GmailKnownLabelName.TRASH.value in added_label_ids and final_mailbot_label_ids
                            )
                            if (
                                not added_mailbot_label_ids
                                and not unread_added_to_mailbot_label
                                and not trash_added_to_mailbot_label
                            ):
                                logger.info(
                                    f"{message_id}: Not considering the message delta as it is not important for mailbot"
                                )
                            else:
                                message_ids_modified.add(message_id)
                        except Exception:
                            logger.exception(
                                "Failed to process single gmail message in history",
                                extra={"email": email, "event": "label added", "message_id": message_id},
                            )

                    for message_modified in history.get("labelsRemoved", []):
                        try:
                            message = message_modified["message"]
                            message_id = message["id"]
                            # Many times `labelIds` is not present in labelsRemoved
                            final_label_ids = set(message.get("labelIds", []))
                            removed_label_ids = set(message_modified.get("labelIds", []))
                            removed_mailbot_label_ids = removed_label_ids.intersection(mailbot_label_ids)
                            final_mailbot_label_ids = final_label_ids.intersection(mailbot_label_ids)
                            unread_removed_from_mailbot_label = (
                                GmailKnownLabelName.UNREAD.value in removed_label_ids and final_mailbot_label_ids
                            )
                            trash_removed_from_mailbot_label = (
                                GmailKnownLabelName.TRASH.value in removed_label_ids and final_mailbot_label_ids
                            )
                            if (
                                not removed_mailbot_label_ids
                                and not unread_removed_from_mailbot_label
                                and not trash_removed_from_mailbot_label
                            ):
                                logger.info(
                                    f"{message_id}: Not considering the message delta as it is not important for mailbot"
                                )
                            else:
                                message_ids_modified.add(message_id)
                        except Exception:
                            logger.exception(
                                "Failed to process single gmail message in history",
                                extra={"email": email, "event": "label removed", "message_id": message_id},
                            )

                parsed_gmail_messages = gmail_service.get_messages(
                    message_ids=message_ids_modified.union(message_ids_sent)
                )
                for parsed_gmail_message in parsed_gmail_messages:
                    try:
                        gmail_service.sync_with_database(parsed_message=parsed_gmail_message)()
                    except Exception:
                        logger.exception(
                            "Failed to process single gmail message in history",
                            extra={
                                "email": email,
                                "event": "sync with database",
                                "message_id": message_id,
                            },
                        )
                if draft_message_ids and check_feature(
                    user_id=user_mailbot_profile.user_id,
                    feature_flag=MailBotFeatureFlag.JARVIS,
                    application_tag=ApplicationTag.MailBot,
                ):
                    process_gmail_draft_messages(user_mailbot_profile, draft_message_ids)
            finally:
                webhook_log.mark_as_processed()
        else:
            logger.info(
                f"Will retry gmail webhook event history_id {history_id} for mailbot profile ID {mailbot_profile_id}"
            )
            # Lock is not acquired, so retry after 5 minutes
            # By default, max_retries is 3, so it will retry 3 more times
            self.retry(countdown=5 * 60, throw=False)


@app.task()
def process_bot_command(user_mailbot_profile_id, message_id):
    """
    Process AI BOT command to take the action specified in the email.

    For now we've hardcoded the action to creating a draft

    Args:
        user_mailbot_profile_id : user's mailbot profile ID
        message_id: identifier of the email that contains the bot command
    """
    logger.info(f"Processing draft message {message_id}")
    user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
    draft_gmail_message(user_mailbot_profile, message_id)


@app.task
def send_reauthorization_mails_dev(user_mailbot_profile_id: int):
    """
    Send reauthorization mail for user who have refresh token expired on dev environment in 24 hours.

    Args:
        user_mailbot_profile_id (int): Mailbot profile ID
    """
    try:
        user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
        message_sent = EmailTemplate.send_email_using_template(
            user=user_mailbot_profile.user,
            context={"frontend_base_url": settings.FRONTEND_BASE_URL},
            tag=MailBotTemplateTag.REAUTHORIZATION_DEV.value,
            to=user_mailbot_profile.user.email,
            application=Application.objects.get(tag=ApplicationTag.MailBot.value),
            headers={MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.REAUTHORIZATION_DEV.value},
        )
        if message_sent:
            logger.info(f"Reauthorization email sent to user {user_mailbot_profile.user.email}")
        else:
            logger.info(f"Failed to send reauthorization email to user {user_mailbot_profile.user.email}")
    except UserMailBotProfile.DoesNotExist:
        logger.exception(f"MailBot profile does not exist")


@app.task
def send_reauthorization_mail_prod(user_mailbot_profile_id: int):
    """
    Send reauthorization reminder email for production environment for user who have not reauthorized for long time.

    Args:
        user_mailbot_profile_id (int): Mailbot profile ID
    """
    now = timezone.now()
    try:
        user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
    except UserMailBotProfile.DoesNotExist:
        logger.exception(f"MailBot profile does not exist")
    else:
        was_last_login_before_june3 = False
        if user_mailbot_profile.user.last_login:
            if user_mailbot_profile.user.last_login < datetime.datetime(
                year=2024, month=6, day=3, tzinfo=datetime.timezone.utc
            ):
                was_last_login_before_june3 = True
        else:
            # User has never logged in to the new system
            was_last_login_before_june3 = True
        if was_last_login_before_june3:
            # For users who have not logged in after June 3, 2024,
            # access revokes on 30th September 2024
            access_revokes_at = datetime.datetime(year=2024, month=9, day=30, tzinfo=datetime.timezone.utc)
        else:
            access_revokes_at = user_mailbot_profile.user.last_login + timezone.timedelta(
                days=GMAIL_ACCESS_REVOKED_AFTER_DAYS
            )
        is_access_revoked = now >= access_revokes_at
        if is_access_revoked:
            # If access is revoked, mailbot will be disabled and tokens will be expired so just check the active subscription
            if not active_subscription_exists(user_mailbot_profile=user_mailbot_profile):
                logger.info("User does not have active subscription, not sending reauthorization email")
                return
            # User has not logged in and Google has revoked the access, send different email
            message_sent = EmailTemplate.send_email_using_template(
                user=user_mailbot_profile.user,
                context={
                    "backend_login_url": f"{settings.BACKEND_BASE_URL}/api/v1/mailbot/google-auth/login/",
                    "first_name": user_mailbot_profile.user.first_name,
                },
                tag=MailBotTemplateTag.REAUTHORIZATION_REMINDER_POST_EXPIRY_PROD.value,
                to=user_mailbot_profile.user.email,
                application=Application.objects.get(tag=ApplicationTag.MailBot.value),
                headers={
                    MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.REAUTHORIZATION_REMINDER_POST_EXPIRY_PROD.value
                },
            )
        else:
            if not is_mailbot_active(user_mailbot_profile=user_mailbot_profile):
                logger.info("User does not have mailbot enabled, not sending reauthorization email")
                return
            message_sent = EmailTemplate.send_email_using_template(
                user=user_mailbot_profile.user,
                context={
                    "backend_login_url": f"{settings.BACKEND_BASE_URL}/api/v1/mailbot/google-auth/login/",
                    "first_name": user_mailbot_profile.user.first_name,
                },
                tag=MailBotTemplateTag.REAUTHORIZATION_REMINDER_PROD.value,
                to=user_mailbot_profile.user.email,
                application=Application.objects.get(tag=ApplicationTag.MailBot.value),
                headers={
                    MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.REAUTHORIZATION_REMINDER_PROD.value
                },
            )
        if message_sent:
            logger.info(f"Reauthorization email sent to prod user {user_mailbot_profile.user.email}")
            for _, days_before in sorted(REAUTHORIZATION_REMINDER_EMAILS_SCHEDULE.items()):
                # if days_before is negative, then timedelta will automatically add days to the date
                # to send reminder emails after access revokes
                next_email_send_at = access_revokes_at - timezone.timedelta(days=days_before)
                if now >= next_email_send_at:
                    # Skip days which have already occurred
                    continue
                else:
                    # Schedule the next reminder email
                    ScheduledTask.create_one_off_task(
                        user=user_mailbot_profile.user,
                        task=MailBotScheduledTasks.SEND_REAUTHORIZATION_PROD_MAIL.value,
                        task_args=(user_mailbot_profile.id,),
                        start_time=next_email_send_at,
                    )
                    break
        else:
            logger.exception(
                "Failed to send reauthorization email to prof user.", extra={"user": user_mailbot_profile.user.email}
            )


@app.task
def on_refresh_token_expired(user_mailbot_profile_id: int):
    """
    Cancel the subscription for user who have revoked their access but did not cancel the subscription.
    """
    try:
        user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
    except UserMailBotProfile.DoesNotExist:
        logger.exception(f"MailBot profile does not exist")
    else:
        if is_primary_profile(user_mailbot_profile=user_mailbot_profile):
            try:
                customer_id = user_mailbot_profile.user.stripe_customer.id
                subscription = StripeSubscription.objects.get(
                    customer_id=customer_id,
                    status=StripeSubscription.STATUS_ACTIVE,
                    cancel_at_period_end=False,
                )
            except (StripeSubscription.DoesNotExist, StripeCustomer.DoesNotExist):
                logger.info(
                    f"User {user_mailbot_profile.user.email} does not have an active subscription, not scheduling reactivation email"
                )
            else:
                # We cancel the subscription if the tokens are expired
                subscription.cancel(reason=StripeSubscription.CANCELED_DUE_TO_TOKENS_EXPIRED)
                send_reactivate_reminder_mail.delay(user_mailbot_profile_id)


@app.task
def on_refresh_token_renew(user_mailbot_profile_id: int):
    """
    Renew the subscription for user who have restored their access but did not cancel the subscription.
    """
    try:
        user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
    except UserMailBotProfile.DoesNotExist:
        logger.exception(f"MailBot profile does not exist")
    else:
        if is_primary_profile(user_mailbot_profile=user_mailbot_profile):
            try:
                customer_id = user_mailbot_profile.user.stripe_customer.id
                subscription = StripeSubscription.objects.get(
                    customer_id=customer_id,
                    status=StripeSubscription.STATUS_ACTIVE,
                    cancel_at_period_end=True,
                )
            except (StripeSubscription.DoesNotExist, StripeCustomer.DoesNotExist):
                logger.info(
                    f"User {user_mailbot_profile.user.email} does not have any existing subscriptions for renewal."
                )
            else:
                try:
                    # We renew the subscription if the tokens are restored
                    subscription.renew()
                except InvalidRequestError:
                    pass


@app.task
def send_reactivate_reminder_mail(user_mailbot_profile_id: int):
    """
    Send email to user who have revoked their access but did not cancel the subscription.
    Args:
        user_mailbot_profile_id (int): Mailbot profile ID
    """
    try:
        user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
    except UserMailBotProfile.DoesNotExist:
        logger.exception(f"MailBot profile does not exist")
    else:
        if user_mailbot_profile.preferences.get("mailbot_enabled") or not user_mailbot_profile.metadata.get(
            MailBotProfileMetadataKey.REFRESH_TOKEN_EXPIRED.value
        ):
            logger.exception("MailBot profile is enabled for user while sending reactivation email")
            return
        # Since we canceled the subscription, we need to fetch the latest subscription
        # and check the reason for cancellation
        try:
            latest_subscription = StripeSubscription.objects.get(
                customer=user_mailbot_profile.user.stripe_customer,
                status=StripeSubscription.STATUS_ACTIVE,
                cancel_at_period_end=True,
            )
        except StripeSubscription.DoesNotExist:
            logger.info(f"User {user_mailbot_profile.user.email} has no active subscription with cancel at period end")
            return
        template = EmailTemplate.objects.get(
            tag=MailBotTemplateTag.REACTIVATE_REMINDER.value,
            application=Application.objects.get(tag=ApplicationTag.MailBot.value),
        )
        message_sent = template.send_email(
            user=user_mailbot_profile.user,
            to=user_mailbot_profile.user.email,
            context={
                "frontend_base_url": settings.FRONTEND_BASE_URL,
                "name": user_mailbot_profile.user.first_name,
                "subscription_cancel_at": latest_subscription.current_period_end.date(),
            },
            headers={
                MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.REACTIVATE_REMINDER.value,
                MailBotMessageHeaders.MAILBOT_PROFILE_ID.value: user_mailbot_profile_id,
                "Reply-To": constance_config.SES_SUPPORT_EMAIL,
            },
        )
        if message_sent:
            logger.info(f"Reactivation reminder email sent to user {user_mailbot_profile.user.email}")
            if (
                SentEmails.objects.filter(
                    user=user_mailbot_profile.user,
                    template=template,
                    created__gte=timezone.now() - timezone.timedelta(days=7),
                ).count()
                < 3
            ):
                ScheduledTask.create_one_off_task(
                    user=user_mailbot_profile.user,
                    task=MailBotScheduledTasks.SEND_REACTIVATE_REMINDER_MAIL.value,
                    task_args=(user_mailbot_profile.id,),
                    start_time=timezone.now() + timezone.timedelta(days=2),
                )
            else:
                # Ideally should not happen
                logger.info("Already sent more than 3 emails in past few days")


@app.task
def mailbot_health_check():
    """
    Run health check for mailbot users
    1. check if they received or sent a message in last [-13, -1] hours, and they are not processed by mailbot
    """
    now = timezone.now()
    last_thirteen_hours = now - timezone.timedelta(hours=13)
    # We are checking emails received till last hour since messages may take some time to sync with database
    processed_user_mailbot_profile_ids = (
        Message.objects.filter(created__gt=last_thirteen_hours)
        .order_by("user_mailbot_profile_id", "-created")
        .distinct("user_mailbot_profile_id")
        .values("user_mailbot_profile_id")
    )
    not_processed_user_mailbot_profiles = (
        UserMailBotProfile.objects.exclude(id__in=processed_user_mailbot_profile_ids)
        .filter(preferences__mailbot_enabled=True)
        .annotate(user_email=F("user__email"))
    )
    health_check_failed_details = []
    mailbot_errors = []
    credentials_expired_emails = []
    for user_mailbot_profile in not_processed_user_mailbot_profiles:
        service = MessageServiceFactory.get_message_service(user_mailbot_profile)
        user_email = user_mailbot_profile.user_email
        if not getattr(service, "is_mailbox_present", True):
            credentials_expired_emails.append(user_email)
            continue
        try:
            # check if user received any new message in last 12 hours
            last_message_received = service.scan_messages(
                generic_label=MailBotGenericLabel.WHITE_LIST.value,
                limit=1,
                select_fields=["id"],
                newer_than_hours=13,
                older_than_hours=1,
            )
            last_message_sent = service.scan_messages(
                generic_label=Message.GENERIC_LABEL_SENT,
                limit=1,
                select_fields=["id"],
                newer_than_hours=13,
                older_than_hours=1,
            )
            if last_message_received or last_message_sent:
                health_check_failed_details.append(
                    {
                        "user": user_email,
                        "last_message_received": last_message_received,
                        "last_message_sent": last_message_sent,
                    }
                )
        except MailBotHTTPError as e:
            if e.status_code == 403 or (e.status_code == 400 and "Precondition check failed" in e.reason):
                logger.info(f"Mailbot precondition check failed for user {user_email}")
            else:
                mailbot_errors.append(str(e))
    if credentials_expired_emails:
        logger.error(
            "Mailbot enabled for users with expired credentials",
            extra={
                "error_type": SentryErrorTypes.ALERT.value,
                "emails": credentials_expired_emails,
                "total": len(credentials_expired_emails),
            },
        )
    if health_check_failed_details:
        logger.error(
            "New messages were not processed for the user in last 12 hours",
            extra={
                "details": health_check_failed_details,
                "total": len(health_check_failed_details),
                "error_type": SentryErrorTypes.ALERT.value,
            },
        )
    if mailbot_errors:
        logger.error(
            "Mailbot health check failed due to HTTP errors",
            extra={"total": len(mailbot_errors), "error_type": SentryErrorTypes.ALERT.value, "errors": mailbot_errors},
        )


@app.task
def send_feedback_mail(user_id):
    """
    Send feedback email for current user.
    Args:
        user_id: user to send feedback to
    """
    user = User.objects.select_related("user_mailbot_profile").get(id=user_id)
    if not user.user_mailbot_profile.preferences["mailbot_enabled"]:
        return
    message_sent = EmailTemplate.send_email_using_template(
        user=user,
        context={"name": user.first_name},
        tag="feedback_mail",
        to=user.email,
        application=Application.objects.get(tag=ApplicationTag.MailBot.value),
        headers={MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.FEEDBACK.value},
    )
    if message_sent:
        logger.info(f"Feedback sent successfully for user {user.email}")
        user_mailbot_profile = UserMailBotProfile.objects.get(user=user)
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.FEEDBACK_MAIL_SENT.value] = True
        user_mailbot_profile.metadata[
            MailBotProfileMetadataKey.FEEDBACK_MAIL_SENT_AT.value
        ] = timezone.now().isoformat()
        user_mailbot_profile.save(update_fields=["metadata"])
    else:
        logger.exception("Unable to send feedback email", extra={"email": user.email})


@app.task
def upload_google_ads_click_conversions():
    """
    Upload google ads click conversion data for T-1 day
    """
    scopes = ["https://www.googleapis.com/auth/adwords"]
    developer_token = os.environ.get("GOOGLE_ADS_DEVELOPER_TOKEN")
    service_account_info = json.loads(get_ssm_parameter("/prod/google/adsServiceAccount"))
    conversion_action_id = "***********"
    customer_id = "**********"
    # setup delegated client
    credentials = service_account.Credentials.from_service_account_info(info=service_account_info, scopes=scopes)
    delegated_credentials = credentials.with_subject("<EMAIL>")
    client = GoogleAdsClient(credentials=delegated_credentials, developer_token=developer_token)
    # prepare all conversion to upload
    click_conversions = []
    user_creation_datetime_to_sync = timezone.now() - timezone.timedelta(hours=1)
    logger.info(f"Uploading google ads click conversion records for {user_creation_datetime_to_sync.date()}.")
    user_mailbot_profiles_qs = UserMailBotProfile.objects.filter(created__gte=user_creation_datetime_to_sync).filter(
        user__stripe_customer__subscriptions__isnull=False
    )
    synced_user_mailbot_profiles = []
    for user_mailbot_profile in user_mailbot_profiles_qs.only("metadata", "created"):
        if not (
            tracking_parameters := user_mailbot_profile.metadata.get(
                MailBotProfileMetadataKey.TRACKING_PARAMETERS.value
            )
        ):
            continue
        if not (gclid := tracking_parameters.get("gclid")):
            continue
        conversion_date_time = f"{user_mailbot_profile.created.strftime('%Y-%m-%d %H:%M:%S')}+00:00"
        conversion_action_service = client.get_service("ConversionActionService")
        click_conversion = client.get_type("ClickConversion")
        click_conversion.conversion_action = conversion_action_service.conversion_action_path(
            customer_id, conversion_action_id
        )
        click_conversion.gclid = gclid
        click_conversion.conversion_value = 1.0
        click_conversion.conversion_date_time = conversion_date_time
        click_conversion.currency_code = "USD"
        click_conversions.append(click_conversion)
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.GOOGLE_ADS_CLICK_CONVERSION_SYNCED.value] = True
        synced_user_mailbot_profiles.append(user_mailbot_profile)
    logger.info(f"Total {len(click_conversions)} google ads click conversion records to upload.")
    # upload all conversion
    conversion_upload_service = client.get_service("ConversionUploadService")
    request = client.get_type("UploadClickConversionsRequest")
    request.customer_id = customer_id
    request.conversions.extend(click_conversions)
    request.partial_failure = True
    conversion_upload_response = conversion_upload_service.upload_click_conversions(
        request=request,
    )
    send_slack_message(
        channel_id=GOOGLE_ADS_CONVERSION_RECORDS_SLACK_CHANNEL_ID,
        text=GoogleAdsConversionRecordsSlackMessageFormat.GOOGLE_ADS_CLICK_CONVERSION_RECORDS.value.format(
            results=len(conversion_upload_response.results),
        ),
    )
    UserMailBotProfile.objects.bulk_update(
        synced_user_mailbot_profiles,
        fields=["metadata"],
    )


@app.task
def update_labels(message_id, remove_label_name=None, add_label_name=None):
    """
    Remove or add label to message.
    Args:
        message_id: Message ID
        remove_label_name: Label name to remove
        add_label_name: Label name to add
    """
    try:
        message = Message.objects.select_related("user_mailbot_profile").get(message_id=message_id)
    except Message.DoesNotExist:
        logger.exception(f"Message with ID {message_id} does not exist for auto removing label")
    else:
        if not check_feature(
            user_id=message.user_mailbot_profile.user.id,
            feature_flag=MailBotFeatureFlag.ARCHIVING_EMAILZAP,
            application_tag=ApplicationTag.MailBot,
        ):
            return
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=message.user_mailbot_profile)
        if isinstance(service, GmailService):
            logger.info(
                f"Updating labels for message {message_id}, remove_label_name {remove_label_name}, add_label_name {add_label_name}"
            )
            remove_label_ids = [service.get_label_id(remove_label_name)] if remove_label_name else None
            add_label_ids = [service.get_label_id(add_label_name)] if add_label_name else None
            try:
                service.mailbox.update_labels(
                    message_id, remove_label_ids=remove_label_ids, add_label_ids=add_label_ids
                )
            except MessageIdNotFoundError:
                pass
            except LabelIdNotFoundError:
                pass
        elif isinstance(service, OutlookService):
            assert add_label_name is not None, "Add label name is required for outlook service"
            # In outlook, we can only move message to a folder, so remove label is not supported
            logger.info(f"Moving message {message_id} to folder {add_label_name}")
            service.move_to_label(message_id=message_id, destination_label_name=add_label_name)


@app.task
def unsubscribe_from_single_mail(sender_unsubscribe_detail_id):
    unsubscribe_email(sender_unsubscribe_detail_id)


@app.task
def unsubscribe_sender(sender_profile_id):
    """
    Unsubscribes the sender profile from all the sender_unsubscribe_detail objects associated with it.

    Args:
        sender_profile_id: ID of the sender profile to unsubscribe.
    """
    for sender_unsubscribe_detail in SenderUnsubscribeDetail.objects.filter(
        sender_profile_id=sender_profile_id, unsubscribed=False
    ):
        unsubscribe_from_single_mail.delay(sender_unsubscribe_detail.id)


@app.task
def send_mail(user_mailbot_profile_id: int, template_tag):
    try:
        user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_mailbot_profile_id)
    except UserMailBotProfile.DoesNotExist:
        logger.error(f"MailBot profile does not exist while sending email", extra={"template_tag": template_tag})
    else:
        send_scheduled_email(user_mailbot_profile=user_mailbot_profile, template_tag=template_tag)


@app.task
def analyze_email_message(message_id: str, message_subject: str, message_body: str, sender_email: str = ""):
    """
    Analyzes a single email message using an LLM to categorize it and identify required actions.
    Stores the analysis results (categories and metadata) in the Message model.

    Args:
        message_id: The email service's unique identifier for the message (e.g., Gmail ID).
        message_subject: The subject of the email.
        message_body: The body content of the email.
        sender_email: The email address of the sender.
    """
    logger.info(f"Starting email analysis task for message_id: {message_id}")
    try:
        # Initialize LLM - Ensure API key is available in the Celery worker environment
        # Using gemini-1.5-flash-latest as requested
        # Ensure correct model name as per availability/preference
        llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-lite",
            temperature=0,
            convert_system_message_to_human=True,
            google_api_key=settings.GOOGLE_GEMINI_API_KEY,
        )

        # Initialize Analyzer
        analyzer = EmailAnalyzer(llm=llm)

        # Analyze the email
        logger.info(f"Calling LLM for analysis on message_id: {message_id}")
        analysis_result: EmailAnalysisResult = analyzer.analyze_single(
            sender_email=sender_email, subject=message_subject, email_body=message_body
        )

        # Log successful analysis
        logger.info(f"Successfully analyzed message_id: {message_id}. Result: {analysis_result.dict()}")

        # --- Store the analysis result in the Message model ---
        try:
            with transaction.atomic():
                # Fetch the message within the transaction and lock it
                message = Message.objects.get(message_id=message_id)
                # Get or create MessageCategory objects
                category_objects = []
                category_names = [analysis_result.category_1, analysis_result.category_2]
                for name in category_names:
                    if name:  # Ensure category name is not empty
                        category, created = MessageCategory.objects.get_or_create(
                            name=name, defaults={"slug": slugify(name)}  # Generate slug for new categories
                        )
                        category_objects.append(category)
                        if created:
                            logger.info(f"Created new MessageCategory: {name}")
                # Associate categories with the message
                message.categories.set(category_objects)
                logger.info(f"Stored analysis results and categories for message_id: {message_id}")

        except Message.DoesNotExist:
            logger.error(f"Message with message_id: {message_id} not found in database for storing analysis results.")
            # No retry needed if message doesn't exist in our DB
            return
        except Exception as db_error:
            logger.error(
                f"Database error storing analysis results for message_id: {message_id}. Error: {db_error}",
                exc_info=True,
            )
            # Re-raise to let Celery handle retries for database issues
            raise db_error
        # --- End storing analysis result ---

    except KeyError as e:
        logger.error(f"Missing key during analysis setup for message_id: {message_id}. Error: {e}", exc_info=True)
        # Potentially retry if it's a transient setup issue
        raise  # Re-raise to let Celery handle retries based on task config
    except Exception as e:
        logger.error(f"Email analysis or LLM call failed for message_id: {message_id}. Error: {e}", exc_info=True)
        # Re-raise the exception to trigger Celery's retry mechanism for analysis failures
        raise


@app.task
def batch_analyze_email_message(user_mailbot_profile_id: int, message_ids: List[str]):
    """
    INTERNAL TASK — DO NOT CALL DIRECTLY.

    This task is meant to be scheduled **only through** the `process_messages_in_chunks` utility.
    It is not safe to call this task with a large list of message IDs directly because:
    - AWS SQS (used by Celery) has strict limits on message size.

    Use `process_messages_in_chunks(message_ids, profile_id)` to safely chunk and schedule this task.

    Args:
        user_mailbot_profile_id (int): ID of the user mailbot profile.
        message_ids (List[str]): List of message IDs (must be <= 2000 per task).
    """
    logger.info(f"Starting email analysis task for user profile: {user_mailbot_profile_id}")
    try:
        user_mailbot_profile = UserMailBotProfile.objects.get(id=user_mailbot_profile_id)
        service = MessageServiceFactory.get_message_service(user_mailbot_profile)
        messages_to_process, messages_to_requeue = message_ids[:50], message_ids[50:]
        parsed_messages = service.get_messages(messages_to_process)
        message_details = []
        for message in parsed_messages:
            _, sender_email = message.from_name_email
            message_details.append((message.message_id, sender_email, message.subject, message.text_body))

        # Initialize LLM - Ensure API key is available in the Celery worker environment
        # Using gemini-1.5-flash-latest as requested
        # Ensure correct model name as per availability/preference
        llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash-lite", temperature=0, convert_system_message_to_human=True)
        # Initialize Analyzer
        analyzer = EmailAnalyzer(llm=llm)
        # Analyze the email
        analysis_result: Dict[str, EmailAnalysisResult] = analyzer.analyze_batch(message_details)

        # Log successful analysis
        logger.info(f"Successfully analyzed {len(analysis_result)}messages for user profile {user_mailbot_profile_id}")

        # --- Store the analysis result in the Message model ---
        try:
            for message_id, result in analysis_result.items():
                with transaction.atomic():
                    # Fetch the message within the transaction and lock it
                    message = Message.objects.get(message_id=message_id)
                    # Get or create MessageCategory objects
                    category_objects = []
                    category_names = [result.category_1, result.category_2]
                    for name in category_names:
                        if name:  # Ensure category name is not empty
                            category, created = MessageCategory.objects.get_or_create(
                                name=name, defaults={"slug": slugify(name)}  # Generate slug for new categories
                            )
                            category_objects.append(category)
                            if created:
                                logger.info(f"Created new MessageCategory: {name}")
                    # Associate categories with the message
                    message.categories.set(category_objects)
                    logger.info(f"Stored analysis results and categories for message_id: {message_id}")
            if messages_to_requeue:
                batch_analyze_email_message.delay(user_mailbot_profile_id, messages_to_requeue)
        except Message.DoesNotExist:
            logger.error(f"Message with message_id: {message_id} not found in database for storing analysis results.")
            # No retry needed if message doesn't exist in our DB
            return
        except Exception as db_error:
            logger.error(
                f"Database error storing analysis results for message_id: {message_id}. Error: {db_error}",
                exc_info=True,
            )
            # Re-raise to let Celery handle retries for database issues
            raise db_error

    except Exception as e:
        logger.error(
            f"Email analysis or LLM call failed for user profile : {user_mailbot_profile_id}. Error: {e}", exc_info=True
        )
        # Re-raise the exception to trigger Celery's retry mechanism for analysis failures
        raise
