import logging

from django.db.models import QuerySet
from django.utils import timezone

from execfn.common.utils.datetime import STANDARD_DATETIME_FORMAT
from jarvis.utils.constraints import BaseConstraint
from mailbot.models import Message
from mailbot.service.factory import MessageServiceFactory

logger = logging.getLogger(__name__)


class SubjectFilterConstraint(BaseConstraint):
    tag = "filter_email_by_subject_constraint"
    schema = {
        "type": "object",
        "properties": {
            "keywords": {
                "description": "List of keywords to look up in the email subjects",
                "type": "array",
                "items": {"type": "string"},
            }
        },
    }

    def evaluate(self, payload):
        keywords = self.params.get("keywords", [])
        if not keywords:
            return False, payload
        if isinstance(payload, QuerySet) and payload.model == Message:
            queryset = Message.objects.none()
            for keyword in keywords:
                queryset |= payload.filter(metadata__subject__icontains=keyword)
            result = (queryset.exists(), queryset)
        elif isinstance(payload, Message):
            has_keyword_in_subject = all(keyword.lower() in payload.subject.lower() for keyword in keywords)
            result = (has_keyword_in_subject, payload)
        else:
            raise NotImplementedError("Invalid payload type.")
        return result


class BodyFilterConstraint(BaseConstraint):
    tag = "filter_email_by_body_constraint"
    schema = {
        "type": "object",
        "properties": {
            "keywords": {
                "description": "List of keywords to look up in the email body",
                "type": "array",
                "items": {"type": "string"},
            }
        },
    }

    def evaluate(self, payload):
        keywords = self.params.get("keywords", [])
        if not keywords:
            return False, payload
        if isinstance(payload, Message):
            user_mailbot_profile = payload.user_mailbot_profile
            service = MessageServiceFactory.get_message_service(user_mailbot_profile)
            message_body = service.get_message_body(payload.message_id, html=False)
            has_keyword_in_body = all(keyword.lower() in message_body.lower() for keyword in keywords)
            result = (has_keyword_in_body, payload)
        else:
            raise NotImplementedError("Invalid payload type.")
        return result


class SenderFilterConstraint(BaseConstraint):
    tag = "filter_email_by_sender_constraint"
    schema = {
        "type": "object",
        "properties": {
            "email": {"description": "Email of the sender to filter by", "type": "string"},
            "domain": {"description": "Domain of the sender to filter by", "type": "string"},
        },
    }

    def evaluate(self, payload):
        sender_email = self.params.get("email")
        sender_domain = self.params.get("domain")
        if isinstance(payload, QuerySet) and payload.model == Message:
            if sender_email:
                payload = payload.filter(metadata__from__1__iexact=sender_email)
            if sender_domain:
                payload = payload.filter(metadata__from__1__icontains=sender_domain)
            result = (payload.exists(), payload)
        elif isinstance(payload, Message):
            from_header = payload.metadata["from"][1].lower()
            match = False
            if sender_email:
                match |= from_header == sender_email.lower()
            if sender_domain:
                match |= from_header.endswith(sender_domain.lower())
            result = (match, payload)
        else:
            raise NotImplementedError("Invalid payload type.")
        return result


class HasAttachmentConstraint(BaseConstraint):
    tag = "filter_email_by_attachment_constraint"
    schema = {
        "type": "object",
        "properties": {"has_attachment": {"description": "Check if the email has an attachment", "type": "boolean"}},
    }

    def evaluate(self, payload):
        if isinstance(payload, QuerySet) and payload.model == Message:
            queryset = payload.filter(metadata__attachments_count__gt=0)
            result = (queryset.exists(), queryset)
        elif isinstance(payload, Message):
            result = (payload.metadata.get("attachments_count", 0) != 0, payload)
        else:
            raise NotImplementedError("Invalid payload type.")
        return result


class EmailReceivedDateTimeConstraint(BaseConstraint):
    tag = "filter_email_by_received_datetime_constraint"
    schema = {
        "type": "object",
        "properties": {
            "on_date_time": {
                "description": 'Email received on this date (format: "%Y-%m-%dT%H:%M:%S.%fZ")',
                "type": "string",
                "format": "date-time",
            },
            "before_date_time": {
                "description": 'Email received before this date (format: "%Y-%m-%dT%H:%M:%S.%fZ")',
                "type": "string",
                "format": "date-time",
            },
            "after_date_time": {
                "description": 'Email received after this date (format: "%Y-%m-%dT%H:%M:%S.%fZ")',
                "type": "string",
                "format": "date-time",
            },
            "last_week": {
                "description": "Filter emails from last week. This must apply if a periodic workflow is recurring every week.",
                "type": "boolean",
            },
            "last_month": {
                "description": "Filter emails from last month. This must apply if a periodic workflow is recurring every month.",
                "type": "boolean",
            },
            "last_day": {
                "description": "Filter emails from last one day. This must apply if a periodic workflow is recurring every day.",
                "type": "boolean",
            },
        },
    }
    ui_schema = {
        "type": "VerticalLayout",
        "elements": [
            {
                "type": "Control",
                "scope": "#/properties/on_date_time",
                "options": {
                    "ampm": True,
                    "format": "date-time",
                    "dateTimeFormat": "DD-MM-YYYY hh:mm A",
                    "dateTimeSaveFormat": "YYYY-MM-DDTHH:mm:ssZ",
                },
            },
            {
                "type": "Control",
                "scope": "#/properties/before_date_time",
                "options": {
                    "ampm": True,
                    "format": "date-time",
                    "dateTimeFormat": "DD-MM-YYYY hh:mm A",
                    "dateTimeSaveFormat": "YYYY-MM-DDTHH:mm:ssZ",
                },
            },
            {
                "type": "Control",
                "scope": "#/properties/after_date_time",
                "options": {
                    "ampm": True,
                    "format": "date-time",
                    "dateTimeFormat": "DD-MM-YYYY hh:mm A",
                    "dateTimeSaveFormat": "YYYY-MM-DDTHH:mm:ssZ",
                },
            },
            {"type": "Control", "scope": "#/properties/last_week"},
            {"type": "Control", "scope": "#/properties/last_month"},
            {"type": "Control", "scope": "#/properties/last_day"},
        ],
    }

    def evaluate(self, payload):
        # TODO : This constraint takes a brute force approach to filter out the payload by datetime. We need to make
        #  this generic
        if before := self.params.get("before_date_time"):
            before = timezone.datetime.strptime(before, STANDARD_DATETIME_FORMAT)
        if after := self.params.get("after_date_time"):
            after = timezone.datetime.strptime(after, STANDARD_DATETIME_FORMAT)
        if on := self.params.get("on_date_time"):
            on = timezone.datetime.strptime(on, STANDARD_DATETIME_FORMAT)
        today = timezone.now()
        if self.params.get("last_week"):
            before = today.isoformat()
            after = (today - timezone.timedelta(days=7)).isoformat()
        if self.params.get("last_month"):
            before = today.isoformat()
            after = (today - timezone.timedelta(days=30)).isoformat()
        if self.params.get("last_day"):
            before = today.isoformat()
            after = (today - timezone.timedelta(days=1)).isoformat()
        if not ((isinstance(payload, QuerySet) and payload.model == Message) or isinstance(payload, Message)):
            raise NotImplementedError("Invalid payload type.")
        if before or after:
            result = self.check_before_or_after(payload, before, after)
        elif on:
            result = self.check_specific_time(payload, on)
        else:
            result = False, payload
        return result

    def check_before_or_after(self, payload, before, after):  # noqa
        if isinstance(payload, QuerySet) and payload.model == Message:
            queryset = Message.objects.none()
            if before and after:
                queryset |= payload.filter(received_at__lte=before, received_at__gte=after)
            elif before:
                queryset |= payload.filter(received_at__lte=before)
            elif after:
                queryset |= payload.filter(received_at__gte=after)
            return queryset.exists(), queryset
        elif isinstance(payload, Message):
            if before and after:
                return after <= payload.received_at <= before, payload
            elif before:
                return payload.received_at <= before, payload
            elif after:
                return payload.received_at >= after, payload
        else:
            raise NotImplementedError("Invalid payload type.")

    def check_specific_time(self, payload, on):  # noqa
        if isinstance(payload, QuerySet) and payload.model == Message:
            queryset = Message.objects.none()
            queryset |= payload.filter(received_at=on)
            return queryset.exists(), queryset
        elif isinstance(payload, Message):
            return payload.received_at <= on, payload
        else:
            raise NotImplementedError("Invalid payload type.")


class EmailRecipientTypeConstraint(BaseConstraint):
    tag = "filter_email_by_recipient_type_constraint"
    schema = {
        "type": "object",
        "properties": {
            "recipient_type": {
                "description": "Type of recipient (to, cc, bcc)",
                "type": "string",
                "enum": ["to", "cc", "bcc"],
                "default": None,
            }
        },
    }

    def evaluate(self, payload):
        if not (recipient_type := self.params.get("recipient_type")):
            return False, payload
        user_email = self.user.email.lower()
        if isinstance(payload, QuerySet) and payload.model == Message:
            queryset = Message.objects.none()
            query_filter = {"metadata__icontains": {recipient_type: [["", user_email]]}}
            queryset |= payload.filter(**query_filter)
            result = queryset.exists, queryset
        elif isinstance(payload, Message):
            interim_result = False
            parsed_emails = [x[1] for x in payload.metadata[recipient_type]]
            interim_result = interim_result or user_email in parsed_emails
            result = interim_result, payload
        else:
            raise NotImplementedError("Invalid payload value.")
        return result
