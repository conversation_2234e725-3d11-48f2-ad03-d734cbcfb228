import logging
from abc import ABC, abstractmethod
from functools import lru_cache
from typing import Any, Callable, Dict, Iterable, List, Optional, Sequence

from django.contrib.auth import get_user_model
from django.db.models.query import QuerySet

from applications.utils.email import get_normalized_email
from mailbot.models import Message, SenderProfile
from mailbot.utils import database
from mailbot.utils.defaults import MailBotProfileMetadataKey
from mailbot.utils.email import get_mailbot_sent_mail_headers
from mailbot.utils.message_parser import ParsedMessage

User = get_user_model()
logger = logging.getLogger(__name__)


class BaseMessageService(ABC):
    """
    Base class defining algorithms for emailzap and defer specific steps to derived classes.
    """

    def __init__(self, user_mailbot_profile):
        self.user_mailbot_profile = user_mailbot_profile
        self.user = user_mailbot_profile.user
        self.email = self.user.email
        self.mailbot_labels = None
        self.label_name_lookup = None
        self.sender_stats_enabled_labels = None

    @property
    @abstractmethod
    def default_fields_to_select(self) -> Iterable[str]:
        """
        Default fields to select while fetching message from API.
        """
        pass

    @abstractmethod
    def sync_with_database(self, message: ParsedMessage) -> Callable[[Any], Message]:
        """Maps the message response from API for current service provider to `Message` model.

        Args:
            message(ParsedMessage): Parsed message response from API.

        Returns:
            Callable[[Any], Message]: Function mapping the response from API.
        """
        pass

    @abstractmethod
    def get_label_mappings(self) -> Dict[str, str]:
        """Generate label mappings from label_id to label_name using API and return the dictionary.

        Returns:
            Dict[str, str]: Mapping from label_id to label_name.
        """
        pass

    @abstractmethod
    def create_label(self, label_name: str) -> Optional[str]:
        """Create label with the given name in the user's mailbox if not already present.
        Check from label mappings if the label exists already before creating it.

        Args:
            label_name (str): label name to create.
        Returns:
            Optional[str]: Label ID of label name if created successfully, else None.
        """
        pass

    @abstractmethod
    def delete_label(self, label_name):
        pass

    @abstractmethod
    def get_label_stats(self, label_name: str) -> dict:
        pass

    @abstractmethod
    def scan_messages(
        self,
        generic_label: str,
        limit: int = None,
        select_fields: List[str] = None,
        message_mapper=None,
        newer_than_hours=None,
        older_than_hours=None,
        **kwargs,
    ) -> Iterable[Any]:
        """Scan the folder messages.

        Args:
            generic_label (str): Generic name of the label to scan that must be present in `label_name_lookup` keys.
            limit (int, optional): Number of messages to scan. Defaults to all messages.
            select_fields (List[str], optional): Only retrieve these fields from the API. Defaults to all fields.
            message_mapper (Callable, optional): After retrieving response from API, transform using this callable. Defaults to identity mapper.
            newer_than_hours (int, optional): Only retrieve messages newer than this many hours. Defaults to None.
            older_than_hours (int, optional): Only retrieve messages older than this many hours. Defaults to None.
            kwargs: Additional keyword arguments to pass to the underlying function.

        Returns:
            Iterable[Any]: Iterable over messages.
        """
        pass

    @abstractmethod
    def get_message(self, message_id: str, select_fields: List[str]) -> ParsedMessage:
        """Get message from API.

        Args:
            message_id (str): Message ID for which to retrieve message.
            select_fields: Extra fields to select while fetching message.

        Returns:
            Dict[str, Any]: Message response from API.
        """
        pass

    @abstractmethod
    def get_messages(self, message_ids: Iterable[str], select_fields: List[str] = None) -> List[ParsedMessage]:
        """Get messages from API.

        Args:
            message_ids: Message IDs for which to retrieve messages.
            select_fields: Extra fields to select while fetching messages.

        Returns:
            Dict[str, Any]: Messages response from API.
        """
        pass

    @abstractmethod
    def get_message_body(self, message_id: str, html=False):
        pass

    def get_thread_messages(self, thread_id: str) -> QuerySet:
        """Get messages from database belonging to the given thread.

        Args:
            thread_id (str): Thread ID for which to fetch messages.

        Returns:
            QuerySet: Django queryset manager over `Message` objects
        """
        return Message.objects.filter(thread_id=thread_id, user_mailbot_profile=self.user_mailbot_profile)

    @abstractmethod
    def subscribe_watch_events(self):
        """Subscribe to watch events on current user's mailbox."""
        pass

    @abstractmethod
    def unsubscribe_watch_events(self):
        """Unsubscribe all subscriptions on mailbox for our application."""
        pass

    @abstractmethod
    def archive_message(self, message_id: str) -> bool:
        pass

    @abstractmethod
    def mark_read(self, message_id: str):
        pass

    @abstractmethod
    def mark_unread(self, message_id: str):
        pass

    @abstractmethod
    def trash_message(self, message_id: str):
        pass

    @abstractmethod
    def star_message(self, message_id: str):
        pass

    @abstractmethod
    def mark_spam(self, message_id: str):
        pass

    @abstractmethod
    def move_to_label(self, message_id: str, destination_label_name: str) -> bool:
        """Move message to specific label using API.
        Args:
            message_id (str): Message which is to be moved.
            destination_label_name (str): Actual label name to which message is to be moved.
        Returns:
            bool: Whether message is successfully moved to destination label.
        """
        pass

    @abstractmethod
    def add_custom_labels(self, message_id: str, label_names: str):
        pass

    def move_to_known_label(self, message_id: str, *, generic_label: str) -> bool:
        """Move message to specific label using API.

        Args:
            message_id (str): Message which is to be moved.
            generic_label (str): Generic label name to which message is to be moved.
        Returns:
            bool: Whether message is successfully moved to destination label.
        """
        label_name = self.label_name_lookup.get(generic_label)
        if not label_name:
            logger.exception(
                "No label name corresponding to know-label found.",
                extra={"user": self.email, "generic_label": generic_label},
            )
            return False
        return self.move_to_label(message_id, destination_label_name=label_name)

    # Label specific functionalities
    def sync_label_mappings(self):
        """Fetch label mappings from API and save in the database."""
        label_mappings = self.get_label_mappings()
        logger.info(f"updating database with latest label_mappings from API for user {self.email}")
        self.user_mailbot_profile.label_mappings = label_mappings
        self.user_mailbot_profile.save(update_fields=["label_mappings"])

    @lru_cache(maxsize=16)
    def get_label_id(self, label_name, synced=False) -> Optional[str]:
        """
        Find label ID from label mappings in database.
        If not found in database and synced is False then sync with API.
        Args:
            label_name: Label name for which to retrieve label ID.
            synced (bool): Whether label mappings should be synced with API in case label name not present in it.
        """
        label_mappings = self.user_mailbot_profile.label_mappings
        for label_id, label_name_ in label_mappings.items():
            if label_name == label_name_:
                return label_id
        if not synced:
            self.sync_label_mappings()
            return self.get_label_id(label_name, synced=True)
        return None

    def get_label_name(self, label_id: str, synced=False) -> Optional[str]:
        """Get label Name corresponding to given label ID using label mappings in database.

        Args:
            label_id (str): Label ID for which to retrieve label Name.
            synced (bool): Whether label mappings should be synced with API in case label id not present in it.

        Returns:
            str: Name of the label.
        """
        label_mappings = self.user_mailbot_profile.label_mappings
        label_name = label_mappings.get(label_id)
        if not label_name and not synced:
            self.sync_label_mappings()
            return self.get_label_name(label_id, synced=True)
        return label_name

    @abstractmethod
    def rename_label(self, old_label_name: str, new_label_name: str):
        pass

    def get_label_id_for_generic_label(self, generic_label: str) -> Optional[str]:
        """Get label Id corresponding to given label name using label mappings in database.

        Args:
            generic_label (str): Generic label name for which to retrieve label Id.

        Returns:
            str: Id of the label.
        """
        label_name = self.label_name_lookup.get(generic_label)
        if not label_name:
            logger.exception(
                "Label name not found in label_name_lookup", extra={"email": self.email, "generic_label": generic_label}
            )
            return None
        return self.get_label_id(label_name)

    def create_default_labels(self):
        """
        Create default labels for current user.
        """
        label_mappings = self.get_label_mappings()
        for label_to_create in self.mailbot_labels.values():
            if label_to_create not in label_mappings.values():
                logger.info(f"creating label {label_to_create} for user {self.email}")
                self.create_label(label_name=label_to_create)
        logger.info(f"created default emailzap labels for user {self.email}")

    @abstractmethod
    def get_auth_tokens(self):
        """Returns authentication tokens(access token, refresh token) if the mailbox is present"""
        pass

    @abstractmethod
    def get_messages_from_sender(self, sender_email: str):
        """
        Get messages from a sender email

        Args:
            sender_email: Email id of the sender whose messages have to be fetched

        Returns:
            List[str]: List of message ids from the sender
        """
        pass

    def trash_historical_messages(self, sender_email: str):
        """
        Moves historical messages to trash from a sender email

        Args:
            from_sender_email: Email id of the sender whose messages have to be moved to trash

        Returns:
            int: Number of messages trashed
        """
        try:
            message_ids = self.get_messages_from_sender(sender_email)
            logger.info(f"{len(message_ids)} Messages pulled for user for deletion from mailbox")
            return self.bulk_trash_message(message_ids)
        except Exception:
            logger.exception("failed to trash historical messages")
            return 0

    @abstractmethod
    def is_sender_stats_enabled(self, label_ids: Sequence[str]):
        pass

    @abstractmethod
    def get_generic_label(self, label_ids: Sequence[str]) -> str:
        pass

    def sync_new_message(self, parsed_message, generic_label):
        is_sender_stats_enabled = self.is_sender_stats_enabled(parsed_message.label_ids)
        sender_profile = database.sync_sender_profile_for_new_message(
            is_sender_stats_enabled=is_sender_stats_enabled,
            parsed_message=parsed_message,
        )
        message_metadata = {
            "message_headers": parsed_message.message_headers,
            "attachments_count": len(parsed_message.attachments),
            "subject": parsed_message.subject,
            "is_read": parsed_message.is_read,
            "from": parsed_message.from_name_email,
            "to": parsed_message.to_name_email,
            "cc": parsed_message.cc_name_email,
            "bcc": parsed_message.bcc_name_email,
            "label_ids": parsed_message.label_ids,
        }

        message_metadata.update(get_mailbot_sent_mail_headers(parsed_message.message_headers))
        logger.info(f"{parsed_message.message_id}: Creating new message in database")
        message = Message.objects.create(
            message_id=parsed_message.message_id,
            user_mailbot_profile=self.user_mailbot_profile,
            received_at=parsed_message.received_at,
            thread_id=parsed_message.thread_id,
            generic_label=generic_label,
            metadata=message_metadata,
            sender_profile=sender_profile,
            is_read=parsed_message.is_read,
        )

        # TODO: Refactor the code below to find a better place for email message analysis.
        # After onboarding is completed, analyze the email message. During onboarding, batch_analyze_email_message is already running,
        # so individual message analysis is performed only after onboarding.
        if not parsed_message.is_sent and self.user_mailbot_profile.metadata.get(
            MailBotProfileMetadataKey.ONBOARDING_COMPLETED.value
        ):
            from mailbot.tasks import analyze_email_message

            _, sender_email = parsed_message.from_name_email
            analyze_email_message.run(
                message_id=parsed_message.message_id,
                message_subject=parsed_message.subject,
                message_body=parsed_message.text_body,
                sender_email=sender_email,
            )
        return message

    @abstractmethod
    def get_attachments(self, parsed_message: ParsedMessage):
        pass

    @abstractmethod
    def get_attachment(self, *args, **kwargs):
        pass

    @abstractmethod
    def bulk_trash_message(self, message_ids):
        pass
