import logging
from typing import Any, Dict, List, Callable, Optional, Iterable, Sequence

import pytz
import requests.exceptions
from O365 import Account
from O365.mailbox import Folder
from O365.message import Message as OutlookMessage
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from oauthlib.oauth2 import InvalidGrantError
from requests.models import Response
from rest_framework.status import HTTP_404_NOT_FOUND, HTTP_400_BAD_REQUEST

from accounts.signals import refresh_token_expired
from applications.utils.base import batch_enumerate
from mailbot.models import Message, MailSubscription, MailBotGenericLabel
from mailbot.service.base import BaseMessageService
from mailbot.signals import post_email_read
from mailbot.utils import database
from mailbot.utils.defaults import (
    MICROSOFT_MAILBOT_REQUIRED_SCOPES,
    OutlookKnownLabelName,
    MICROSOFT_EXTENDED_PROPERTY_ID,
    MICROSOFT_EXTENDED_PROPERTY_NAME,
    OUTLOOK_LABELS,
)
from mailbot.utils.exceptions import LabelIdNotFoundError, MessageIdNotFoundError, SyncMessageWithDatabaseError
from mailbot.utils.message_parser import parse_outlook_message, ParsedOutlookMessage
from mailbot.utils.microsoft import DatabaseTokenBackend

User = get_user_model()
logger = logging.getLogger(__name__)


def invalid_grant_error_decorator(func):
    """
    Decorator for sending refresh token expired signal.
    Use this decorator on functions using O365 mailbox and ones using `requests` library for directly sending requests.
    Args:
        func: Function using API calls to Microsoft Graph API
    """

    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except InvalidGrantError:
            self = args[0]
            logger.info(f"Invalid GrantError for args {args} and kwargs {kwargs}")
            refresh_token_expired.send(sender=self.__class__, user_mailbot_profile_id=self.user_mailbot_profile.id)
            raise

    return wrapper


class OutlookService(BaseMessageService):
    default_fields_to_select = (
        "internetMessageHeaders",
        "receivedDateTime",
        "parentFolderId",
        "sender",
        "from",
        "isRead",
        "subject",
        "toRecipients",
        "ccRecipients",
        "bccRecipients",
        "conversationId",
        "body",
        "hasAttachments",
    )
    MAX_BATCH_SIZE = 20
    MAX_TOP_SIZE = 500

    # https://learn.microsoft.com/en-us/graph/change-notifications-overview#subscription-lifetime
    OUTLOOK_WATCH_SUBSCRIPTION_EXPIRE_IN_MINUTES = 4230

    def __init__(self, user_mailbot_profile):
        super().__init__(user_mailbot_profile=user_mailbot_profile)
        self.token_backend = DatabaseTokenBackend(user_mailbot_profile=user_mailbot_profile)
        client_id = settings.MICROSOFT["app_id"]
        client_secret = settings.MICROSOFT["app_secret"]
        self.account = Account(
            credentials=(client_id, client_secret),
            token_backend=self.token_backend,
            default_headers={"Prefer": "IdType='ImmutableId'"},
            scopes=MICROSOFT_MAILBOT_REQUIRED_SCOPES,
            timezone=pytz.utc,
            raise_http_errors=False,
        )
        self.mailbox = self.account.mailbox()
        self.mailbot_labels = {
            MailBotGenericLabel.WHITE_LIST.value: OUTLOOK_LABELS[MailBotGenericLabel.WHITE_LIST.value],
            MailBotGenericLabel.ZAPPED.value: OUTLOOK_LABELS[MailBotGenericLabel.ZAPPED.value],
        }
        self.sender_stats_enabled_labels = [
            MailBotGenericLabel.WHITE_LIST.value,
            MailBotGenericLabel.ZAPPED.value,
            Message.GENERIC_LABEL_SENT,
        ]
        self.label_name_lookup = {
            Message.GENERIC_LABEL_SENT: OutlookKnownLabelName.SENT.value,
            Message.GENERIC_LABEL_TRASH: OutlookKnownLabelName.TRASH.value,
            Message.GENERIC_LABEL_ARCHIVE: OutlookKnownLabelName.ARCHIVE.value,
            Message.GENERIC_LABEL_SPAM: OutlookKnownLabelName.SPAM.value,
            **self.mailbot_labels,
        }

    @invalid_grant_error_decorator
    def _make_authenticated_request(self, endpoint, method, beta=False, **kwargs) -> Optional[Response]:
        """
        Makes a request to url using an oauth session.

        Args:
            endpoint: URL to which sending request
            method: Type of request (get/put/post/patch/delete)
            kwargs: Extra params to send to the request API
        Returns:
            Optional[requests.models.Response]: Server's response to an HTTP request if successful
        """
        if beta:
            base_url = settings.BASE_MICROSOFT_GRAPH_BETA_URL
        else:
            base_url = settings.BASE_MICROSOFT_GRAPH_URL
        response: Response = self.account.connection.oauth_request(
            url=f"{base_url}/{endpoint}", method=method, **kwargs
        )
        response.raise_for_status()
        return response

    def _make_authenticated_batch_request(self, batch_requests: List[Dict[str, Any]]):
        """
        Make batch requests to Microsoft Graph API.

        Args:
            batch_requests: List of requests

        References:
            https://learn.microsoft.com/en-us/graph/json-batching?view=graph-rest-1.0
            https://learn.microsoft.com/en-us/graph/throttling-limits#outlook-service-limits

        Notes:
            The status code on a batch response is typically 200 or 400.
            If the batch request itself is malformed, the status code is 400.
            If the batch request is parseable, the status code is 200.
            A 200 status code on the batch response does not indicate that the individual requests inside the batch succeeded.
            This is why each individual response in the responses property has a status code.
        """
        # Assign id to associate individual responses with requests
        success_request_ids = []
        success_responses = []

        for i in range(len(batch_requests)):
            batch_requests[i].update(
                {
                    "id": str(i),
                    "headers": {"Content-Type": "application/json"},
                }
            )
        for batch_num, batch_range in batch_enumerate(
            sequence_length=len(batch_requests), batch_size=self.MAX_BATCH_SIZE
        ):
            try:
                response = self._make_authenticated_request(
                    endpoint="$batch",
                    method="post",
                    data={"requests": batch_requests[batch_range]},
                )
                response.raise_for_status()
            except requests.exceptions.HTTPError:
                logger.exception("whole batch request failed for Microsoft Graph API")
            else:
                for message_response in response.json().get("responses", []):
                    if 200 <= message_response["status"] < 300:
                        # The id we assigned (index of the request)
                        response_id = message_response.pop("id")
                        success_request_ids.append(int(response_id))
                        success_responses.append(message_response)
                    else:
                        logger.exception(
                            "single request in batch failed for Microsoft Graph API",
                            extra={
                                "message_response": message_response,
                                "email": self.email,
                            },
                        )
        return success_request_ids, success_responses

    def get_generic_label(self, label_ids: Sequence[str]) -> str:
        assert len(label_ids) == 1
        folder_id = label_ids[0]
        folder_name = self.user_mailbot_profile.label_mappings.get(folder_id)
        if folder_name == OutlookKnownLabelName.TRASH.value:
            return Message.GENERIC_LABEL_TRASH
        elif folder_name == OutlookKnownLabelName.SENT.value:
            return Message.GENERIC_LABEL_SENT
        generic_label = None
        for k, v in self.mailbot_labels.items():
            if self.get_label_id(v) in label_ids:
                generic_label = k
                break
        if not generic_label:
            generic_label = Message.GENERIC_LABEL_ARCHIVE
        return generic_label

    def is_sender_stats_enabled(self, label_ids: Sequence[str]):
        return self.get_generic_label(label_ids) in self.sender_stats_enabled_labels

    def sync_existing_message(self, db_message, parsed_message, generic_label):
        if parsed_message.is_read == db_message.is_read and generic_label == db_message.generic_label:
            logger.info(f"Message {db_message.message_id} already synced with database")
            return db_message
        was_read = db_message.is_read
        is_sender_stats_enabled = generic_label in self.sender_stats_enabled_labels
        was_sender_stats_enabled = db_message.generic_label in self.sender_stats_enabled_labels
        database.sync_sender_profile_for_existing_message(
            parsed_message=parsed_message,
            was_read=was_read,
            is_sender_stats_enabled=is_sender_stats_enabled,
            was_sender_stats_enabled=was_sender_stats_enabled,
        )
        db_message.generic_label = generic_label
        db_message.metadata["is_read"] = parsed_message.is_read
        db_message.is_read = parsed_message.is_read
        db_message.save()
        if not was_read and parsed_message.is_read:
            post_email_read.send(sender=self.__class__, message=db_message)
        return db_message

    def sync_with_database(self, parsed_message: ParsedOutlookMessage) -> Callable[[Any], Message]:
        def mapper(**kwargs):
            """
            Sync API response with SenderProfile, Message in database.

            Args:
                **kwargs: Extra information such as generic label.
            Returns:
                Message: Message object
            """
            folder_id = parsed_message.label_ids[0]
            folder_name = self.user_mailbot_profile.label_mappings.get(folder_id)
            if not folder_name:
                folder = self.mailbox.get_folder(folder_id=folder_id)
                folder_name = folder.name
                logger.info(f"Syncing mapping for folder {folder_name} for user {self.email}")
                self.user_mailbot_profile.label_mappings[folder_id] = folder_name
                self.user_mailbot_profile.save(update_fields=["label_mappings"])
            generic_label = kwargs.get("generic_label")
            if not generic_label or self.label_name_lookup.get(generic_label) != folder_name:
                for generic_label_name, actual_label_name in self.label_name_lookup.items():
                    if actual_label_name == folder_name:
                        generic_label = generic_label_name
                        break
            # Generic label could be None
            try:
                db_message = Message.objects.get(message_id=parsed_message.message_id)
            except Message.DoesNotExist:
                return self.sync_new_message(parsed_message, generic_label)
            else:
                return self.sync_existing_message(db_message, parsed_message, generic_label)

        return mapper

    @invalid_grant_error_decorator
    def get_label_mappings(self):
        """
        Get label mappings from Outlook API.
        Returns:
            Dict[str, str]: Mappings from label id to label name.
        """
        folders: List[Folder] = self.mailbox.get_folders()
        label_mappings = {}
        for folder in folders:
            label_mappings[folder.folder_id] = folder.name
        return label_mappings

    @invalid_grant_error_decorator
    def create_label(self, label_name: str) -> Optional[str]:
        """Create label with the given name in the user's mailbox if not already present.
        Check from label mappings if the label exists already before creating it.

        Args:
            label_name (str): label name to create.
        """
        folder: Folder = self.mailbox.create_child_folder(folder_name=label_name)
        if folder:
            return folder.folder_id
        return None

    @invalid_grant_error_decorator
    def delete_label(self, label_name):
        self.mailbox.get_folder(folder_name=label_name).delete()
        if (label_id := self.get_label_id(label_name)) and label_id in self.user_mailbot_profile.label_mappings:
            del self.user_mailbot_profile.label_mappings[label_id]
            self.user_mailbot_profile.save(update_fields=["label_mappings"])
        else:
            logger.info(f"Folder {label_name} not found for user {self.email} for deletion")

    @invalid_grant_error_decorator
    def get_label_stats(self, label_name: str):
        """
        Get the stats for the label from the API.

        Args:
            label_name: Label name for which to get stats.
        Returns:
            Dict: Stats for the label.
        """
        folder = self.mailbox.get_folder(folder_name=label_name)
        return {
            "total_messages": folder.total_items_count,
            "unread_messages": folder.unread_items_count,
        }

    @invalid_grant_error_decorator
    def rename_label(self, old_label_name, new_label_name):
        """
        Rename the label in the user's mailbox.

        Args:
            old_label_name: Old label name to rename.
            new_label_name: New label name to rename.
        """
        folder: Folder = self.mailbox.get_folder(folder_name=old_label_name)
        updated = folder.update_folder_name(new_label_name, update_folder_data=False)
        if updated:
            self.user_mailbot_profile.label_mappings[folder.folder_id] = new_label_name
            self.user_mailbot_profile.save(update_fields=["label_mappings"])

    @invalid_grant_error_decorator
    def scan_messages(
        self,
        generic_label: str,
        limit: int = None,
        select_fields: List[str] = None,
        message_mapper=None,
        newer_than_hours=None,
        older_than_hours=None,
        **kwargs,
    ) -> Iterable[Any]:
        """Scan the folder messages.

        Args:
            generic_label (str): Generic name of the label to scan that must be present in `label_name_lookup` keys.
            limit (int, optional): Number of messages to scan. Defaults to all messages.
            select_fields (List[str], optional): Only retrieve these fields from the API. Defaults to all fields.
            message_mapper (Callable, optional): After retrieving response from API, transform using this callable. Defaults to identity mapper.
            newer_than_hours (int, optional): Only scan messages newer than this many hours. Defaults to None.
            older_than_hours (int, optional): Only scan messages older than this many hours. Defaults to None.

        Returns:
            Iterable[Any]: Iterable over messages.
        """
        label_name = self.label_name_lookup.get(generic_label)
        if not label_name:
            logger.exception(
                "Skipped scanning of label as it is not found in label_name_lookup",
                extra={"email": self.email, "generic_label": generic_label},
            )
            return []
        if select_fields is None:
            select_fields = []
        logger.info(f"Fetching messages for label {label_name} for user {self.email}")
        q = self.mailbox.new_query()
        q.select(*self.default_fields_to_select, *select_fields)
        if newer_than_hours:
            q.chain("and").on_attribute("received_date_time").greater(
                timezone.now() - timezone.timedelta(hours=newer_than_hours)
            )
        if older_than_hours:
            q.chain("and").on_attribute("received_date_time").less(
                timezone.now() - timezone.timedelta(hours=older_than_hours)
            )
        folder: Folder = self.mailbox.get_folder(folder_name=label_name)
        scanned_messages = []
        unhandled_errors = []
        db_sync_errors = []
        batch_size = 100
        messages = folder.get_messages(limit=limit, query=q, download_attachments=True, batch=batch_size)
        idx = 0
        while True:
            batch = []
            for _ in range(batch_size):
                try:
                    batch.append(next(messages))
                except StopIteration:
                    break
            if not batch:
                logger.info(f"No more messages for label {label_name} for user {self.email}")
                break
            parsed_messages = [parse_outlook_message(self.user_mailbot_profile, x) for x in batch]
            for message in parsed_messages:
                # Don't interrupt onboarding due to error scanning in single message
                try:
                    scanned_message = message_mapper(message)() if message_mapper else message
                except SyncMessageWithDatabaseError as e:
                    db_sync_errors.append(str(e))
                except Exception as e:
                    unhandled_errors.append(str(e))
                else:
                    scanned_messages.append(scanned_message)
            idx += len(batch)
            if limit and idx >= limit:
                logger.info(f"Reached limit of {limit} messages for label {label_name} for user {self.email}")
                break
            elif len(batch) < batch_size:
                logger.info(f"No more messages for label {label_name} for user {self.email}")
                break
        if unhandled_errors:
            logger.error(
                "Unhandled errors while scanning messages during onboarding", extra={"errors": unhandled_errors}
            )
        if db_sync_errors:
            logger.error(
                "Database sync errors while scanning messages during onboarding", extra={"errors": db_sync_errors}
            )
        return scanned_messages

    def get_messages(self, message_ids: Iterable[str], select_fields: List[str] = None) -> List[ParsedOutlookMessage]:
        if not select_fields:
            select_fields = []
        batch_requests = [
            {
                "method": "GET",
                "url": f"/me/messages/{message_id}",
                "body": {
                    "$select": ",".join([*self.default_fields_to_select, *select_fields]),
                    "$expand": f"singleValueExtendedProperties($filter=id eq 'String {MICROSOFT_EXTENDED_PROPERTY_ID} Name {MICROSOFT_EXTENDED_PROPERTY_NAME}')",
                },
            }
            for message_id in message_ids
        ]
        success_request_ids, success_responses = self._make_authenticated_batch_request(batch_requests)
        return [
            parse_outlook_message(
                message=OutlookMessage(
                    parent=self.mailbox.get_folder(folder_id=response["body"]["parentFolderId"]),
                    download_attachments=True,
                    __cloud_data__=response["body"],
                ),
                user_mailbot_profile=self.user_mailbot_profile,
            )
            for response in success_responses
        ]

    def get_message(self, message_id: str, select_fields: List[str] = None) -> ParsedOutlookMessage:
        """
        Get single message from API for specified message Id.

        Args:
            message_id: Message Id for which to fetch message.
            select_fields: Extra fields to select while fetching message.

        Returns:
            Dict[str, str]: Message response from API.
        """
        if not select_fields:
            select_fields = []
        response = self._make_authenticated_request(
            endpoint=f"me/messages/{message_id}",
            method="get",
            params={
                "$select": ",".join([*self.default_fields_to_select, *select_fields]),
                "$expand": f"singleValueExtendedProperties($filter=id eq 'String {MICROSOFT_EXTENDED_PROPERTY_ID} Name {MICROSOFT_EXTENDED_PROPERTY_NAME}')",
            },
        )
        data = response.json()
        folder: Folder = self.mailbox.get_folder(folder_id=data["parentFolderId"])
        return parse_outlook_message(
            user_mailbot_profile=self.user_mailbot_profile,
            message=OutlookMessage(parent=folder, download_attachments=True, __cloud_data__=data),
        )

    def get_message_body(self, message_id: str, html=False):
        """
        Get message body for specified message id.

        Args:
            message_id (str): Message ID for which to fetch body.
            html (bool, optional): Whether to return html body or text body. Defaults to False (text body).

        Returns:
            str: Message body.
        """
        response = self.get_message(message_id=message_id)
        if not html:
            return response.text_body
        else:
            return response.html_body

    def archive_message(self, message_id: str) -> bool:
        """
        Move the message to the archive folder.

        Args:
            message_id: Message ID of message to archive.
        Returns:
            bool: Whether the message is moved to archive folder or not.
        """
        return self.move_to_known_label(message_id, generic_label=Message.GENERIC_LABEL_ARCHIVE)

    @invalid_grant_error_decorator
    def mark_read(self, message_id: str):
        """Mark message as read.

        Args:
            message_id: Message ID of message to mark as read.
        Returns:
            bool: Whether message is marked as read or not.
        """
        self.account.mailbox().get_message(object_id=message_id).mark_as_read()

    @invalid_grant_error_decorator
    def mark_unread(self, message_id: str):
        """Mark message as unread.

        Args:
            message_id (str): Message ID of message to mark as unread.
        """
        self.account.mailbox().get_message(object_id=message_id).mark_as_unread()

    def add_extended_property(self, message_id: str, destination_label_name: str) -> bool:
        """
        Whenever we move message to another folder, we will add extended property to it.
        This process is used to make 100% accurate decision whether message is moved by bot or user.

        Args:
            message_id: Message ID to which we add extended property.
            destination_label_name: Folder to which bot is moving this message.
        Returns:
            bool: Whether extended property is added to message successfully.
        References:
            https://learn.microsoft.com/en-us/graph/api/resources/extended-properties-overview?view=graph-rest-1.0
            https://learn.microsoft.com/en-us/graph/api/singlevaluelegacyextendedproperty-post-singlevalueextendedproperties?view=graph-rest-1.0&tabs=http
        """

        response = self._make_authenticated_request(
            endpoint=f"me/messages/{message_id}",
            method="patch",
            data={
                "singleValueExtendedProperties": [
                    {
                        "id": f"String {MICROSOFT_EXTENDED_PROPERTY_ID} Name {MICROSOFT_EXTENDED_PROPERTY_NAME}",
                        "value": destination_label_name,
                    }
                ]
            },
        )
        if not response:
            logger.info("HTTP error while adding extended property to message")
            return False
        else:
            return True

    def http_move_label(self, message_id, destination_label_id):
        """Move message to specific label using API.

        Args:
            message_id (str): Message which is to be moved.
            destination_label_id (str): Label id to which message is to be moved.

        Returns:
            bool: Whether message is successfully moved to destination label.
        """
        response = self._make_authenticated_request(
            endpoint=f"me/messages/{message_id}/move",
            method="post",
            data={"destinationId": destination_label_id},
        )
        if not response:
            logger.info(f"Moving mail failed for outlook, label_id {destination_label_id}, message_id {message_id}")
            return False
        else:
            if response.status_code == 201:
                return True
            else:
                response_body = response.json()
                if response_error := response_body.get("error", {}):
                    code = response_error.get("code")
                    reason = response_error.get("message")
                    if code == "ErrorToFolderNotFound":
                        raise LabelIdNotFoundError(
                            reason=reason,
                            status_code=HTTP_404_NOT_FOUND,
                            message_id=message_id,
                            added_label_ids=[destination_label_id],
                            removed_label_ids=None,
                        )
                    elif code == "ErrorInvalidIdMalformed":
                        raise MessageIdNotFoundError(
                            message_id=message_id, status_code=HTTP_400_BAD_REQUEST, reason=reason
                        )

            return False

    def move_to_known_label(self, message_id: str, *, generic_label: str) -> bool:
        """Move message to specific label using API.

        Args:
            message_id (str): Message which is to be moved.
            generic_label (str): Generic label name to which message is to be moved.
        Returns:
            bool: Whether message is successfully moved to destination label.
        """
        label_name = self.label_name_lookup.get(generic_label)
        if not label_name:
            logger.exception(
                "No label name corresponding to know-label", extra={"user": self.email, "generic_label": generic_label}
            )
            return False
        if not self.add_extended_property(message_id, destination_label_name=generic_label):
            logger.info("Move message failed, unable to add extended property")
            return False
        else:
            logger.info(f"Added extended property {generic_label} to {message_id}")
        return self.move_to_label(message_id, destination_label_name=label_name)

    def move_to_label(self, message_id: str, destination_label_name: str) -> bool:
        """Move message to specific label using API.

        Args:
            message_id (str): Message which is to be moved.
            destination_label_name (str): Label name to which message is to be moved.

        Returns:
            bool: Whether message is successfully moved to destination label.
        """
        logger.info(f"Moving message {message_id} to {destination_label_name}")
        destination_label_id = self.get_label_id(label_name=destination_label_name)
        if not destination_label_id:
            # Destination label is not present in user's mailbox and neither stored previously ever.
            logger.info(f"Creating folder {destination_label_name} for user {self.email} for first time")
            destination_label_id = self.create_label(label_name=destination_label_name)
            # Save the newly created label in database
            self.user_mailbot_profile.label_mappings[destination_label_id] = destination_label_name
            self.user_mailbot_profile.save(update_fields=["label_mappings"])
        moved = self.http_move_label(message_id, destination_label_id)
        if not moved:
            logger.info(f"Recreating folder {destination_label_name} for user {self.email} as it was deleted by user")
            # If label id is present in label mappings but user deleted the folder.
            if destination_label_id := self.create_label(label_name=destination_label_name):
                # Update the new label ID of `destination_label_name` in label mappings.
                self.sync_label_mappings()
                return self.http_move_label(message_id, destination_label_id)
            else:
                return False
        else:
            return True

    def subscribe_watch_events(self):
        """
        Subscribe to inbox, grey list, black list folders for created.
        """
        expiration_datetime = timezone.now() + timezone.timedelta(
            minutes=OutlookService.OUTLOOK_WATCH_SUBSCRIPTION_EXPIRE_IN_MINUTES
        )
        # We first have to unsubscribe old watch events
        self.unsubscribe_watch_events()
        data_to_subscribe = [
            {"label": MailBotGenericLabel.WHITE_LIST.value, "change_type": ["created", "updated", "deleted"]},
            {"label": MailBotGenericLabel.ZAPPED.value, "change_type": ["created", "updated", "deleted"]},
            {"label": Message.GENERIC_LABEL_SENT, "change_type": ["created"]},
        ]
        for data in data_to_subscribe:
            generic_label = data["label"]
            label_id = self.get_label_id_for_generic_label(generic_label=generic_label)
            subscription = {
                "changeType": ",".join(data.get("change_type")),
                "notificationUrl": f"{settings.BACKEND_BASE_URL}/api/v1/mailbot/outlook-webhook/",
                "resource": f"/me/mailFolders/{label_id}/messages",
                "expirationDateTime": expiration_datetime.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "clientState": settings.MICROSOFT.get("client_state", ""),
            }
            response = self._make_authenticated_request(endpoint="subscriptions", method="post", data=subscription)
            if not response:
                logger.info(f"HTTP Error while setting up outlook watch channel subscription for user {self.email}")
            else:
                subscription_id = response.json().get("id")
                if not subscription_id:
                    logger.error("No subscription id found in response while setting up outlook watch channel")
                MailSubscription.objects.update_or_create(
                    user_mailbot_profile=self.user_mailbot_profile,
                    label_name=generic_label,
                    defaults={"subscription_id": subscription_id, "expire_at": expiration_datetime},
                )
                logger.info(f"watch event subscribed for user {self.email}, label {generic_label}")

    def unsubscribe_watch_events(self):
        watch_events = self.get_watch_events()
        for watch_event in watch_events:
            if watch_event.get("applicationId") == settings.MICROSOFT.get("app_id"):
                try:
                    self._make_authenticated_request(endpoint=f"subscriptions/{watch_event.get('id')}", method="delete")
                    logger.info(f"unsubscribed user {self.email} from watch channel successfully")
                    MailSubscription.objects.filter(subscription_id=watch_event.get("id")).delete()
                except requests.exceptions.HTTPError as e:
                    if e.response.status_code == 404:
                        logger.info(f"watch event already deleted for user {self.email}")
                        MailSubscription.objects.filter(subscription_id=watch_event.get("id")).delete()
                    else:
                        raise e

    def get_watch_events(self) -> List:
        """
        Get all watch events for user.

        Returns:
            List: List of watch events for user.
        """
        response = self._make_authenticated_request(endpoint="subscriptions", method="get")
        if not response:
            return []
        else:
            return response.json().get("value", [])

    def batch_add_extended_property(self, message_ids, label_name):
        """
        Batch request for `add_extended_property` method.

        Args:
            message_ids: Message IDs for which to add extended property to.
            label_name: Label name which is to be added in extended property.

        Returns:
            list: Successful messages to which property is added.
        """
        batch_requests = [
            {
                "method": "PATCH",
                "url": f"/me/messages/{message_id}",
                "body": {
                    "singleValueExtendedProperties": [
                        {
                            "id": f"String {MICROSOFT_EXTENDED_PROPERTY_ID} Name {MICROSOFT_EXTENDED_PROPERTY_NAME}",
                            "value": label_name,
                        }
                    ]
                },
            }
            for message_id in message_ids
        ]
        success_request_ids, _ = self._make_authenticated_batch_request(batch_requests=batch_requests)
        return [message_ids[success_request_id] for success_request_id in success_request_ids]

    def batch_move(self, message_ids: List[str], label_name: str):
        """
        Move messages in batches to provided label_name.

        Args:
            message_ids: List of message ID of messages to be moved to destination label.
            label_name: Actual label name to which messages are to be moved.
        Returns:
            list: message_ids moved successfully
        """
        if label_name == MailBotGenericLabel.ZAPPED.value:
            # If moving to zapped then mark extended_property on email (flag as moved by bot) so that training can be skipped when we get the
            # webhook for the moved emails. Training only happens on zapped and inbox and since bot cannot move email to inbox on its own, only
            # check for zapped label here.
            success_message_ids = self.batch_add_extended_property(message_ids, label_name)
            logger.info(
                f"Successfully added extended property to {len(success_message_ids)}/{len(message_ids)} messages"
            )
        else:
            success_message_ids = message_ids
        label_id = self.get_label_id_for_generic_label(label_name)
        batch_requests = [
            {
                "method": "POST",
                "url": f"/me/messages/{message_id}/move",
                "body": {"destinationId": label_id},
            }
            for message_id in success_message_ids
        ]
        success_request_ids, _ = self._make_authenticated_batch_request(batch_requests=batch_requests)
        return [success_message_ids[success_request_id] for success_request_id in success_request_ids]

    def get_auth_tokens(self):
        """
        Get granted authentication tokens for outlook mailbot profile
        returns:
            dict: Authentication tokens (access token, refresh token) if the mailbox is present
        """
        return self.token_backend.get_token()

    @invalid_grant_error_decorator
    def mark_spam(self, message_id: str):
        """
        Report email as junk (outlook's spam folder) and block sender
        """
        self._make_authenticated_request(
            endpoint=f"me/messages/{message_id}/markAsJunk", method="post", data={"moveToJunk": True}, beta=True
        )

    @invalid_grant_error_decorator
    def trash_message(self, message_id: str):
        self.account.mailbox().get_message(object_id=message_id).move(
            folder=self.get_label_id(OutlookKnownLabelName.TRASH.value)
        )

    @invalid_grant_error_decorator
    def star_message(self, message_id: str):
        """
        Mark the message as flagged for star in outlook
        """
        message = self.account.mailbox().get_message(object_id=message_id)
        message.flag.set_flagged()
        message.save_message()

    def get_messages_from_sender(self, sender_email):
        """
        Get all message ids from sender
        Args:
            sender_email: Email address of sender
        Returns:
            messaged_ids: List of message ids from sender
        """
        message_ids = []
        skip = 0
        while True:
            response = self._make_authenticated_request(
                endpoint="me/messages/",
                method="get",
                params={
                    "$filter": f"(from/emailAddress/address) eq '{sender_email}'",
                    "$select": "id",
                    "$top": self.MAX_TOP_SIZE,
                    "$skip": skip,
                },
            )
            if not response:
                logger.error(f"Fetching emails failed while bulk trash for outlook", {"sender_email": sender_email})
            else:
                message_ids.extend([x["id"] for x in response.json().get("value", [])])
            if response.json().get("@odata.nextLink", None) is None:
                break
            else:
                skip += self.MAX_TOP_SIZE

        return message_ids

    def bulk_trash_message(self, message_ids: List[str]):
        """
        Moves provided messages to trash
        Args:
            message_ids: Message ids of the messages which need to be moved to trash
        Returns:
            Length of message_ids which are moved to trash
        """
        # Message and sender profile data will be updated in the webhook sync
        message_ids_moved = self.batch_move(message_ids, Message.GENERIC_LABEL_TRASH)
        logger.info(f"{len(message_ids_moved)}/{len(message_ids)} messages moved to trash")
        return len(message_ids_moved)

    def get_attachment(self, *args, **kwargs):
        pass
