from django.conf import settings

from mailbot.models import UserMailBotProfile
from mailbot.service.base import BaseMessageService
from mailbot.service.gmail import GmailService
from mailbot.service.outlook import OutlookService


class MessageServiceFactory:
    @staticmethod
    def get_message_service(user_mailbot_profile: UserMailBotProfile) -> BaseMessageService:
        """
        MessageServiceFactory is a factory class to get message service based on service_provider.

        Args:
            user_mailbot_profile (UserMailBotProfile): UserMailBotProfile object

        Returns:
            BaseMessageService: Message service object
        """
        if user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_MICROSOFT:
            return OutlookService(user_mailbot_profile=user_mailbot_profile)
        elif user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_GOOGLE:
            return GmailService(user_mailbot_profile=user_mailbot_profile)
