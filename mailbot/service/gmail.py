import base64
import datetime
import logging
from io import Bytes<PERSON>
from typing import Dict, List, Iterable, Any, Callable, Optional, Set, Sequence

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Q, F, Func, Value, J<PERSON>NField
from google.auth.exceptions import RefreshError

from applications.exceptions import FailedToSendEmailException
from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from applications.models import EmailTemplate, ScheduledTask
from applications.utils.email import html_to_text
from execfn import ApplicationTag
from mailbot.models import MailBotGenericLabel, Message, MailSubscription, UserMailBotProfile
from mailbot.service.base import BaseMessageService
from mailbot.signals import post_email_read, post_label_change
from mailbot.utils import database
from mailbot.utils.defaults import (
    GmailKnownLabelName,
    MailBotScheduledTasks,
    MailBotTemplateTag,
    MailBotMessageCategory,
    MailBotMessageHeaders,
    GMAIL_LABELS,
)
from mailbot.utils.exceptions import (
    LabelIdNotFoundError,
    LabelNameAlreadyExistError,
    MultipleMailBotLabelError,
    MessageIdNotFoundError,
    SyncMessageWithDatabaseError,
)
from mailbot.utils.google import GMailBox
from mailbot.utils.message_parser import parse_gmail_message, ParsedGmailMessage, ParsedMessage

User = get_user_model()
logger = logging.getLogger(__name__)


class MailBoxPaginator:
    """
    Paginator for Gmail API service `.list()` endpoints which generalize the pagination to lazily fetch pages using iterator.
    """

    MAX_RESULTS_PER_PAGE = 500
    HISTORY_LIST_MAPPER = lambda response: response.pop("history", [])
    MESSAGES_LIST_MAPPER = lambda response: list(map(lambda x: x["id"], response.pop("messages", [])))

    def __init__(
        self,
        *,
        mailbox_function: Callable,
        message_kwargs: Dict[str, Any],
        response_data_mapper: Callable[[Dict[str, Any]], List],
        limit: Optional[int] = None,
    ):
        """
        Initialise the paginator for Gmail API service `.list()` like endpoints.
        Args:
            mailbox_function: API function to use. It must accept `page_token` parameter and return nextPageToken in response.
            message_kwargs: Kwargs to pass onto mailbox_function in the initial request. For following requests page_token will also be passed if set in previous response.
            response_data_mapper: Map the response to the list of interest which paginator will return when iterated over.
            limit: Upper bound on count of messages to fetch matching the filters. Default to None for returning all data.
        """
        self.mailbox_function_kwargs = message_kwargs
        self.mailbox_function = mailbox_function
        self.response_data_mapper = response_data_mapper
        self.total_pages = 0
        self.extra_data = []
        self.limit = limit  # Number of items to return when called list(paginator)
        self.state = 0  # Index over current page data
        self.data = []  # Window for single page data
        self.next_page_token = None  # Whether there are more pages to fetch
        self.data_count = 0  # Number of items on current page
        self.total_count = 0  # Total number of items fetched till now
        self.initialise_first_request()

    def initialise_first_request(self):
        """
        Fetch the first page data based on which following pages are fetched if data limit is still not reached.
        """
        logger.info("Fetching page 1")
        response = self.mailbox_function(**self.mailbox_function_kwargs)
        self.data = self.response_data_mapper(response)
        self.next_page_token = response.pop("nextPageToken", None)
        self.extra_data.append(response)
        self.total_pages = 1
        data_count = len(self.data)
        logger.info(f"Fetched {data_count} items for page 1")
        if self.limit and self.limit < data_count:
            logger.info(f"Limiting data to {self.limit}")
            self.data_count = self.limit
            self.total_count = self.limit
        else:
            self.data_count = data_count
            self.total_count = data_count

    def __iter__(self):
        return self

    def __next__(self):
        if self.state < self.data_count:
            value = self.data[self.state]
            self.state += 1
            return value
        elif self.limit and self.total_count >= self.limit:
            raise StopIteration()
        elif self.next_page_token is None:
            raise StopIteration()
        # exhausted the current page, fetch next
        self.total_pages += 1
        logger.info(f"Fetching page {self.total_pages}")
        response: Dict[str, Any] = self.mailbox_function(
            **self.mailbox_function_kwargs, page_token=self.next_page_token
        )
        self.data = self.response_data_mapper(response)
        self.next_page_token = response.pop("nextPageToken", None)
        self.extra_data.append(response)
        items_count = len(self.data)
        logger.info(f"Fetched {items_count} items for page {self.total_pages}")
        if self.limit:
            diff = self.limit - (self.total_count + items_count)
            if diff < 0:
                logger.info(f"Limiting data to {self.limit}")
                self.data = self.data[:diff]
                self.next_page_token = None  # stop batching
                items_count += diff
        if items_count:
            self.data_count = items_count
            self.total_count += items_count
            self.state = 0
            value = self.data[self.state]
            self.state += 1
            return value
        else:
            raise StopIteration()

    def iterate_batch(self, batch_size):
        """
        Return the batch of messages.
        """
        batch = []
        for _ in range(batch_size):
            try:
                batch.append(next(self))
            except StopIteration:
                break
        return batch


class GmailService(BaseMessageService):
    default_fields_to_select = (
        "id",
        "threadId",
        "labelIds",
        "historyId",
        "internalDate",
        "sizeEstimate",
        "payload",
    )
    ARCHIVING_EMAILZAP = "Archiving_EmailZap"

    def __init__(self, user_mailbot_profile: UserMailBotProfile):
        super().__init__(user_mailbot_profile=user_mailbot_profile)
        self.mailbot_labels = {
            MailBotGenericLabel.WHITE_LIST.value: GMAIL_LABELS[MailBotGenericLabel.WHITE_LIST.value],
            MailBotGenericLabel.ZAPPED.value: GMAIL_LABELS[MailBotGenericLabel.ZAPPED.value],
        }
        self.sender_stats_enabled_labels = [
            MailBotGenericLabel.WHITE_LIST.value,
            MailBotGenericLabel.ZAPPED.value,
            Message.GENERIC_LABEL_SENT,
        ]
        self.label_name_lookup = {
            Message.GENERIC_LABEL_SENT: GmailKnownLabelName.SENT.value,
            Message.GENERIC_LABEL_TRASH: GmailKnownLabelName.TRASH.value,
            Message.GENERIC_LABEL_SPAM: GmailKnownLabelName.SPAM.value,
            **self.mailbot_labels,
        }
        self.is_mailbox_present = True
        try:
            self.mailbox = GMailBox(user_mailbot_profile=user_mailbot_profile)
        except RefreshError:
            self.is_mailbox_present = False

    def sync_label_mappings(self):
        if self.is_mailbox_present:
            super().sync_label_mappings()

    def get_label_mappings(self):
        """
        Get label mappings from Gmail API.
        Returns:
            Dict[str, str]: Mappings from label id to label name.
        """
        labels = self.mailbox.get_labels()
        label_mappings = {}
        for label in labels:
            label_mappings[label["id"]] = label["name"]
        return label_mappings

    def create_label(self, label_name: str) -> str:
        """Create label with the given name in the user's mailbox if not already present.
        Check from label mappings if the label exists already before creating it.

        Args:
            label_name (str): label name to create.
        """
        response = self.mailbox.create_label(label_name=label_name)
        return response["id"]

    def get_label_stats(self, label_name: str):
        """
        Get the stats of the label in the user's mailbox.

        Args:
            label_name (str): Label name to get stats.
        """
        if not (label_id := self.get_label_id(label_name)):
            return {}
        response = self.mailbox.get_label_stats(label_id)
        return {
            "total_messages": response.get("messagesTotal", 0),
            "unread_messages": response.get("messagesUnread", 0),
        }

    def rename_label(self, old_label_name: str, new_label_name: str):
        """
        Rename the label in the user's mailbox.
        NOTE: Remember to update database references to the label name after renaming.

        Args:
            old_label_name (str): Old label name to rename.
            new_label_name (str): New label name to rename.
        """
        logger.info(f"Renaming label {old_label_name} to {new_label_name} for user {self.email}")
        if not (label_id := self.get_label_id(old_label_name)):
            return False
        self.mailbox.rename_label(label_id, new_label_name)
        # As label ID is same, we want to clear cache
        self.get_label_id.cache_clear()
        self.user_mailbot_profile.label_mappings[label_id] = new_label_name
        self.user_mailbot_profile.save(update_fields=["label_mappings"])
        return True

    def create_archiving_emailzap_label(self, sync_label_mappings=False):
        # Create the ARCHIVING_EMAILZAP label in GMail
        label_mappings = self.get_label_mappings()
        # We consider archive label by EmailZap as a separate label from other mailbot labels
        # as it is a temporary label and can exist along with other mailbot labels
        if self.ARCHIVING_EMAILZAP not in label_mappings.values():
            logger.info(f"creating label {self.ARCHIVING_EMAILZAP} for user {self.email}")
            try:
                self.create_label(label_name=self.ARCHIVING_EMAILZAP)
            except LabelNameAlreadyExistError:
                logger.info(f"Label {self.ARCHIVING_EMAILZAP} already exists for user {self.email} for creation")
            if sync_label_mappings:
                self.sync_label_mappings()
        else:
            logger.info(f"Label {self.ARCHIVING_EMAILZAP} already exists for user {self.email} for creation")

    def delete_label(self, label_name):
        """
        Remove the label from the user's mailbox.
        NOTE: Remember to update database references to the label name after removing.

        Args:
            label_name (str): Label name to remove.
        """
        if (label_id := self.get_label_id(label_name)) and label_id in self.user_mailbot_profile.label_mappings:
            logger.info(f"removing label {label_name} for user {self.email}")
            self.mailbox.delete_label(label_id=label_id)
            del self.user_mailbot_profile.label_mappings[label_id]
            self.user_mailbot_profile.save(update_fields=["label_mappings"])
        else:
            logger.info(f"Label {label_name} not found for user {self.email} for removal")

    def create_default_labels(self):
        """
        Create default labels for the user's mailbox during onboarding.
        """
        if check_feature(
            user_id=self.user.id,
            feature_flag=MailBotFeatureFlag.ARCHIVING_EMAILZAP,
            application_tag=ApplicationTag.MailBot,
        ):
            self.create_archiving_emailzap_label()
        super().create_default_labels()

    def scan_messages(
        self,
        generic_label: str,
        limit: int = None,
        select_fields: List[str] = None,
        message_mapper=None,
        newer_than_hours=None,
        older_than_hours=None,
        **kwargs,
    ) -> Iterable[Any]:
        """Scan the folder messages.

        Args:
            generic_label (str): Generic name of the label to scan that must be present in `label_name_lookup` keys.
            limit (int, optional): Number of messages to scan. Defaults to all messages.
            select_fields (List[str], optional): Only retrieve these fields from the API. Defaults to all fields.
            message_mapper (Callable, optional): After retrieving response from API, transform using this callable. Defaults to identity mapper.

        Returns:
            Iterable[Any]: Iterable over messages.
        """
        label_name = self.label_name_lookup.get(generic_label)
        if not label_name:
            logger.exception(
                "Skipped scanning of label as it is not found in label_name_lookup",
                extra={"email": self.email, "generic_label": generic_label},
            )
            return []
        label_id = self.get_label_id_for_generic_label(generic_label=generic_label)
        logger.info(f"Fetching messages for label {label_name} for user {self.email}")
        scanned_messages = []
        unhandled_errors = []
        db_sync_errors = []
        max_results = (
            min(limit, MailBoxPaginator.MAX_RESULTS_PER_PAGE) if limit else MailBoxPaginator.MAX_RESULTS_PER_PAGE
        )
        message_kwargs = {
            "label_ids": [label_id],
            "max_results": max_results,
        }
        if newer_than_hours and older_than_hours:
            message_kwargs["q"] = f"newer_than:{newer_than_hours}h older_than:{older_than_hours}h"
        elif newer_than_hours:
            message_kwargs["q"] = f"newer_than:{newer_than_hours}h"
        elif older_than_hours:
            message_kwargs["q"] = f"older_than:{older_than_hours}h"
        paginator = MailBoxPaginator(
            limit=limit,
            mailbox_function=self.mailbox.get_message_ids,
            message_kwargs=message_kwargs,
            response_data_mapper=MailBoxPaginator.MESSAGES_LIST_MAPPER,
        )
        idx = 0
        while True:
            batch = paginator.iterate_batch(max_results)
            if not batch:
                logger.info(f"No more messages for label {label_name} for user {self.email}")
                break
            messages = self.get_messages(batch, select_fields=select_fields)
            logger.info(f"Fetched {len(messages)} messages for label {label_name} for user {self.email}")
            for message in messages:
                # Don't interrupt onboarding due to error scanning in single message
                try:
                    scanned_message = message_mapper(message)() if message_mapper else message
                except SyncMessageWithDatabaseError as e:
                    db_sync_errors.append(str(e))
                except Exception as e:
                    unhandled_errors.append(str(e))
                else:
                    scanned_messages.append(scanned_message)
            idx += len(messages)
            if limit and idx >= limit:
                logger.info(f"Reached limit of {limit} messages for label {label_name} for user {self.email}")
                break
            elif len(batch) < max_results:
                logger.info(f"No more messages for label {label_name} for user {self.email}")
                break
        if unhandled_errors:
            logger.error(
                "Unhandled errors while scanning messages during onboarding", extra={"errors": unhandled_errors}
            )
        if db_sync_errors:
            logger.error(
                "Database sync errors while scanning messages during onboarding", extra={"errors": db_sync_errors}
            )
        return scanned_messages

    def is_sender_stats_enabled(self, label_ids: Sequence[str]):
        """
        Whether we should maintain count and read fraction for current message with given label IDs.
        Args:
            label_ids: Label IDs of message currently processing.

        Returns:
            bool: True if even single label is present for stats enabled, else False.
        """
        if GmailKnownLabelName.TRASH.value in label_ids:
            # If message is moved to TRASH then consider it stats disabled
            return False
        result = False
        for sender_stats_enabled_label in self.sender_stats_enabled_labels:
            label_name = self.label_name_lookup[sender_stats_enabled_label]
            if self.get_label_id(label_name) in label_ids:
                result = True
                break
        return result

    def _prepare_sets_for_delta_change(self, stored_label_ids: Iterable[str], final_label_ids: Iterable[str]):
        """
        Prepare sets for delta change in user's mailbox.
        Args:
            stored_label_ids: Label IDs stored in database for email currently processing.
            final_label_ids: Current Label IDs in user's mailbox for email currently processing.

        Returns:
            Multiple sets of interest used for making decisions in syncing with database.
        """
        stored_label_ids_set = set(stored_label_ids)
        final_label_ids_set = set(final_label_ids)
        mailbot_label_ids_set = {
            self.get_label_id(mailbot_label_name) for mailbot_label_name in self.mailbot_labels.values()
        }

        stored_mailbot_label_ids_set = stored_label_ids_set.intersection(mailbot_label_ids_set)
        assert (
            len(stored_mailbot_label_ids_set) <= 1
        ), f"We never store message with more than one mailbot label ID, got {stored_label_ids_set}"
        final_mailbot_label_ids = final_label_ids_set.intersection(mailbot_label_ids_set)
        added_label_ids_set = final_label_ids_set.difference(stored_label_ids_set)
        added_mailbot_label_ids_set = added_label_ids_set.intersection(mailbot_label_ids_set)
        removed_label_ids_set = stored_label_ids_set.difference(final_label_ids_set)
        removed_mailbot_label_ids_set = removed_label_ids_set.intersection(mailbot_label_ids_set)
        return (
            stored_label_ids_set,
            stored_mailbot_label_ids_set,
            final_label_ids_set,
            final_mailbot_label_ids,
            added_label_ids_set,
            added_mailbot_label_ids_set,
            removed_label_ids_set,
            removed_mailbot_label_ids_set,
        )

    def _multi_mailbot_labels_pre_check(
        self,
        message_id: str,
        final_label_ids: List[str],
        added_mailbot_label_ids_set: Set[str],
        added_label_ids_set: Set[str],
        removed_label_ids_set: Set[str],
        final_mailbot_label_ids_set: Set[str],
        stored_mailbot_label_ids_set: Set[str],
    ) -> bool:
        """
        Performs pre-checks on multiple mailbot labels before updating in database.
        This can happen if following cases:
            - Case-1: User adds multiple mailbot labels (assigns grey_list, black_list to inbox)
            - Case-2: User moved mailbot labelled message to TRASH (Gmail will not remove user-defined labels when moving to TRASH)
            - Case-3: User moved mailbot labelled message to INBOX (which is also a mailbot label) (Gmail will not remove user-defined labels when moving to INBOX)
        Args:
            message_id: Message ID to be stored
            final_label_ids: List of final label IDs after the update
            added_mailbot_label_ids_set: Set of added mailbot label IDs
            added_label_ids_set: Set of added label IDs
            removed_label_ids_set: Set of removed label IDs
            final_mailbot_label_ids_set: Set of final mailbot label IDs
            stored_mailbot_label_ids_set: Set of stored mailbot label IDs

        Returns:
            bool: True if no multiple mailbot label present, False otherwise
        """
        if len(added_mailbot_label_ids_set) > 1:
            # CASE-1
            logger.info(
                f"{message_id}: Undoing the user's ({self.email}) whole action due to addition of multiple mailbot labels"
            )
            # Undo the operation using Gmail API
            # TODO : In future we need a way to the let user know
            self.mailbox.update_labels(
                message_id, remove_label_ids=added_label_ids_set, add_label_ids=removed_label_ids_set
            )
            return False
        # CASE-2
        added_trash_only = (
            GmailKnownLabelName.TRASH.value in added_label_ids_set and len(final_mailbot_label_ids_set) == 1
        )
        # CASE-3 (Generalised to moving from one mailbot label to another)
        added_mailbot_label_only = len(added_mailbot_label_ids_set) == 1 and len(final_mailbot_label_ids_set) == 2

        if added_mailbot_label_only or added_trash_only:
            # Remove old mailbot label id in these cases
            assert len(stored_mailbot_label_ids_set) == 1
            old_mailbot_label_id = stored_mailbot_label_ids_set.pop()
            assert old_mailbot_label_id in final_label_ids
            logger.info(f"{message_id}: Enforced removing {old_mailbot_label_id}")
            final_label_ids.remove(old_mailbot_label_id)
            try:
                self.mailbox.update_labels(message_id, remove_label_ids=[old_mailbot_label_id], add_label_ids=None)
            except MessageIdNotFoundError as message_id_not_found_error:
                # If message is moved to Trash, then it may have been deleted from the Trash immediately after that
                if not added_trash_only:
                    # Only raise error if not trashed
                    raise message_id_not_found_error
            return True
        else:
            return True

    def _update_user_training(self, sender_email, added_mailbot_label_ids_set: Set[str]):
        """
        Updates user training information based on the added mailbot label.

        Args:
            sender_email: Sender's email
            added_mailbot_label_ids_set: Set of added mailbot label IDs
        """
        user_training_generic_label = None
        # Assuming only one mailbot label is added
        added_mailbot_label_id = added_mailbot_label_ids_set.pop()
        for generic_label_name in self.mailbot_labels.keys():
            if added_mailbot_label_id == self.get_label_id_for_generic_label(generic_label_name):
                user_training_generic_label = generic_label_name
                break
        if user_training_generic_label:
            logger.info(f"{sender_email}: User training to {user_training_generic_label}")
            database.update_user_training(
                self.user_mailbot_profile.id, sender_email=sender_email, user_training=user_training_generic_label
            )

    def get_generic_label(self, label_ids: Sequence[str]) -> str:
        """
        Generic label to maintain the single label of interest.
        This is to maintain common meaning in all service providers.

        Examples:
            In outlook generic label is archive if mail is present in "Archive" folder,
            but in Gmail generic label is considered archive if no mailbot label IDs are found.
        Args:
            label_ids: Label IDs from which to get a single generic label.
        Returns:
            str: A generic label that have common meaning in all service providers.
        """
        if GmailKnownLabelName.TRASH.value in label_ids:
            # If message is in TRASH then don't consider any other label
            return Message.GENERIC_LABEL_TRASH
        if GmailKnownLabelName.SENT.value in label_ids:
            # If message is in SENT but not TRASH then don't consider any other label
            return Message.GENERIC_LABEL_SENT
        generic_label = None
        for k, v in self.mailbot_labels.items():
            if self.get_label_id(v) in label_ids:
                if generic_label:
                    raise MultipleMailBotLabelError(f"Got multiple mailbot label ID: {label_ids}")
                else:
                    generic_label = k
        if not generic_label:
            return Message.GENERIC_LABEL_ARCHIVE
        return generic_label

    def train_due_to_label_removal(
        self, db_message, removed_mailbot_label_ids_set, removed_label_ids_set, added_label_ids_set
    ):
        """
        If the user removes label grey_list or black_list from message, move and train to inbox.
        If user removes the EmailZap's archive label from the message, and it is still present in the inbox,
        consider it the user training to the inbox.

        Args:
            db_message: Message object
            removed_mailbot_label_ids_set: Set of removed mailbot label IDs
            removed_label_ids_set: Set of removed label IDs
            added_label_ids_set: Set of added label IDs
        """
        if len(added_label_ids_set) == 0 and len(removed_mailbot_label_ids_set) == 1:
            removed_generic_label = self.get_generic_label(removed_label_ids_set)
            if removed_generic_label == MailBotGenericLabel.ZAPPED.value:
                # If the user removes label zapped from message, move and train to inbox
                # NOTE: This will undo some users' intention to actually archive the message
                logger.info(f"{db_message.message_id}: Training to inbox due to removal of zapped label by user")
                # Moving the email directly using the API without syncing it with the database first
                # will result in simulating that the user has moved email to the inbox and will be trained to the inbox
                # in the next sync.
                self.mailbox.update_labels(
                    db_message.message_id,
                    remove_label_ids=None,
                    add_label_ids=[self.get_label_id(GmailKnownLabelName.INBOX.value)],
                )
                return db_message
            elif (
                check_feature(
                    user_id=self.user.id,
                    feature_flag=MailBotFeatureFlag.ARCHIVING_EMAILZAP,
                    application_tag=ApplicationTag.MailBot,
                )
                and db_message.generic_label == MailBotGenericLabel.WHITE_LIST.value
                and self.get_label_name(next(iter(removed_label_ids_set))) == self.ARCHIVING_EMAILZAP
            ):
                # If user removes the emailzap's archive label from the message, and it is still present in the inbox,
                # consider it the user training to the inbox.
                logger.info(f"{db_message.message_id}: Training to INBOX due to removal of archive label by EmailZap")
                # Delete all scheduled tasks for updating labels
                ScheduledTask.objects.filter(
                    user=self.user,
                    periodic_task__task=MailBotScheduledTasks.UPDATE_LABELS.value,
                    metadata__message_id=db_message.message_id,
                ).delete()
                # Inbox is already present, just train the sender
                database.update_user_training(
                    self.user_mailbot_profile.id,
                    sender_email=db_message.metadata.get("from")[1],
                    user_training=MailBotGenericLabel.WHITE_LIST.value,
                )

    def sync_existing_message(self, parsed_message: ParsedGmailMessage, db_message: Message):
        (
            stored_label_ids_set,
            stored_mailbot_label_ids_set,
            final_label_ids_set,
            final_mailbot_label_ids_set,
            added_label_ids_set,
            added_mailbot_label_ids_set,
            removed_label_ids_set,
            removed_mailbot_label_ids_set,
        ) = self._prepare_sets_for_delta_change(
            stored_label_ids=db_message.metadata["label_ids"], final_label_ids=parsed_message.label_ids
        )
        if stored_label_ids_set != final_label_ids_set:
            logger.info(
                f"{parsed_message.message_id}: Labels changed for existing message from {stored_mailbot_label_ids_set} to {final_label_ids_set}"
            )
            should_sync_with_database = self._multi_mailbot_labels_pre_check(
                message_id=parsed_message.message_id,
                final_label_ids=parsed_message.label_ids,
                added_label_ids_set=added_label_ids_set,
                added_mailbot_label_ids_set=added_mailbot_label_ids_set,
                removed_label_ids_set=removed_label_ids_set,
                final_mailbot_label_ids_set=final_mailbot_label_ids_set,
                stored_mailbot_label_ids_set=stored_mailbot_label_ids_set,
            )
            if not should_sync_with_database:
                logger.warning(
                    f"{parsed_message.message_id}: Skipping sync with database due to multiple mailbot labels"
                )
                return db_message
            if train_due_to_label_removal_result := self.train_due_to_label_removal(
                db_message=db_message,
                removed_label_ids_set=removed_label_ids_set,
                removed_mailbot_label_ids_set=removed_mailbot_label_ids_set,
                added_label_ids_set=added_label_ids_set,
            ):
                logger.info("Skipping sync with database due to training to INBOX, will sync after training")
                return train_due_to_label_removal_result
            is_sender_stats_enabled = self.is_sender_stats_enabled(parsed_message.label_ids)
            was_sender_stats_enabled = self.is_sender_stats_enabled(stored_label_ids_set)
            was_read = db_message.is_read
            database.sync_sender_profile_for_existing_message(
                parsed_message=parsed_message,
                was_read=was_read,
                is_sender_stats_enabled=is_sender_stats_enabled,
                was_sender_stats_enabled=was_sender_stats_enabled,
            )
            # When moving messages from API, just preset the generic label to avoid user training
            # Label Ids, read count, etc. will still be updated
            if (
                len(added_mailbot_label_ids_set) == 1
                and not parsed_message.is_sent
                and db_message.generic_label != self.get_generic_label(parsed_message.label_ids)
            ):
                if Message.objects.filter(thread_id=db_message.thread_id).count() == 1:
                    user_training_generic_label = self.get_generic_label(added_mailbot_label_ids_set)
                    logger.info(f"{parsed_message.message_id}: Updating user training to {user_training_generic_label}")
                    database.update_user_training(
                        self.user_mailbot_profile.id,
                        sender_email=parsed_message.from_name_email[1],
                        user_training=user_training_generic_label,
                    )
                else:
                    logger.info(
                        f"{parsed_message.message_id}: Skipped user training as message was part of conversation/thread"
                    )

            old_generic_label = db_message.generic_label
            new_generic_label = self.get_generic_label(parsed_message.label_ids)

            db_message.generic_label = new_generic_label
            db_message.metadata["is_read"] = parsed_message.is_read
            db_message.is_read = parsed_message.is_read
            db_message.metadata["label_ids"] = parsed_message.label_ids
            db_message.save()

            if not parsed_message.is_sent and not was_read and parsed_message.is_read:
                post_email_read.send(sender=self.__class__, message=db_message)

            if old_generic_label != new_generic_label:
                post_label_change.send(
                    sender=self.__class__,
                    message=db_message,
                    old_generic_label=old_generic_label,
                    new_generic_label=new_generic_label,
                )
        else:
            logger.info(f"{parsed_message.message_id}: Message has already been synced with the database")
        return db_message

    def sync_with_database(self, parsed_message: ParsedMessage) -> Callable[[Any], Message]:
        """Maps the message response from API for GMAIL to `Message` model.
        Args:
            parsed_message: Parsed message response from `parse_gmail_message`.

        Returns:
            Callable[[Any], Message]: Function mapping the response from API.
        """

        def mapper(**kwargs):
            """
            Sync API response with SenderProfile, Message in database.
            Args:
                **kwargs: Extra information such as generic label.
            Benchmarking:
                0.008 seconds if non-sent message
                0.004 sync_sender_profile
            Returns:
                Message: Message object
            """
            try:
                db_message = Message.objects.get(message_id=parsed_message.message_id)
            except Message.DoesNotExist:
                try:
                    generic_label = self.get_generic_label(parsed_message.label_ids)
                except MultipleMailBotLabelError:
                    logger.warning(
                        f"Skipping message_id {parsed_message.message_id} due to multiple mailbot labels for user {self.email}"
                    )
                    raise SyncMessageWithDatabaseError(
                        message_id=parsed_message.message_id, reason="Multiple mailbot labels"
                    )
                else:
                    return self.sync_new_message(parsed_message=parsed_message, generic_label=generic_label)
            else:
                return self.sync_existing_message(parsed_message, db_message)

        return mapper

    def get_messages(self, message_ids: Iterable[str], select_fields: List[str] = None) -> List[ParsedGmailMessage]:
        """Get messages from API and parse them.

        Args:
            message_ids (Iterable[str]): List of message IDs to fetch.
            select_fields (List[str], optional): Retrieve these additional fields from API. Defaults to None.

        Returns:
            List[Dict[str, Any]]: List of parsed messages.
        """
        if not select_fields:
            select_fields = []
        messages = self.mailbox.get_messages(
            message_ids, fields_to_select=[*self.default_fields_to_select, *select_fields]
        )
        parsed_messages = []
        for message in messages:
            try:
                parsed_message = parse_gmail_message(self.user_mailbot_profile, message)
            except ValidationError:
                # 'From' Header contains invalid Email
                logger.info(f"Error parsing gmail message with ID {message['id']}")
            else:
                parsed_messages.append(parsed_message)
        return parsed_messages

    def get_message(self, message_id: str, select_fields: List[str] = None) -> ParsedMessage:
        """Get message from API.

        Args:
            message_id (str): Message ID to fetch.
            select_fields (List[str], optional): Retrieve these additional fields from API. Defaults to None.

        Returns:
            Dict[str, Any]: Parsed message.

        Raises:
            MessageIdNotFoundError: If message ID not found.
            InvalidSelectFieldsError: If any invalid select field is passed.
            MailBotHTTPError: If any other error occurs while fetching message.
        """
        if not select_fields:
            select_fields = []
        message = self.mailbox.get_message(
            message_id=message_id, fields_to_select=[*self.default_fields_to_select, *select_fields]
        )
        return parse_gmail_message(user_mailbot_profile=self.user_mailbot_profile, message=message)

    def get_message_body(self, message_id: str, html=False):
        """
        Fetch the message payload using API and parse the message body.
        Args:
            message_id: ID of the message to fetch
            html: Whether to return body in HTML or Text format

        Returns:
            str: Message body in specified format

        Raises:
            MessageIdNotFoundError: If message ID not found.
            InvalidSelectFieldsError: If any invalid select field is passed.
            MailBotHTTPError: If any other error occurs while fetching message.
        """
        parsed_gmail_message = self.get_message(message_id=message_id)
        if html:
            return parsed_gmail_message.html_body
        else:
            text_body = parsed_gmail_message.text_body
            if not text_body:
                text_body = html_to_text(parsed_gmail_message.html_body)
            return text_body

    def store_history_id(self):
        """
        Store the current history ID of mailbox in database for the user if not already stored (during onboarding).
        """
        mailbox_current_history_id = self.mailbox.latest_history_id
        update_count = UserMailBotProfile.objects.filter(
            ~Q(metadata__has_key="history_id"), id=self.user_mailbot_profile.id
        ).update(
            metadata=Func(
                F(name="metadata"),
                Value(value=["history_id"]),
                Value(value=mailbox_current_history_id, output_field=JSONField()),
                function="jsonb_set",
            )
        )
        if update_count:
            logger.info(f"Stored current history ID {mailbox_current_history_id} for user {self.email}")

    def subscribe_watch_events(self):
        """
        Subscribe to watch channel events for the user's mailbox.
        """
        self.unsubscribe_watch_events()
        self.store_history_id()
        labels_to_watch = (
            GmailKnownLabelName.SENT.value,
            GmailKnownLabelName.INBOX.value,
            GmailKnownLabelName.DRAFT.value,
            GMAIL_LABELS[MailBotGenericLabel.ZAPPED.value],
        )
        label_ids = [self.get_label_id(label_to_watch) for label_to_watch in labels_to_watch]
        response = self.mailbox.watch_channel(label_ids=label_ids)
        expiration = response["expiration"]
        expire_at = datetime.datetime.fromtimestamp(int(expiration) / 1000, tz=datetime.timezone.utc)
        MailSubscription.objects.update_or_create(
            user_mailbot_profile=self.user_mailbot_profile, label_name="", defaults={"expire_at": expire_at}
        )
        logger.info(f"Gmail watch channel events subscribed for user {self.email}")

    def unsubscribe_watch_events(self):
        try:
            if self.is_mailbox_present:
                self.mailbox.unwatch_channel()
                logger.info(f"Unsubscribed watch events for user {self.email} successfully")
            else:
                logger.info(f"Unsubscribe watch events skipped for user {self.email} as mailbox not present")
        except RefreshError:
            # Access is revoked so no need to log exception
            logger.info(f"Unsubscribe watch events skipped for user {self.email} as mailbox not present")

    @staticmethod
    def remove_label_ids_to_retain_post_archive(label_ids_set: Set[str]):
        """
        Remove label IDs from the 'label_ids_set' that should be retained after archiving the message.
        This ensures that when un-archiving the message, the user retains these specific labels.

        Args:
            label_ids_set (str): Set of label IDs on the message.

        Returns:
            Set[str]: Set of label IDs to remove for archiving the message.
        """
        return label_ids_set.difference(
            [
                GmailKnownLabelName.UNREAD.value,
                GmailKnownLabelName.STARRED.value,
                GmailKnownLabelName.SENT.value,
                GmailKnownLabelName.IMPORTANT.value,
                GmailKnownLabelName.CATEGORY_FORUMS.value,
                GmailKnownLabelName.CATEGORY_SOCIAL.value,
                GmailKnownLabelName.CATEGORY_PERSONAL.value,
                GmailKnownLabelName.CATEGORY_UPDATES.value,
                GmailKnownLabelName.CATEGORY_PROMOTIONS.value,
            ]
        )

    def _move_by_bot(self, message_id: str, remove_label_ids: Set[str], add_label_ids: Set[str]):
        """
        Move message by bot helper function which atomically move message and sync with database,
        and rollback in case of failure.
        Args:
            message_id: Message ID of message to move
            remove_label_ids: Label IDs to remove from email
            add_label_ids: Label IDs to add to email
        Raises:
            ModifyMessageLabelsError: If any error occurs while modifying message labels.
        """
        # Setting durable to avoid nested atomic blocks
        # Following code sets auto_commit to False and commit_on_exit iff no exception is raised
        with transaction.atomic(durable=True):
            message = Message.objects.select_for_update(of=("self",)).get(message_id=message_id)
            old_label_ids = message.metadata["label_ids"]
            message.metadata["label_ids"] = list(set(old_label_ids).difference(remove_label_ids).union(add_label_ids))
            message.generic_label = self.get_generic_label(message.metadata["label_ids"])
            message.save(update_fields=["metadata", "generic_label"])
            self.mailbox.update_labels(
                message_id=message_id, remove_label_ids=remove_label_ids, add_label_ids=add_label_ids
            )

    def send_recreate_label_alert(self):
        """
        If a user deletes emailzap label, we re-create the label and send them an alert email.
        """
        try:
            recreate_label_alert_template = EmailTemplate.objects.get(
                application__tag=ApplicationTag.MailBot.value,
                tag=MailBotTemplateTag.RECREATE_LABEL_ALERT.value,
            )
            recreate_label_alert_template.send_email(
                user=self.user,
                to=self.user.email,
                context={"frontend_base_url": settings.FRONTEND_BASE_URL, "name": self.user.first_name},
                headers={
                    MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.RECREATE_LABEL_ALERT.value
                },
            )
        except EmailTemplate.DoesNotExist:
            logger.exception("Recreate label alert template not found")
        except FailedToSendEmailException:
            logger.exception("Failed to send recreate label alert email", extra={"email": self.user.email})

    def move_to_label(self, message_id: str, destination_label_name: str) -> bool:
        """
        Remove all labels from message (except not needed ones like UNREAD) and add `destination_label` to message.
        If `destination_label` is not present in user's mailbox then create it before moving.
        Following cases are handled:
            - Case-1: Destination label is not present in user's mailbox and neither stored previously (Jarvis custom workflows for new user labels).
            - Case-2: Destination label is not present in user's mailbox but stored previously (User deleted the label).
                        Need to create the label again, clear the cache on `get_label_id(label_name)`, and update the label mappings in database.
            - Case-3: Destination label is present in user's mailbox but not stored previously (Handled by `get_label_id`, Jarvis custom workflows for existing user labels).
            - Case-4: Destination label is present in user's mailbox and stored previously. (Ideal case)
            - Case-5: Destination label is present in user's mailbox and stale label ID stored previously. Update mappings in database and clear the cache on `get_label_id(label_name)`.

        Args:
            message_id: Message ID of message to move.
            destination_label_name: Final single label for message.
        """
        logger.info(f"Moving message {message_id} to {destination_label_name}")
        destination_label_id = self.get_label_id(label_name=destination_label_name)
        if not destination_label_id:
            # Case-1
            logger.info(f"Creating folder {destination_label_name} for user {self.email} for first time")
            destination_label_id = self.create_label(label_name=destination_label_name)
            # Save the newly created label in database
            self.user_mailbot_profile.label_mappings[destination_label_id] = destination_label_name
            self.user_mailbot_profile.save(update_fields=["label_mappings"])

        db_message = Message.objects.get(message_id=message_id)
        label_ids_set = set(db_message.metadata.get("label_ids", []))
        if destination_label_id in label_ids_set:
            logger.info(f"Message ID {message_id} is already in {destination_label_name}")
            return True
        remove_label_ids = self.remove_label_ids_to_retain_post_archive(label_ids_set=label_ids_set)
        try:
            self._move_by_bot(message_id, remove_label_ids=remove_label_ids, add_label_ids={destination_label_id})
        except LabelIdNotFoundError:
            self.sync_label_mappings()
            if destination_label_id != self.get_label_id(label_name=destination_label_name):
                # Case-5 ((Rare case of user deleting the label and creating it again)
                logger.info(
                    f"Updated the stale label mapping for {destination_label_name} for user {self.email} in database"
                )
                self.get_label_id.cache_clear()
                self._move_by_bot(message_id, remove_label_ids=remove_label_ids, add_label_ids={destination_label_id})
                return True
            else:
                # Case-2
                logger.info(f"Creating label {destination_label_name} for user {self.email}")
                self.send_recreate_label_alert()
                # If label id is present in label mappings but user deleted the folder.
                destination_label_id = self.create_label(label_name=destination_label_name)
                self.get_label_id.cache_clear()
                self.sync_label_mappings()
                self._move_by_bot(message_id, remove_label_ids=remove_label_ids, add_label_ids={destination_label_id})
                return True
        else:
            # Case-1, Case-3, Case-4
            return True

    def archive_message(self, message_id: str) -> bool:
        """
        Remove all labels present on the message.
        Args:
            message_id: Message ID of message to archive.
        Returns:
            bool: Whether the message is archived or not.
        """
        try:
            message = self.get_message(message_id=message_id)
        except MessageIdNotFoundError:
            # If message does not exist while archiving then consider it archived
            return True
        else:
            label_ids_set = set(message.label_ids)
            remove_label_ids = self.remove_label_ids_to_retain_post_archive(label_ids_set=label_ids_set)
            if not remove_label_ids:
                # No label_ids to remove means message is already archived
                return True
            self.mailbox.update_labels(message_id, remove_label_ids=remove_label_ids, add_label_ids=None)
            return True

    def mark_read(self, message_id):
        """
        Mark message as read.

        Args:
            message_id: Message ID of message to mark as read.
        """
        self.mailbox.update_labels(message_id, remove_label_ids=[GmailKnownLabelName.UNREAD.value], add_label_ids=None)

    def mark_unread(self, message_id):
        """
        Mark message as unread.

        Args:
            message_id: Message ID of message to mark as unread.
        """
        self.mailbox.update_labels(message_id, remove_label_ids=None, add_label_ids=[GmailKnownLabelName.UNREAD.value])

    def list_history(self, start_history_id: int):
        """
        List history for user's mailbox.

        Args:
            start_history_id: History ID to start from.
        Returns:
            List[Dict[str, Any]]: List of history events.
        """
        message_kwargs = {"start_history_id": start_history_id}
        paginator = MailBoxPaginator(
            message_kwargs=message_kwargs,
            mailbox_function=self.mailbox.history_list,
            response_data_mapper=MailBoxPaginator.HISTORY_LIST_MAPPER,
        )
        latest_history_id = start_history_id
        if paginator.extra_data:
            latest_history_id = int(paginator.extra_data[0].get("historyId", latest_history_id))
        return list(paginator), latest_history_id

    def get_auth_tokens(self):
        """
        Optionally returns authentication tokens(access token, refresh token) if the mailbox is present
        """
        if self.is_mailbox_present:
            return self.mailbox.get_auth_tokens()

    def trash_message(self, message_id: str):
        self.mailbox.update_labels(message_id, remove_label_ids=None, add_label_ids=[GmailKnownLabelName.TRASH.value])

    def star_message(self, message_id: str):
        self.mailbox.update_labels(message_id, remove_label_ids=None, add_label_ids=[GmailKnownLabelName.STARRED.value])

    def mark_spam(self, message_id: str):
        self.mailbox.update_labels(message_id, remove_label_ids=None, add_label_ids=[GmailKnownLabelName.SPAM.value])

    def add_custom_labels(self, message_id: str, label_names: List[str]):
        """
        Add custom labels to an email message.

        Uses a helper method to get or create IDs for all specified label names,
        then applies them to the message.

        Args:
            message_id (str): The ID of the message to label.
            label_names (List[str]): A list of label names to attach.
        """
        label_ids_to_add = self.get_or_create_label_ids(label_names)

        if label_ids_to_add:
            try:
                self.mailbox.update_labels(message_id, remove_label_ids=None, add_label_ids=label_ids_to_add)
                logger.info(
                    f"Applied labels {label_names} to message {message_id} for user {self.user_mailbot_profile.user.email}"
                )
            except Exception as e:
                logger.error(
                    f"Error applying labels {label_names} to message {message_id} for user {self.user_mailbot_profile.user.email}: {e}"
                )
        else:
            logger.warning(
                f"No valid label IDs found or created for names {label_names} for message {message_id} for user {self.user_mailbot_profile.user.email}"
            )

    def get_or_create_label_ids(self, label_names: List[str]) -> List[str]:
        """
        Gets existing label IDs and creates new ones for label names not found.

        Handles syncing mappings and clearing cache if any new labels are created.

        Args:
            label_names (List[str]): A list of label names.

        Returns:
            List[str]: A list of corresponding label IDs (existing and newly created).
        """
        label_ids_to_add = []
        labels_created = False

        for label_name in label_names:
            label_id = self.get_label_id(label_name)
            if label_id:
                label_ids_to_add.append(label_id)
            else:
                try:
                    new_label_id = self.create_label(label_name)
                    label_ids_to_add.append(new_label_id)
                    labels_created = True
                except Exception as e:
                    logger.error(
                        f"Error creating label '{label_name}' for user {self.user_mailbot_profile.user.email}: {e}"
                    )
        # If any new labels were created, update local mappings and clear cache
        if labels_created:
            self.sync_label_mappings()
            self.get_label_id.cache_clear()

        return label_ids_to_add

    def get_messages_from_sender(self, sender_email: str):
        """
        Retrieves messages for a given sender email
        Args:
            from_sender_email: Email address of sender.
        Returns:
            message_ids: List of message IDs.
        """
        message_kwargs = {
            "label_ids": [],
            "max_results": MailBoxPaginator.MAX_RESULTS_PER_PAGE,
            "q": f"from: {sender_email}",
        }
        paginator = MailBoxPaginator(
            limit=None,
            mailbox_function=self.mailbox.get_message_ids,
            message_kwargs=message_kwargs,
            response_data_mapper=MailBoxPaginator.MESSAGES_LIST_MAPPER,
        )
        return list(paginator)

    def bulk_trash_message(self, message_ids: List[str]):
        """
        Moves provided messages to trash
        Args:
            message_ids: Message ids of the messages which need to be moved to trash
        """
        # Message and sender profile data will be updated in the webhook sync
        message_ids_moved = self.mailbox.batch_update_labels(
            message_ids, remove_label_ids=None, add_label_ids=[GmailKnownLabelName.TRASH.value]
        )
        logger.info(f"{len(message_ids_moved)}/{len(message_ids)} messages moved to trash")
        return len(message_ids_moved)

    def get_attachment(self, message_id: str, attachment_id: str):
        content = self.mailbox.get_attachment(message_id, attachment_id)
        attachment = BytesIO(base64.urlsafe_b64decode(content.encode("UTF-8")))
        return attachment

    def get_attachments(self, parsed_message: ParsedMessage):
        """
        Fetches and formats attachments from the message.

        For each attachment in the message, this method:
        1. Retrieves the attachment content using the email service
        2. Formats the attachment as a tuple of (filename, content, mimetype)

        Returns:
            List[Tuple[str, bytes, str]]: A list of tuples containing:
                - filename (str): The name of the attachment file
                - content (bytes): The binary content of the attachment
                - mimetype (str): The MIME type of the attachment
        """
        attachments = []
        for attachment_details in parsed_message.attachments:
            try:
                attachment_content = self.get_attachment(parsed_message.message_id, attachment_details["attachmentId"])
                if attachment_content:
                    attachment_content.seek(0)
                    content_bytes = attachment_content.read()
                    filename = attachment_details.get("filename")
                    mime_type = attachment_details.get("mimeType")

                    # Ensure all required attributes are available
                    if filename and content_bytes and mime_type:
                        attachments.append(
                            (
                                filename,
                                content_bytes,
                                mime_type,
                            )
                        )
                    else:
                        logger.warning(
                            f"Skipping attachment with missing attributes in message {parsed_message.message_id}. "
                            f"filename: {'present' if filename else 'missing'}, "
                            f"content: {'present' if content_bytes else 'missing'}, "
                            f"mimeType: {'present' if mime_type else 'missing'}"
                        )
                else:
                    logger.warning(
                        f"Could not fetch attachment content for ID {attachment_details['attachmentId']} in message {parsed_message.message_id}"
                    )
            except Exception as attach_exc:
                logger.exception(
                    f"Failed to fetch or process attachment ID {attachment_details.get('attachmentId', 'N/A')}",
                    extra={
                        "user": self.service.email,
                        "message_id": self.parsed_message.message_id,
                        "error_details": attach_exc,
                    },
                )

        return attachments

    def move_between_generic_labels(self, message_id: str, remove_label_name: str, add_label_name: str):
        """
        Moves a message from one generic label to another by adding and removing the corresponding Gmail labels.

        Args:
            message_id: The ID of the message to move
            remove_label_name: The original generic label (e.g., WHITE_LIST, ZAPPED)
            add_label_name: The target generic label
        """
        try:
            # Get the Gmail label names for the generic labels
            remove_label_generic_name = self.mailbot_labels.get(remove_label_name)
            add_label_generic_name = self.mailbot_labels.get(add_label_name)

            remove_label_id = self.get_label_id(remove_label_generic_name) if remove_label_generic_name else None
            add_label_id = self.get_label_id(add_label_generic_name) if add_label_generic_name else None

            self.mailbox.update_labels(
                message_id,
                remove_label_ids=[remove_label_id] if remove_label_id else [],
                add_label_ids=[add_label_id] if add_label_id else [],
            )

            logger.info(
                f"Successfully moved message {message_id} from {remove_label_generic_name} to {add_label_generic_name}"
            )
        except Exception as e:
            logger.exception(f"Failed to move message {message_id} between labels: {str(e)}")
