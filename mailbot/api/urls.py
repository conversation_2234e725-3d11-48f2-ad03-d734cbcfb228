from django.urls import path
from rest_framework.routers import SimpleRouter

from mailbot.api import views

router = SimpleRouter()
router.register("microsoft-auth", views.MicrosoftMailBotAuthViewSet, basename="mailbot-microsoft-auth")
router.register("google-auth", views.GoogleMailBotAuthViewSet, basename="mailbot-google-auth")
router.register("auto-cleaner", views.AutoCleanerViewSet, basename="mailbot-auto-cleaner")
router.register(
    "unified-inbox-manage-senders",
    views.********************************,
    basename="mailbot-unified-inbox-manage-senders",
)
router.register("unified-inbox-delete", views.UnifiedInboxDeleteViewSet, basename="mailbot-unified-inbox-delete")
router.register(
    "unified-inbox-unsubscribe", views.UnifiedInboxUnsubscribeViewSet, basename="mailbot-unified-inbox-unsubscribe"
)
router.register("bulk-delete", views.BulkDeleteViewSet, basename="mailbot-bulk-delete")
router.register("profile", views.MailBotProfileViewSet, basename="mailbot-profile")
router.register("checks", views.MailBotProfileChecksViewSet, basename="mailbot-checks")
router.register("secondary-profiles", views.SecondaryMailbotProfileViewSet, basename="secondary-mailbot-profile")
router.register("unsubscribe-details", views.SenderUnsubscribeDetailViewSet, basename="unsubscribe-details")
router.register(
    "unified-inbox-statistics", views.UnifiedInboxMailbotStatisticsViewSet, basename="unified-inbox-statistics"
)
router.register("statistics", views.MailBotStatisticsViewSet, basename="user-mailbot-statistics")
urlpatterns = router.urls

urlpatterns.extend(
    [
        path("actions/", views.ActionRequestHandlerAPIView.as_view(), name="handle_actions"),
        path("users/me/", views.LoggedInUserAPIView.as_view(), name="current-user"),
        path("outlook-webhook/", views.OutlookWebhookAPIView.as_view(), name="outlook-webhook"),
        path("gmail-webhook/", views.GmailWebhookAPIView.as_view(), name="gmail-webhook"),
        path("mail-operations/", views.HTMLEmailEventAPIView.as_view(), name="mail-operations"),
        path("amp-events/", views.AMPEmailEventAPIView.as_view(), name="amp-events"),
        path("pusher-auth/", views.PusherAuthAPIView.as_view(), name="pusher-auth"),
        path("support/", views.SupportAPIView.as_view(), name="support"),
        path("pricing-catalogue/", views.MailBotPriceCatalogueAPIView.as_view(), name="mailbot-pricing-catalogue"),
    ]
)
