import logging
from datetime import datetime
from mailbox import Message

import pytz
from constance import config as constance_config
from django.conf import settings
from django.contrib.auth import get_user_model, logout, login
from django.db.models import Q, Exists, OuterRef, F, Case, When, Float<PERSON>ield, Value, Bo<PERSON>anField
from django.http import Http404
from django.shortcuts import redirect, get_object_or_404
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from rest_framework import filters
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.exceptions import AuthenticationFailed, ValidationError
from rest_framework.generics import RetrieveAPIView, ListAPIView
from rest_framework.mixins import (
    ListModelMixin,
)
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.status import HTTP_400_BAD_REQUEST, HTTP_500_INTERNAL_SERVER_ERROR, HTTP_200_OK
from rest_framework.views import APIView
from rest_framework.viewsets import ViewSet, GenericViewSet

from accounts.api import serializers
from accounts.api.serializers import UserSerializer
from accounts.utils.base import verify_and_decode_oauth_state
from accounts.utils.google import get_google_sign_in_flow
from accounts.utils.microsoft import get_ms_email_id, get_ms_sign_in_flow, get_ms_token_from_code
from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from applications.models import EmailTemplate
from applications.utils.email import EmailBackend
from applications.utils.tracking_parameters import get_tracking_params
from execfn.common.applications import ApplicationTag
from execfn.settings import APP_ENV_PROD, APP_ENV_LOCAL
from mailbot.api.filters import SenderProfilesFilter
from mailbot.api.pagination import CustomLimitOffsetPagination
from mailbot.api.renderers import TextRenderer
from mailbot.api.serializers import (
    MailbotProfileSerializer,
    MicrosoftCallbackSerializer,
    SenderProfileSerializer,
    SecondaryMailBotProfilesSerializer,
    MailBotPriceCatalogueSerializer,
    SecondaryMailbotProfilePreferencesSerializer,
    UnifiedInboxUserAnalyticsSerializer,
    ActionRequestSerializer,
    UnifiedInboxManageSendersSerializer,
    UnifiedInboxDeleteSerializer,
    UnifiedInboxUnsubscribeSerializer,
)
from mailbot.authentication import (
    GmailWebhookAuthentication,
    AMPEmailEventAuthentication,
    HTMLEmailEventAuthentication,
    OutlookWebhookAuthentication,
)
from mailbot.models import (
    MailBotGenericLabel,
    MailBotUsageLog,
    SenderProfile,
    UserMailBotAnalytics,
    UserMailBotProfile,
    SecondaryMailBotProfilesThrough,
    WebhookLog,
    SenderUnsubscribeDetail,
)
from mailbot.permission import (
    HasAllScopesPermission,
    IsAssociatedProfileUser,
    IsPrimaryProfile,
    LinkedProfilesLimitNotExceeded,
    IsAssociatedMailbotProfile,
)
from mailbot.service.factory import MessageServiceFactory
from mailbot.tasks import process_gmail_event, unsubscribe_sender
from mailbot.tasks import process_outlook_event
from mailbot.utils.base import (
    activate_mailbot,
    deactivate_mailbot,
    create_and_update_scheduled_tasks,
    pre_login,
    check_mailbot_scopes,
)
from mailbot.utils.check import (
    can_disable_mailbot,
    can_enable_mailbot,
    can_unsubscribe_more_senders,
    can_delete_more_senders,
    is_mailbot_active,
    show_new_ui_popup,
    show_onboarding_tour,
    is_primary_profile,
)
from mailbot.utils.pricing import get_pricing_version
from mailbot.utils.defaults import (
    GOOGLE_MAILBOT_REQUIRED_SCOPES,
    OutlookKnownLabelName,
    GmailKnownLabelName,
    SubscriptionStatisticsKey,
    AnalyticsStatisticsKey,
    MailBotProfileMetadataKey,
)
from mailbot.utils.defaults import MICROSOFT_MAILBOT_REQUIRED_SCOPES
from mailbot.utils.email_scheduler import CreditCardMissingEmail
from mailbot.utils.login import set_cookies
from mailbot.utils.mail_operations import MailOperation
from mailbot.utils.profiles import create_mailbot_profile
from mailbot.utils.pusher import get_private_channel
from mailbot.utils.statistics import (
    update_statistics_in_analytics,
    update_mailbot_usage,
)
from mailbot.utils.subscription import check_login_code, create_free_user, get_active_subscription
from payments.models import (
    StripePrice,
    StripeSubscription,
    StripeInvoice,
    StripeCustomer,
    StripeCoupon,
    CouponPriceThrough,
)
from payments.utils.checkout_page import CheckoutFlow

from execfn.common.subquery import SQCount
from mailbot.utils.actions import DeleteAction, BaseAction, ActionProcessor

User = get_user_model()
logger = logging.getLogger(__name__)


# LOGIN AUTH API VIEWS
class LoggedInUserAPIView(RetrieveAPIView):
    serializer_class = UserSerializer

    def get_object(self):
        user = self.request.user
        try:
            user.user_mailbot_profile
        except UserMailBotProfile.DoesNotExist:
            # This happens when admin user logs in with different account
            raise Http404("No mailbot profile for the given user")
        else:
            return user


class AuthViewSet(ViewSet):
    @staticmethod
    def get_post_login_redirect_url(
        important_params, mailbot_scopes_information, user_mailbot_profile, mailbot_profile_created
    ):
        checkout_flow = CheckoutFlow()
        if user_mailbot_profile.metadata.get("onboarding_started_at"):
            # If user already onboarded then redirect to home page
            return settings.FRONTEND_BASE_URL
        if not (show_onboarding_tour := is_primary_profile(user_mailbot_profile)):
            # If mailbot profile is not primary, redirect them to homepage
            return settings.FRONTEND_BASE_URL
        if signup_code := important_params.get("signup_code"):
            if check_login_code(code=signup_code, user=user_mailbot_profile.user):
                # If user signs up successfully using signup code, redirect them to homepage
                return settings.FRONTEND_BASE_URL
        if not mailbot_scopes_information["all_scopes_granted_for_first_time"]:
            return settings.FRONTEND_BASE_URL
        # When user grants the scope for the first time, redirect them to subscription tab
        # Case 1: If price is present in query params, redirect to stripe checkout
        if pricing_nickname := important_params.get("stripe_payment_plan"):
            try:
                price = StripePrice.objects.get(nickname__iexact=pricing_nickname, active=True)
            except StripePrice.DoesNotExist:
                logger.error(
                    "Price does not exist while user login",
                    extra={
                        "pricing_nickname": pricing_nickname,
                        "user": user_mailbot_profile.user.email,
                    },
                )
                return f"{settings.FRONTEND_BASE_URL}/subscription"
            current_pricing_version = get_pricing_version(user_mailbot_profile=user_mailbot_profile)
            coupon = None
            if coupon_id := important_params.get("coupon"):
                try:
                    coupon = StripeCoupon.objects.get(id=coupon_id)
                except StripeCoupon.DoesNotExist:
                    logger.info(f"Invalid coupon_id {coupon_id}")
                    return f"{settings.FRONTEND_BASE_URL}/subscription"

            if not CouponPriceThrough.objects.filter(
                price=price,
                coupon=coupon,
                version=current_pricing_version,
            ).exists():
                logger.info(
                    f"coupon_id {coupon_id}, price {price.nickname}, pricing_version {current_pricing_version} is not supported"
                )
                return f"{settings.FRONTEND_BASE_URL}/subscription"
            if price.nickname == "freebie":
                create_free_user(
                    user_email=user_mailbot_profile.user.email,
                    price_nickname=price.nickname,
                    cancel_at=int((timezone.now() + timezone.timedelta(days=7)).timestamp()),
                )
                success_redirect_url = checkout_flow.get_success_redirect_url(
                    price, show_onboarding_tour, user_mailbot_profile.user
                )
                return success_redirect_url
            user_mailbot_profile.metadata[
                MailBotProfileMetadataKey.LAST_CHECKOUT_SESSION_CREATED_AT.value
            ] = timezone.now().isoformat()
            user_mailbot_profile.save(update_fields=("metadata",))
            CreditCardMissingEmail(user_mailbot_profile=user_mailbot_profile).schedule_email()

            redirect_url = checkout_flow.get_checkout_url(
                price, coupon, user_mailbot_profile.user, current_pricing_version, show_onboarding_tour
            )
            return redirect_url
        else:
            # Case 2: Price not present, redirect to subscription page
            return f"{settings.FRONTEND_BASE_URL}/subscription"

    @staticmethod
    def get_important_params_from_request(request):
        stripe_payment_plan = request.GET.get("stripe_payment_plan", None)
        coupon = request.GET.get("coupon", None)
        signup_code = request.GET.get("signup_code", None)
        query_params = dict(request.query_params) or {}
        tracking_params = get_tracking_params(query_params)
        important_params = {
            "tracking_parameters": tracking_params,
            "stripe_payment_plan": stripe_payment_plan,
            "coupon": coupon,
            "signup_code": signup_code,
        }
        return important_params


class MicrosoftMailBotAuthViewSet(AuthViewSet):
    # Bypass the enforce_csrf and session authentication
    authentication_classes = ()
    # Bypass the IsAuthenticated permission
    permission_classes = ()

    @action(detail=False, methods=["GET"], url_path="login")
    def login(self, request: Request):
        """
        Initiates an interactive login process using Microsoft Authentication Library (MSAL).
        The user is redirected to the callback for validation and initialization.

        Args:
            request (Request): The HTTP request.

        Returns:
            HttpResponseRedirect: A redirect to the MSAL authentication URI.
        """
        important_params = self.get_important_params_from_request(request)
        flow = get_ms_sign_in_flow(
            scopes=MICROSOFT_MAILBOT_REQUIRED_SCOPES,
            callback_uri=f"{'http://localhost:8000' if settings.APP_ENV == APP_ENV_LOCAL else settings.BACKEND_BASE_URL}/api/v1/mailbot/microsoft-auth/callback/",
            metadata=important_params,
        )
        request.session["auth_flow"] = flow
        return redirect(flow["auth_uri"])

    @action(detail=False, methods=["GET"], url_path="callback")
    def callback(self, request: Request):
        """
        After successfully entering credentials, MSAL will redirect to our callback
        with code in query params.
        """
        state_token = request.query_params.get("state")
        try:
            state_information = verify_and_decode_oauth_state(settings.SERVICE_PROVIDER_MICROSOFT, state_token)
        except AuthenticationFailed:
            return redirect(settings.FRONTEND_BASE_URL)
        result = get_ms_token_from_code(request)
        if error := result.get("error"):
            if error == "access_denied":
                logger.info("User denied access to microsoft scopes for mailbot")
            else:
                error_description = result.get("error_description")
                logger.exception(
                    "Error while signing in to microsoft mailbot",
                    extra={"error": error, "error_description": error_description},
                )
            return redirect(settings.FRONTEND_BASE_URL)
        serializer = MicrosoftCallbackSerializer(data=result)
        serializer.is_valid(raise_exception=True)
        validated_data = serializer.validated_data
        email = get_ms_email_id(validated_data.get("access_token"))
        if not email:
            return redirect(settings.FRONTEND_BASE_URL)
        name = validated_data.get("id_token_claims", {}).get("name", "")
        if name:
            first_name, *last_name_parts = name.split()
            last_name = " ".join(last_name_parts)
        else:
            first_name, last_name = "", ""
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return redirect(settings.FRONTEND_BASE_URL)
        granted_scopes = validated_data.get("scope", "").split(" ")
        mailbot_scopes_information = check_mailbot_scopes(
            user=user, required_scopes=MICROSOFT_MAILBOT_REQUIRED_SCOPES, granted_scopes=granted_scopes
        )
        authentication = {
            "token_type": validated_data.get("token_type"),
            "access_token": validated_data.get("access_token"),
            "scope": granted_scopes,
            "expires_in": validated_data.get("expires_in"),
            "refresh_token": validated_data.get("refresh_token"),
            "expires_at": int(datetime.now().timestamp()) + validated_data.get("expires_in"),
        }
        user_mailbot_profile, mailbot_profile_created = create_mailbot_profile(
            user,
            settings.SERVICE_PROVIDER_MICROSOFT,
            authentication,
            mailbot_scopes_information=mailbot_scopes_information,
        )
        create_and_update_scheduled_tasks(user_mailbot_profile, user)
        pre_login(
            user_mailbot_profile,
            mailbot_profile_created,
            state_information,
        )
        login(request, user)
        redirect_url = self.get_post_login_redirect_url(
            important_params=state_information.get("metadata", {}),
            mailbot_scopes_information=mailbot_scopes_information,
            mailbot_profile_created=mailbot_profile_created,
            user_mailbot_profile=user_mailbot_profile,
        )
        response = redirect(redirect_url)
        set_cookies(response, user_mailbot_profile)
        return response


class GoogleMailBotAuthViewSet(AuthViewSet):
    # Bypass the enforce_csrf and session authentication
    authentication_classes = ()
    # Bypass the IsAuthenticated permission
    permission_classes = ()
    CALLBACK_URI = f"{'http://localhost:8000' if settings.APP_ENV == APP_ENV_LOCAL else settings.BACKEND_BASE_URL}/api/v1/mailbot/google-auth/callback/"

    @action(detail=False, methods=["GET"], url_path="login")
    def login(self, request: Request):
        important_params = self.get_important_params_from_request(request)
        flow = get_google_sign_in_flow(
            client_id=settings.GOOGLE.get("client_id"),
            client_secret=settings.GOOGLE.get("client_secret"),
            scopes=GOOGLE_MAILBOT_REQUIRED_SCOPES,
            callback_uri=self.CALLBACK_URI,
            metadata=important_params,
        )
        # Generate URL for request to Google's OAuth 2.0 server.
        authorization_url, _ = flow.authorization_url(
            # Enable offline access so that you can refresh an access token without
            # re-prompting the user for permission. Recommended for web server apps.
            access_type="offline",
            prompt="consent",
            # Enable incremental authorization. Recommended as a best practice.
            include_granted_scopes="false",
        )
        return redirect(authorization_url)

    @action(detail=False, methods=["GET"], url_path="callback")
    def callback(self, request: Request):
        """
        After successfully entering credentials, MSAL will redirect to our callback
        with code in query params.
        References:
            oauthlib/oauth2/rfc6749/parameters.py: Set `OAUTHLIB_RELAX_TOKEN_SCOPE` os environ if subset of scopes can
                be used for logging in
        """
        response = redirect(settings.FRONTEND_BASE_URL)
        state_token = request.query_params.get("state")
        code = request.query_params.get("code")
        try:
            state_information = verify_and_decode_oauth_state(settings.SERVICE_PROVIDER_GOOGLE, state_token)
        except AuthenticationFailed:
            return response
        flow = get_google_sign_in_flow(
            client_id=settings.GOOGLE.get("client_id"),
            client_secret=settings.GOOGLE.get("client_secret"),
            scopes=GOOGLE_MAILBOT_REQUIRED_SCOPES,
            callback_uri=self.CALLBACK_URI,
        )
        try:
            flow.fetch_token(code=code)
        except Exception as exc:
            # User clicks "cancel" on any consent screen
            logger.info("Login via Google failed due to unknown error")
            return response
        else:
            credentials: Credentials = flow.credentials
            # Fetch user details and get or create a user
            service = build("oauth2", "v2", credentials=credentials)
            user_info = service.userinfo().get().execute()
            user_picture = user_info.get("picture")
            email = user_info.get("email")
            if not email:
                logger.exception(
                    "Google user email not found in user info while logging in", extra={"user_info": user_info}
                )
                return response
            name = user_info.get("name", "")
            first_name, *last_name_parts = name.split()
            last_name = " ".join(last_name_parts)
            user, user_created = User.objects.get_or_create(
                email=email, defaults={"username": email, "first_name": first_name, "last_name": last_name}
            )
            # Prepare auth details and create or update user mailbot profile
            granted_scopes = credentials.granted_scopes
            mailbot_scopes_information = check_mailbot_scopes(
                user=user, required_scopes=GOOGLE_MAILBOT_REQUIRED_SCOPES, granted_scopes=granted_scopes
            )
            authentication = {
                "access_token": credentials.token,
                "scope": granted_scopes,
                "refresh_token": credentials.refresh_token,
                "expires_at": credentials.expiry.timestamp(),
            }
            user_mailbot_profile, mailbot_profile_created = create_mailbot_profile(
                user,
                settings.SERVICE_PROVIDER_GOOGLE,
                authentication,
                mailbot_scopes_information=mailbot_scopes_information,
                user_picture=user_picture,
            )
            create_and_update_scheduled_tasks(user_mailbot_profile, user)
            pre_login(
                user_mailbot_profile,
                mailbot_profile_created,
                state_information,
            )
            login(request, user)
            redirect_url = self.get_post_login_redirect_url(
                important_params=state_information.get("metadata", {}),
                mailbot_scopes_information=mailbot_scopes_information,
                mailbot_profile_created=mailbot_profile_created,
                user_mailbot_profile=user_mailbot_profile,
            )
            response = redirect(redirect_url)
            set_cookies(response, user_mailbot_profile)
            return response


# WEBHOOK API VIEWS
class OutlookWebhookAPIView(APIView):
    """When setting up webhook, microsoft will first send POST request with validationToken query param to check
    if the site is up and working. We must send the same validationToken back in response for successfully establishing
    the watch channel.
    Note:
        We have to send response when processing inbox mail otherwise we will get another event
    """

    authentication_classes = (OutlookWebhookAuthentication,)
    # Need text/plain when outlook send the events
    renderer_classes = [TextRenderer]
    permission_classes = ()

    def post(self, request: Request):
        validation_token = request.query_params.get("validationToken")
        if validation_token:
            logger.info("validating webhook receiving url for outlook")
            return Response(
                validation_token,
                headers={"Content-Type": "text/plain"},
            )
        else:
            try:
                user_mailbot_profile = UserMailBotProfile.all_objects.get(
                    user=request.user, service_provider=settings.SERVICE_PROVIDER_MICROSOFT
                )
            except UserMailBotProfile.DoesNotExist:
                # This should never happen
                logger.exception(
                    "Outlook event received for a user whose mailbot profile is hard deleted.",
                    extra={"user": request.user.email},
                )
            else:
                if user_mailbot_profile.deleted:
                    logger.info(
                        f"Outlook event received for a user {request.user.email} whose mailbot profile is soft deleted.",
                    )
                else:
                    # Only process webhook event if mailbot is enabled
                    if is_mailbot_active(user_mailbot_profile=user_mailbot_profile):
                        webhook_log = WebhookLog.objects.create(
                            user_mailbot_profile=user_mailbot_profile,
                            status=WebhookLog.SCHEDULED,
                            metadata={
                                "payload": request.data,
                                "mail_subscription_id": request.mail_subscription_id,
                                "user_mailbot_profile_id": user_mailbot_profile.id,
                            },
                        )
                        process_outlook_event.delay(webhook_log.id)
            finally:
                return Response()


class GmailWebhookAPIView(APIView):
    authentication_classes = (GmailWebhookAuthentication,)
    permission_classes = ()

    def post(self, request: Request):
        """
        Handles incoming HTTP POST requests from Google Pub/Sub, and processes push notifications.
        Args:
            request: HTTP POST request containing data from Google Pub/Sub.
        Returns:
            HttpResponse: An acknowledgment indicating whether the webhook successfully processed the message.
        """
        try:
            user_mailbot_profile = UserMailBotProfile.all_objects.get(
                user=request.user, service_provider=settings.SERVICE_PROVIDER_GOOGLE
            )
        except UserMailBotProfile.DoesNotExist:
            # This should never happen
            logger.exception(
                "Gmail event received for a user whose mailbot profile is hard deleted.",
                extra={"user": request.user.email},
            )
        else:
            if user_mailbot_profile.deleted:
                logger.info(
                    f"Gmail event received for a user {request.user.email} whose mailbot profile is soft deleted."
                )
            else:
                # Only process webhook event if mailbot is enabled
                history_id = getattr(request, "history_id", None)
                if history_id and is_mailbot_active(user_mailbot_profile=user_mailbot_profile):
                    webhook_log = WebhookLog.objects.create(
                        user_mailbot_profile=user_mailbot_profile,
                        status=WebhookLog.SCHEDULED,
                        metadata={"history_id": int(history_id), "user_mailbot_profile_id": user_mailbot_profile.id},
                    )
                    process_gmail_event.delay(webhook_log.id)
        finally:
            return Response()


class UnifiedInboxMailbotStatisticsViewSet(GenericViewSet):
    serializer_class = UnifiedInboxUserAnalyticsSerializer
    permission_classes = (IsAssociatedMailbotProfile,)

    @action(detail=False, methods=("GET",), url_path="all")
    def all_statistics(self, request: Request):
        # TODO : This should be coming through cookies
        mailbot_profile_ids = self.request.query_params.getlist("mailbot_profile_ids", [])
        mailbot_profile_ids = list(map(int, mailbot_profile_ids))
        analytics_qs = UserMailBotAnalytics.objects.filter(user_mailbot_profile_id__in=mailbot_profile_ids)
        serializer = UnifiedInboxUserAnalyticsSerializer(analytics_qs, many=True)
        return Response(data=serializer.data)


class UnifiedInboxDeleteViewSet(GenericViewSet, ListModelMixin):
    serializer_class = UnifiedInboxDeleteSerializer
    pagination_class = CustomLimitOffsetPagination
    permission_classes = (IsAssociatedMailbotProfile,)

    def get_queryset(self):
        all_associated_profile_ids = SecondaryMailBotProfilesThrough.get_associated_mailbot_profile_ids(
            user_mailbot_profile_id=self.request.user.user_mailbot_profile.id
        )
        all_associated_profile_ids.append(self.request.user.user_mailbot_profile.id)
        if self.action == "list":
            # TODO : This should be coming through cookies
            mailbot_profile_ids = self.request.query_params.getlist("mailbot_profile_ids", [])
            mailbot_profile_ids = list(map(int, mailbot_profile_ids))
            queryset = SenderProfile.objects.filter(
                Q(total_count__gte=0),
                ~Q(
                    sender_email__in=UserMailBotProfile.objects.filter(id__in=all_associated_profile_ids).values(
                        "user__email"
                    )
                ),
                ~Q(sender_email=settings.SES_FROM_EMAIL),
                user_mailbot_profile_id__in=mailbot_profile_ids,
            ).order_by("-scanned_count", "sender_email", "-recent_timestamp")
        else:
            queryset = SenderProfile.objects.filter(user_mailbot_profile__id__in=all_associated_profile_ids)
        return queryset


class UnifiedInboxUnsubscribeViewSet(GenericViewSet, ListModelMixin):
    serializer_class = UnifiedInboxUnsubscribeSerializer
    pagination_class = CustomLimitOffsetPagination
    permission_classes = (IsAssociatedMailbotProfile,)

    def get_queryset(self):
        all_associated_profile_ids = SecondaryMailBotProfilesThrough.get_associated_mailbot_profile_ids(
            user_mailbot_profile_id=self.request.user.user_mailbot_profile.id
        )
        all_associated_profile_ids.append(self.request.user.user_mailbot_profile.id)
        if self.action == "list":
            # TODO : This should be coming through cookies
            mailbot_profile_ids = self.request.query_params.getlist("mailbot_profile_ids", [])
            mailbot_profile_ids = list(map(int, mailbot_profile_ids))

            sender_unsubscribe_detail_subquery = SenderUnsubscribeDetail.objects.filter(
                Q(unsubscribe_mail_to__isnull=False) | Q(unsubscribe_link_one_click=True),
                user_mailbot_profile__id__in=mailbot_profile_ids,
                sender_profile_id=OuterRef("id"),
            ).only("id")
            queryset = (
                SenderProfile.objects.annotate(
                    mailing_list_exists=Exists(sender_unsubscribe_detail_subquery),
                )
                .filter(
                    mailing_list_exists=True,
                    user_mailbot_profile__id__in=mailbot_profile_ids,
                )
                .order_by("-scanned_count", "sender_email", "-recent_timestamp")
                .prefetch_related("user_mailbot_profile")
            )
        else:
            queryset = SenderProfile.objects.filter(user_mailbot_profile__id__in=all_associated_profile_ids)
        return queryset


class UnifiedInboxManageSendersViewSet(GenericViewSet, ListModelMixin):
    serializer_class = UnifiedInboxManageSendersSerializer
    pagination_class = CustomLimitOffsetPagination
    permission_classes = (IsAssociatedMailbotProfile,)

    def get_queryset(self):
        all_associated_profile_ids = SecondaryMailBotProfilesThrough.get_associated_mailbot_profile_ids(
            user_mailbot_profile_id=self.request.user.user_mailbot_profile.id
        )
        all_associated_profile_ids.append(self.request.user.user_mailbot_profile.id)

        if self.action == "list":
            # TODO : This should be coming through cookies
            mailbot_profile_ids = self.request.query_params.getlist("mailbot_profile_ids", [])
            mailbot_profile_ids = list(map(int, mailbot_profile_ids))

            queryset = SenderProfile.objects.filter(
                Q(total_count__gte=0),
                ~Q(
                    sender_email__in=UserMailBotProfile.objects.filter(id__in=all_associated_profile_ids).values(
                        "user__email"
                    )
                ),
                ~Q(sender_email=settings.SES_FROM_EMAIL),
                user_mailbot_profile__id__in=mailbot_profile_ids,
            ).order_by("-total_count", "sender_email", "-recent_timestamp")
        else:
            queryset = SenderProfile.objects.filter(user_mailbot_profile__id__in=all_associated_profile_ids)
        return queryset


# MODEL API VIEWS
class AutoCleanerViewSet(GenericViewSet, ListModelMixin):
    serializer_class = SenderProfileSerializer
    pagination_class = CustomLimitOffsetPagination
    filter_backends = (filters.OrderingFilter, DjangoFilterBackend)
    ordering_fields = (
        "total_count",
        "sender_email",
        "read_fraction",
        "recent_timestamp",
    )
    filterset_class = SenderProfilesFilter
    lookup_field = "id"

    def get_queryset(self):
        if self.action == "list":
            queryset = (
                SenderProfile.objects.select_related("user_mailbot_profile")
                .filter(
                    Q(total_count__gt=0)
                    | Q(user_training=MailBotGenericLabel.ZAPPED.value)
                    | Q(user_action__isnull=False),
                    ~Q(sender_email=self.request.user.email),
                    ~Q(sender_email=settings.SES_FROM_EMAIL),
                    user_mailbot_profile=self.request.user.user_mailbot_profile,
                )
                .annotate(
                    read_fraction=Case(
                        When(total_count=0, then=0),
                        default=F("read_count") * 1.0 / F("total_count"),
                        output_field=FloatField(),
                    ),
                )
            )
        else:
            queryset = SenderProfile.objects.filter(user_mailbot_profile=self.request.user.user_mailbot_profile)
        return queryset

    @action(detail=False, methods=("POST",), url_path="update-training")
    def update_training(self, request):
        sender_profiles = request.data.get("sender_profiles", [])
        serializer = self.get_serializer(data=sender_profiles, many=True)
        serializer.is_valid(raise_exception=True)
        sender_profile_ids = [x["id"] for x in sender_profiles]
        user_training = request.data.get("user_training")
        if user_training == "":
            user_training = None
        SenderProfile.objects.filter(id__in=sender_profile_ids).update(user_training=user_training)
        if user_training is not None:
            # Remove user defined action if user_training is set
            SenderProfile.objects.filter(id__in=sender_profile_ids).update(user_action=None, user_action_reason=None)
        return Response({"message": "Training updated successfully"})

    @action(detail=True, methods=("POST", "DELETE"), url_path="custom-workflow")
    def custom_workflow(self, request, id):
        sender_profile: SenderProfile = self.get_object()
        if request.method == "POST":
            user_action_value = request.data.get("action", None)
            action_reason = request.data.get("action_reason", None)
            if not user_action_value:
                return Response({"message": "No user action provided"}, status=status.HTTP_400_BAD_REQUEST)
            sender_profile.user_action = user_action_value
            sender_profile.user_action_reason = action_reason
            sender_profile.save(update_fields=("user_action", "user_action_reason"))
            return Response({"message": "User action updated successfully"})
        elif request.method == "DELETE":
            sender_profile.user_action = None
            sender_profile.user_action_reason = None
            sender_profile.save(update_fields=("user_action", "user_action_reason"))
            return Response({"message": "User action deleted successfully"})


class BulkDeleteViewSet(GenericViewSet, ListModelMixin):
    serializer_class = SenderProfileSerializer
    pagination_class = CustomLimitOffsetPagination
    filter_backends = (filters.OrderingFilter, DjangoFilterBackend)
    ordering_fields = (
        "total_count",
        "sender_email",
        "read_fraction",
        "recent_timestamp",
        "inbox_count",
        "scanned_count",
    )
    filterset_class = SenderProfilesFilter
    lookup_field = "id"

    def get_queryset(self):
        if self.action == "list":
            queryset = (
                SenderProfile.objects.select_related("user_mailbot_profile")
                .filter(
                    Q(total_count__gt=0) | Q(user_action__isnull=False),
                    ~Q(sender_email=self.request.user.email),
                    ~Q(sender_email=settings.SES_FROM_EMAIL),
                    user_mailbot_profile=self.request.user.user_mailbot_profile,
                )
                .annotate(
                    read_fraction=Case(
                        When(total_count=0, then=0),
                        default=F("read_count") * 1.0 / F("total_count"),
                        output_field=FloatField(),
                    ),
                    inbox_count=F("total_count") - F("sent_count"),
                )
                .prefetch_related("user_mailbot_profile")
            )
        else:
            queryset = SenderProfile.objects.filter(user_mailbot_profile=self.request.user.user_mailbot_profile)
        return queryset

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        response.data["deleted_count"] = SenderProfile.objects.filter(
            user_mailbot_profile=request.user.user_mailbot_profile,
            user_action_reason=SenderProfile.USER_ACTION_REASON_MARKED_DELETED,
            user_action__isnull=False,
        ).count()
        return response

    @action(detail=False, methods=("GET",), url_path="count")
    def bulk_delete_count(self, request):
        """
        Returns the count of all emails from a given sender that are eligible for bulk deletion.
        Args:
            request: HTTP GET request containing the sender email.
        """
        sender_email = request.query_params.get("sender_email", None)
        if not sender_email:
            return Response({"message": "No sender email provided"}, status=status.HTTP_400_BAD_REQUEST)
        message_service = MessageServiceFactory.get_message_service(self.request.user.user_mailbot_profile)
        message_ids = message_service.get_messages_from_sender(sender_email=sender_email)
        return Response({"count": len(message_ids)})

    @action(detail=True, methods=("POST",), url_path="trash")
    def bulk_delete(self, request, id):
        """
        Handles bulk movement of emails to trash from a given sender
        Args:
            request: HTTP POST request containing data from Google Pub/Sub.
        """
        if not check_feature(
            user_id=request.user.id,
            feature_flag=MailBotFeatureFlag.BULK_DELETE,
            application_tag=ApplicationTag.MailBot,
        ):
            return Response(status=status.HTTP_403_FORBIDDEN)
        sender_profile: SenderProfile = self.get_object()
        user_action = request.data.get("action", None)
        if not action:
            return Response({"message": "No user action provided"}, status=status.HTTP_400_BAD_REQUEST)

        if sender_profile.user_action_reason != SenderProfile.USER_ACTION_REASON_MARKED_DELETED:
            if not can_delete_more_senders(user_mailbot_profile=request.user.user_mailbot_profile):
                return Response(status=status.HTTP_402_PAYMENT_REQUIRED)
            sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_MARKED_DELETED
            message_service = MessageServiceFactory.get_message_service(self.request.user.user_mailbot_profile)
            messages_trashed = message_service.trash_historical_messages(sender_profile.sender_email)
            update_statistics_in_analytics(
                user_mailbot_profile=request.user.user_mailbot_profile,
                key=AnalyticsStatisticsKey.TRASHED_COUNT.value,
                value=messages_trashed,
            )
            update_mailbot_usage(
                user_mailbot_profile=request.user.user_mailbot_profile,
                key=SubscriptionStatisticsKey.SENDERS_TRASHED.value,
                value=1,
            )
            sender_profile.user_action = user_action
            sender_profile.save(update_fields=("user_action", "user_action_reason"))
            return Response({"message": "Messages deleted successfully"})
        else:
            sender_profile.user_action = user_action
            sender_profile.save(update_fields=("user_action",))
            return Response({"message": "Future preference for delete action updated successfully"})


class MailBotProfileViewSet(GenericViewSet):
    serializer_class = MailbotProfileSerializer

    def get_object(self):
        try:
            return UserMailBotProfile.objects.get(user=self.request.user)
        except UserMailBotProfile.DoesNotExist:
            raise Http404("No mailbot profile for the given user")

    @action(detail=False, methods=("GET", "DELETE"), url_path="me")
    def profile(self, request):
        profile = self.get_object()
        if request.method == "GET":
            serializer = self.get_serializer(profile)
            return Response(serializer.data)
        else:
            deactivate_mailbot(profile)
        try:
            profile.soft_delete()
        except:
            activate_mailbot(profile)
            logger.exception(f"Exception while deleting user mailbot profile", extra={"id": profile.id})
            return Response(status=HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            response = Response()
            cookies_to_delete = ("csrftoken", "mailbot_profile_id", "sessionid", "service_provider")
            for cookie_to_delete in cookies_to_delete:
                response.delete_cookie(cookie_to_delete, domain=settings.COOKIE_DOMAIN, samesite="None")
            return response

    @action(detail=False, methods=("PUT",), url_path="preferences")
    def update_preference(self, request):
        instance = self.get_object()
        preference = request.data.get("preference")

        if "mailbot_enabled" in preference:
            is_mailbot_enabled = preference["mailbot_enabled"]
            was_mailbot_enabled = instance.preferences.get("mailbot_enabled", False)
            if was_mailbot_enabled != is_mailbot_enabled:
                if not is_mailbot_enabled:
                    if settings.APP_ENV == APP_ENV_PROD:
                        return Response(status=HTTP_400_BAD_REQUEST, data={"error": "Mailbot cannot be disabled"})
                    else:
                        deactivate_mailbot(instance)
                else:
                    activate_mailbot(instance)
        if "digest_timezone" in preference or "digest_time" in preference:
            if "digest_timezone" in preference:
                digest_timezone = pytz.timezone(preference["digest_timezone"])
            else:
                digest_timezone = pytz.timezone(instance.preferences.get("digest_timezone", "UTC"))
            if "digest_time" in preference:
                digest_hour, digest_minute = preference["digest_time"].split(":")
            else:
                digest_hour, digest_minute = instance.preferences["digest_time"].split(":")
            user_local_time = digest_timezone.localize(
                timezone.datetime(2023, 1, 1, int(digest_hour), int(digest_minute))
            )
            universal_time = user_local_time.astimezone(timezone.utc)
            instance.preferences["digest_timeslot"] = universal_time.hour * 2 + universal_time.minute // 30

        for key, value in preference.items():
            logger.info(f"Updating preference {key} to {value}")
            instance.preferences[key] = value
        instance.save()
        return Response("Preference updated successfully")

    @action(
        detail=False,
        methods=("PUT",),
        url_path="preferences/secondary-profile",
        permission_classes=[IsAssociatedProfileUser],
    )
    def update_secondary_preference(self, request):
        preference = request.data.get("preference")
        try:
            mailbot_profile_id_to_update = request.data.get("mailbot_profile_id_to_update")
            instance = get_object_or_404(UserMailBotProfile, id=mailbot_profile_id_to_update)
            serializer = SecondaryMailbotProfilePreferencesSerializer(
                data=preference, partial=True, context={"user_mailbot_profile": instance}
            )
            serializer.is_valid(raise_exception=True)
            instance.secondary_profile_preferences.update(serializer.validated_data)
            instance.save(update_fields=["secondary_profile_preferences"])
            return Response(status=HTTP_200_OK, data={"message": "Settings updated successfully"})
        except Exception as e:
            logger.exception(f"Exception while updating secondary profile preference", extra={"error": e})
            return Response(
                status=HTTP_500_INTERNAL_SERVER_ERROR,
                data={"error": "An error occurred while updating the secondary profile preference"},
            )

    @action(detail=False, methods=("POST",), url_path="onboarding-tour-viewed")
    def onboarding_tour_viewed(self, request):
        user_mailbot_profile = request.user.user_mailbot_profile
        last_onboarding_tour_step_viewed = request.data.get("last_onboarding_tour_step_viewed")
        if last_onboarding_tour_step_viewed is None:
            return Response({"message": "Onboarding tour step must be provided"}, status=status.HTTP_400_BAD_REQUEST)
        user_mailbot_profile.metadata[
            MailBotProfileMetadataKey.LAST_ONBOARDING_TOUR_STEP_VIEWED.value
        ] = last_onboarding_tour_step_viewed
        user_mailbot_profile.save(update_fields=("metadata",))
        return Response({"message": "Last onboarding tour step updated successfully"})

    @action(detail=False, methods=("POST",), url_path="user-role")
    def user_role(self, request):
        user_mailbot_profile = request.user.user_mailbot_profile
        user_role = request.data.get("user_role")
        if not user_role:
            return Response({"message": "User role must be provided"}, status=status.HTTP_400_BAD_REQUEST)
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.USER_ROLE.value] = user_role
        user_mailbot_profile.save(update_fields=("metadata",))
        return Response({"message": "User role updated successfully"})


class MailBotProfileChecksViewSet(GenericViewSet):
    @action(detail=False, methods=("GET",), url_path="is-active")
    def is_active(self, request):
        user_mailbot_profile = request.user.user_mailbot_profile
        active = is_mailbot_active(user_mailbot_profile)
        return Response(active)

    @action(detail=False, methods=("GET",), url_path="can-enable")
    def can_enable(self, request):
        user_mailbot_profile = request.user.user_mailbot_profile
        can_enable = can_enable_mailbot(user_mailbot_profile)
        return Response(can_enable)

    @action(detail=False, methods=("GET",), url_path="can-disable")
    def can_disable(self, request):
        user_mailbot_profile = request.user.user_mailbot_profile
        can_disable = can_disable_mailbot(user_mailbot_profile)
        return Response(can_disable)

    @action(detail=False, methods=("POST",), url_path="new-ui-viewed")
    def new_ui_viewed(self, request):
        user_mailbot_profile = request.user.user_mailbot_profile
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.NEW_UI_VIEWED.value] = True
        user_mailbot_profile.save()
        return Response({"message": "New UI viewed updated successfully"})

    @action(detail=False, methods=("GET",), url_path="show-new-ui-popup")
    def show_new_ui_popup(self, request):
        user_mailbot_profile = request.user.user_mailbot_profile
        data = show_new_ui_popup(user_mailbot_profile)
        return Response(data)

    @action(detail=False, methods=("GET",), url_path="show-onboarding-tour")
    def show_onboarding_tour(self, request):
        user_mailbot_profile = request.user.user_mailbot_profile
        data = show_onboarding_tour(user_mailbot_profile)
        return Response(data)


# EMAIL EVENT API VIEWS
class HTMLEmailEventAPIView(APIView):
    """
    API view for handling HTML email events.
    """

    authentication_classes = (HTMLEmailEventAuthentication,)
    permission_classes = ()

    def post(self, request: Request):
        mail_operation = MailOperation(user_mailbot_profile=request.user_mailbot_profile, payload=request.payload)
        data = mail_operation.process_mail_operation()
        return Response(data=data)


class AMPEmailEventAPIView(APIView):
    authentication_classes = (AMPEmailEventAuthentication,)
    permission_classes = ()

    def post(self, request: Request):
        amp_email_sender = request.headers.get("Amp-Email-Sender")
        origin = request.headers.get("Origin")

        message_id = request.payload.get("message_id")
        undo = request.parsed_body.get(f"undo-for-{message_id}") == "true"
        data = MailOperation(
            user_mailbot_profile=request.user_mailbot_profile, payload=request.payload, undo=undo
        ).process_mail_operation()

        # According to AMP requirements, it is mandatory to include the 'content-type' header in the response.
        # The implementation of rest_framework.response.Response is designed to remove the 'content-type' header
        # if the payload in the response is not present.
        # To address this, we include an empty dictionary as the payload.
        response = Response(
            data=data,
            headers={
                "Access-Control-Allow-Origin": origin,
                "AMP-Email-Allow-Sender": amp_email_sender,
                "Content-Type": "application/json",
                "Access-Control-Allow-Headers": "Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token",
                "Access-Control-Allow-Credentials": "true",
                "AMP-Access-Control-Allow-Source-Origin": request.query_params.get("__amp_source_origin", ""),
                "Access-Control-Expose-Headers": "AMP-Access-Control-Allow-Source-Origin",
            },
        )
        return response


class SecondaryMailbotProfileViewSet(ViewSet):
    GOOGLE_AUTH_CALLBACK_URI = f"{'http://localhost:8000' if settings.APP_ENV == APP_ENV_LOCAL else settings.BACKEND_BASE_URL}/api/v1/mailbot/google-auth/callback/"

    @action(
        detail=False,
        methods=["GET"],
        url_path="google/add",
        permission_classes=[IsPrimaryProfile, LinkedProfilesLimitNotExceeded],
    )
    def add_google_profile(self, request: Request):
        """
        Add a secondary google mailbot profile to the current user's profile. We return with 400 in case the current
        user's profile is not a primary profile.
        """
        user_mailbot_profile = request.user.user_mailbot_profile
        flow = get_google_sign_in_flow(
            client_id=settings.GOOGLE.get("client_id"),
            client_secret=settings.GOOGLE.get("client_secret"),
            scopes=GOOGLE_MAILBOT_REQUIRED_SCOPES,
            callback_uri=self.GOOGLE_AUTH_CALLBACK_URI,
            metadata={
                "primary_mailbot_profile_id": user_mailbot_profile.id,
                "forwarding_policy": request.query_params.get("forwarding_policy"),
                "digest_frequency": request.query_params.get("digest_frequency"),
            },
        )
        # Generate URL for request to Google's OAuth 2.0 server.
        authorization_url, _ = flow.authorization_url(
            # Enable offline access so that you can refresh an access token without
            # re-prompting the user for permission. Recommended for web server apps.
            access_type="offline",
            prompt="consent",
            # Enable incremental authorization. Recommended as a best practice.
            include_granted_scopes="false",
            login_hint=request.query_params.get("login_hint"),
        )
        logout(request)
        return redirect(authorization_url)

    @action(detail=False, methods=["POST"], url_path="switch")
    def switch(self, request: Request):
        """
        Switch user mailbot profile between any related profile. In case the profile to switch is not related to the
        current profile, we return with 400.
        """
        if "mailbot_profile_id" not in request.COOKIES:
            # This should ideally never happen
            logger.exception("MailBot Profile ID cookie was not found while fetching secondary profiles")
            return Response(status=HTTP_400_BAD_REQUEST, data={"error": "MailBot Profile ID cookie was not found"})
        current_user_mailbot_profile_id = int(request.COOKIES["mailbot_profile_id"])
        user_profile_to_switch_id = request.data["user_profile_to_switch_id"]
        # Check if the current profile and the profile to switch are related
        allow_mailbot_profile_switch = SecondaryMailBotProfilesThrough.objects.filter(
            Q(
                primary_mailbot_profile_id=current_user_mailbot_profile_id,
                secondary_mailbot_profile_id=user_profile_to_switch_id,
            )
            | Q(
                primary_mailbot_profile_id=user_profile_to_switch_id,
                secondary_mailbot_profile_id=current_user_mailbot_profile_id,
            )
        ).exists()
        if allow_mailbot_profile_switch:
            user_mailbot_profile = UserMailBotProfile.objects.select_related("user").get(id=user_profile_to_switch_id)
            user = user_mailbot_profile.user
            logout(request)
            login(request, user)
            response = Response(data=UserSerializer(user).data)
            set_cookies(response, user_mailbot_profile)
        else:
            response = Response(
                status=HTTP_400_BAD_REQUEST,
                data={"error": "User profile to switch is not associated with the current user"},
            )
        return response

    def list(self, request: Request):  # noqa (function may be static)
        """
        List all mailbot profiles for a user (primary and all secondary profiles)
        """
        if "mailbot_profile_id" not in request.COOKIES:
            # This should ideally never happen
            logger.exception("MailBot Profile ID cookie was not found while fetching secondary profiles")
            return Response(status=HTTP_400_BAD_REQUEST, data={"error": "MailBot Profile ID cookie was not found"})

        user_mailbot_profile_id = int(request.COOKIES["mailbot_profile_id"])
        all_associated_mailbot_profile_ids = SecondaryMailBotProfilesThrough.get_associated_mailbot_profile_ids(
            user_mailbot_profile_id
        )

        queryset = (
            UserMailBotProfile.objects.select_related("user")
            .filter(id__in=[user_mailbot_profile_id, *all_associated_mailbot_profile_ids])
            .annotate(
                primary=~Exists(
                    SecondaryMailBotProfilesThrough.objects.filter(secondary_mailbot_profile_id=OuterRef("id"))
                )
            )
        )
        serializer = SecondaryMailBotProfilesSerializer(queryset, many=True, context={"request": request})
        return Response(serializer.data)


class MailBotStatisticsViewSet(GenericViewSet):
    @action(
        detail=False,
        methods=("GET",),
        url_path="all",
        permission_classes=(HasAllScopesPermission,),
    )
    def all_statistics(self, request: Request):
        analytics, _ = UserMailBotAnalytics.objects.get_or_create(
            user_mailbot_profile=request.user.user_mailbot_profile
        )

        # Count the number of unique senders that the user has an unsubscribe link for.
        subscriptions_count = (
            SenderUnsubscribeDetail.objects.filter(
                Q(unsubscribe_mail_to__isnull=False) | Q(unsubscribe_link_one_click=True),
                user_mailbot_profile=request.user.user_mailbot_profile,
            )
            .values("sender_profile_id")
            .distinct()
            .count()
        )

        #  Retrieve the total number of messages in the user's Gmail inbox
        message_service = MessageServiceFactory.get_message_service(self.request.user.user_mailbot_profile)
        inbox_mails_count = message_service.get_label_stats(
            GmailKnownLabelName.INBOX.value
            if self.request.user.user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_GOOGLE
            else OutlookKnownLabelName.INBOX.value
        )["total_messages"]

        analytics_data = analytics.statistics
        analytics_data["subscriptions_count"] = subscriptions_count
        analytics_data["inbox_mails_count"] = inbox_mails_count
        return Response(data=analytics.statistics)

    @action(
        detail=False,
        methods=("GET",),
        url_path="latest-subscription",
    )
    def latest_subscription_statistics(self, request: Request):
        latest_subscription = get_active_subscription(user_mailbot_profile=request.user.user_mailbot_profile)
        if not latest_subscription:
            return Response(data={})
        mailbot_usage_log, _ = MailBotUsageLog.objects.get_or_create(
            user_mailbot_profile=request.user.user_mailbot_profile,
            subscription_period_start=latest_subscription.current_period_start,
        )
        return Response(data=mailbot_usage_log.metadata)


class PusherAuthAPIView(APIView):
    def post(self, request):
        """
        Authenticate a user's presence on a private channel.
        """
        channel = request.data.get("channel")
        socket_id = request.data.get("socket_id")
        channel = get_private_channel(channel, request.user.id)
        try:
            auth = channel.authenticate(
                user_id=request.user.id,
                socket_id=socket_id,
            )
        except ValueError:
            logger.exception("Invalid pusher details")
            return Response("Invalid pusher details", status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(auth)


class SupportAPIView(APIView):
    def post(self, request):
        """
        Send email to the support email address.
        """
        text_body = request.data.get("body")
        user_mailbot_profile = request.user.user_mailbot_profile
        service = MessageServiceFactory.get_message_service(user_mailbot_profile=user_mailbot_profile)
        email_backend = EmailBackend.from_service_provider(user_mailbot_profile.service_provider)
        template = EmailTemplate.objects.get(tag="support")
        template.send_email(
            user=request.user,
            to=constance_config.SES_SUPPORT_EMAIL,
            context={"body": text_body},
            backend=email_backend,
            auth_tokens=service.get_auth_tokens(),
        )
        return Response()


class SenderUnsubscribeDetailViewSet(GenericViewSet, ListModelMixin):
    serializer_class = SenderProfileSerializer
    pagination_class = CustomLimitOffsetPagination
    filter_backends = [filters.OrderingFilter, DjangoFilterBackend]
    filterset_class = SenderProfilesFilter
    ordering_fields = ["sender_email", "total_count", "recent_timestamp"]
    lookup_field = "id"

    def get_queryset(self):
        sender_unsubscribe_detail_subquery = SenderUnsubscribeDetail.objects.filter(
            Q(unsubscribe_mail_to__isnull=False) | Q(unsubscribe_link_one_click=True),
            user_mailbot_profile=self.request.user.user_mailbot_profile,
            sender_profile_id=OuterRef("id"),
        ).only("id")
        queryset = SenderProfile.objects.annotate(
            mailing_list_exists=Exists(sender_unsubscribe_detail_subquery),
        ).filter(
            mailing_list_exists=True,
            user_mailbot_profile=self.request.user.user_mailbot_profile,
        )
        return queryset

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        response.data["unsubscribed_count"] = SenderProfile.objects.filter(
            user_mailbot_profile=self.request.user.user_mailbot_profile,
            user_action_reason=SenderProfile.USER_ACTION_REASON_UNSUBSCRIBED,
        ).count()
        return response

    @action(detail=True, methods=("POST",), url_path="unsubscribe")
    def unsubscribe(self, request, id):
        """
        Unsubscribe from a sender's emails.
        """
        if not check_feature(
            user_id=request.user.id,
            feature_flag=MailBotFeatureFlag.UNSUBSCRIBE,
            application_tag=ApplicationTag.MailBot,
        ):
            return Response(status=status.HTTP_403_FORBIDDEN)
        if not can_unsubscribe_more_senders(user_mailbot_profile=request.user.user_mailbot_profile):
            return Response(status=status.HTTP_402_PAYMENT_REQUIRED)
        sender_profile: SenderProfile = self.get_object()
        unsubscribe_sender.delay(sender_profile.id)
        update_statistics_in_analytics(
            user_mailbot_profile=request.user.user_mailbot_profile,
            key=AnalyticsStatisticsKey.SENDERS_UNSUBSCRIBED.value,
            value=1,
        )
        update_mailbot_usage(
            user_mailbot_profile=request.user.user_mailbot_profile,
            key=SubscriptionStatisticsKey.SENDERS_UNSUBSCRIBED.value,
            value=1,
        )
        sender_profile.user_action = SenderProfile.USER_ACTION_MOVE_TO_TRASH
        sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_UNSUBSCRIBED
        sender_profile.save()
        return Response({"message": "Unsubscribed successfully"})


class MailBotPriceCatalogueAPIView(ListAPIView):
    serializer_class = MailBotPriceCatalogueSerializer
    lookup_field = "id"

    def get_queryset(self):
        queryset = StripePrice.objects.filter(active=True)
        try:
            secondary_mailbot_profile = SecondaryMailBotProfilesThrough.objects.get(
                secondary_mailbot_profile=self.request.user.user_mailbot_profile
            )
        except SecondaryMailBotProfilesThrough.DoesNotExist:
            primary_user = self.request.user
            mailbot_profile = primary_user.user_mailbot_profile
        else:
            primary_user = secondary_mailbot_profile.primary_mailbot_profile.user
            mailbot_profile = secondary_mailbot_profile.primary_mailbot_profile
        pricing_version = get_pricing_version(user_mailbot_profile=mailbot_profile)

        if pricing_version == "v1":
            queryset = queryset.filter(nickname__in=("mailbot_monthly", "mailbot_annual"))
        elif pricing_version == "v2":
            queryset = queryset.filter(nickname__in=("basic", "power"))
        elif pricing_version == "v3":
            queryset = queryset.filter(nickname__in=("freebie", "basic", "power"))
        elif pricing_version == "v4":
            queryset = queryset.filter(nickname__in=("basic", "power_v2"))
        elif pricing_version == "v5":
            queryset = queryset.filter(nickname__in=("basic", "power_v3"))
        elif pricing_version == "v6":
            queryset = queryset.filter(nickname__in=("basic_v4", "power_v4"))
        current_subscription = None
        try:
            current_subscription = StripeSubscription.objects.select_related("price").get(
                customer__user_id=primary_user.id,
                status__in=[StripeSubscription.STATUS_ACTIVE, StripeSubscription.STATUS_PAST_DUE],
            )
        except StripeSubscription.DoesNotExist:
            pass
        paid_invoice_exists = StripeInvoice.objects.filter(
            customer__user_id=primary_user.id, status=StripeInvoice.STATUS_PAID
        ).exists()
        # Only allow users to upgrade the subscription from price catalogue
        # If no active subscription, include free price only if first time user (no paid invoice exists)
        unit_amount_threshold = (
            current_subscription.price.unit_amount if current_subscription else (1 if paid_invoice_exists else 0)
        )
        queryset = (
            queryset.exclude(unit_amount__lt=unit_amount_threshold)
            .annotate(
                is_current_plan=Case(
                    When(id=current_subscription.price.id if current_subscription else None, then=True),
                    default=Value(False),
                    output_field=BooleanField(),
                )
            )
            .order_by("unit_amount")
        )
        return queryset

    def get_serializer_context(self):
        context = super().get_serializer_context()
        user_mailbot_profile = self.request.user.user_mailbot_profile
        context["pricing_version"] = get_pricing_version(user_mailbot_profile=user_mailbot_profile)
        try:
            context["stripe_customer"] = self.request.user.stripe_customer
        except StripeCustomer.DoesNotExist:
            context["stripe_customer"] = None
        return context


class ActionRequestHandlerAPIView(APIView):
    def __init__(self):
        self.action_processor = ActionProcessor()

    def post(self, request):
        try:
            serializer = ActionRequestSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            result = self.action_processor.process(
                data=serializer.validated_data,
                user=request.user,
            )
            return Response({"message": "Action executed successfully", "result": result}, status=status.HTTP_200_OK)
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Error executing action: {e}", exc_info=True)
            return Response(
                {"error": "An unexpected error occurred while processing your request."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
