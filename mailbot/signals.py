from django.dispatch import Signal
from functools import wraps
import importlib

# Mail status changes from unread to read
post_email_read = Signal()
# Mail bot changes from inactive to active
on_mailbot_activate = Signal()
# Mail bot changes from active to inactive
on_mailbot_deactivate = Signal()
# Mailbot profile is deleted
post_app_profile_delete = Signal(["user"])
# Mail label changed
post_label_change = Signal()

scan_completed = Signal()


def _resolve_receiver(receiver):
    """
    Resolves a receiver from a string path to the actual function.

    Args:
        receiver: Either a callable or a string path (e.g., 'mailbot.receivers.increment_processed_count')

    Returns:
        The resolved callable receiver function
    """
    if not isinstance(receiver, str):
        return receiver

    # Parse the string path into module and attribute
    module_path, attribute = receiver.rsplit(".", 1)
    module = importlib.import_module(module_path)
    return getattr(module, attribute)


def mute_signal(signal, receiver, sender=None):
    """
    Simple decorator that temporarily disables a Django signal during function execution.

    Usage:
        @mute_signal(post_save, 'app.module.receiver_function', sender=Model)
        def my_function():
            # Function code here
    """

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            resolved_receiver = _resolve_receiver(receiver)
            signal_disconnected = signal.disconnect(resolved_receiver, sender=sender)
            try:
                return func(*args, **kwargs)
            finally:
                if signal_disconnected:
                    signal.connect(resolved_receiver, sender=sender)

        return wrapper

    return decorator
