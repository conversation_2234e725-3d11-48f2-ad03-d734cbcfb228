import re
from collections import defaultdict
from typing import Dict, <PERSON>, Tuple, Optional

from nltk.stem import PorterStemmer


class FullTextSearch:
    """
    A simple in-memory full-text search class with stemming functionality.

    Attributes:
        index (dict): Inverted index mapping stemmed terms to document IDs.
        stemmer (PorterStemmer): NLTK Porter Stemmer for stemming words.

    Methods:
        tokenize_and_stem(text): Tokenizes and stems the input text.
        index_document(document_id, text): Indexes a document in the search engine.
        search(query): Performs a full-text search and returns matching document IDs.
    """

    def __init__(self):
        """
        Initializes the FullTextSearch instance with an empty inverted index and a Porter Stemmer.
        """
        self.index = defaultdict(list)
        self.stemmer = PorterStemmer()

    def tokenize_and_stem(self, text: str):
        """
        Tokenizes and stems the input text.

        Args:
            text (str): The input text to be tokenized and stemmed.

        Returns:
            list: List of stemmed tokens.
        """
        # The regular expression \b\w+\b matches word boundaries and extracts words.
        # This regex ensures that only whole words are considered, excluding punctuation and other characters.
        words = re.findall(r"\b\w+\b", text.lower())
        return [self.stemmer.stem(word) for word in words]

    def index_document(self, document_id: str, text: str):
        """
        Indexes a document in the search engine.

        Args:
            document_id (str): The unique identifier for the document.
            text (str): The text content of the document to be indexed.
        """
        tokens = self.tokenize_and_stem(text)
        for token in tokens:
            self.index[token].append(document_id)

    def search(self, query: str):
        """
        Performs a full-text search and returns matching document IDs.

        Args:
            query (str): The search query.

        Returns:
            list: List of document IDs that match the query.
        """
        query_tokens = self.tokenize_and_stem(query)
        result_docs = set(self.index[query_tokens[0]])

        for token in query_tokens[1:]:
            result_docs.intersection_update(self.index.get(token, []))

        return list(result_docs)


def search_phrases(document: Dict, field: str, phrases: List) -> bool:
    """
    Search if any of the phrases match against the value of given field in the document
    Args:
        document: document to search across
        field: name of the field to search
        phrases: list of phrases to search

    Returns:
        A boolean representing if any of the phrases matched the value of given field
    """
    assert "id" in document.keys(), "Document to be searched must define a unique identifier field 'id'"
    searcher = FullTextSearch()
    searcher.index_document(document["id"], document[field])
    result = False
    for phrase in phrases:
        if result := result or len(searcher.search(phrase)) != 0:
            break
    return result


def search_phrases_all(document_id: str, text: str, phrases: List) -> Tuple[bool, Optional[list]]:
    """
    Search if all the phrases match against the value of given field in the document.

    Args:
        document_id: document to search across
        text: document text to search across
        phrases: list of phrases to search

    Returns:
        [bool, string]: Whether all the phrases matched and matched phrases
    """
    matched_phrases = []
    if not phrases:
        return False, matched_phrases
    searcher = FullTextSearch()
    searcher.index_document(document_id=document_id, text=text)
    result = True
    for phrase in phrases:
        if phrase.isdigit():
            regex_pattern = f"\\b\\d{{{int(phrase)}}}\\b"
            result = bool(re.search(regex_pattern, text, re.IGNORECASE))
        else:
            result = len(searcher.search(phrase)) != 0
        if not result:
            break
        matched_phrases.append(phrase)
    return result, matched_phrases
