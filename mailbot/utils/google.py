import datetime
import logging
import random
import time
from typing import List, Any, Dict, Optional, Tu<PERSON>, Iterable

from django.conf import settings
from django.utils import timezone
from google.auth.exceptions import RefreshError
from google.oauth2.credentials import Credentials
from googleapiclient._auth import refresh_credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import HttpRequest, BatchHttpRequest
from rest_framework.status import HTTP_404_NOT_FOUND, HTTP_409_CONFLICT

from accounts.signals import refresh_token_expired
from applications.utils.base import batch_enumerate
from mailbot.utils.exceptions import (
    InvalidSelectFieldsError,
    LabelIdNotFoundError,
    LabelNameAlreadyExistError,
    MailBotHTTPError,
    MessageIdNotFoundError,
    ConflictingLabelIdError,
    ModifyMessageLabelsError,
)

logger = logging.getLogger(__name__)


class GMailBox:
    MAX_NUM_TRIES = 6
    MAX_BATCH_LIMIT = 50

    def __init__(self, user_mailbot_profile):
        client_id = settings.GOOGLE["client_id"]
        client_secret = settings.GOOGLE["client_secret"]
        if not client_id or not client_secret:
            raise ValueError("Google client_id or client_secret not found in settings")
        credential = Credentials(
            token=user_mailbot_profile.access_token,
            refresh_token=user_mailbot_profile.refresh_token,
            client_id=client_id,
            client_secret=client_secret,
            token_uri=settings.GOOGLE["token_uri"],
        )
        self.email = user_mailbot_profile.user.email
        self.user_mailbot_profile_id = user_mailbot_profile.id
        expires_at = user_mailbot_profile.granted_authentication.get("expires_at", timezone.now().timestamp())
        expiry = datetime.datetime.fromtimestamp(expires_at, tz=datetime.timezone.utc)
        if timezone.now() >= expiry - timezone.timedelta(minutes=5):
            try:
                refresh_credentials(credential)
            except RefreshError as refresh_error:
                logger.info(f"Refreshing credentials for gmail user {self.email} failed due to {refresh_error}")
                refresh_token_expired.send(sender=self.__class__, user_mailbot_profile_id=self.user_mailbot_profile_id)
                raise refresh_error
            else:
                if credential.refresh_token:
                    expires_at = credential.expiry.timestamp()
                    user_mailbot_profile.granted_authentication["expires_at"] = expires_at
                    user_mailbot_profile.access_token = credential.token
                    user_mailbot_profile.refresh_token = credential.refresh_token
                    user_mailbot_profile.save(
                        update_fields=["granted_authentication", "encrypted_access_token", "encrypted_refresh_token"]
                    )
                else:
                    logger.exception(
                        "Refresh token missing after refreshing Google credentials", extra={"email": self.email}
                    )
        self.credential = credential
        self.service = build(serviceName="gmail", version="v1", credentials=credential, cache_discovery=False)

    def http_execute_safe(self, request: HttpRequest) -> Tuple[bool, Dict[str, Any]]:
        """
        Execute HTTP request and return response if successful else return False and empty dict.

        Args:
            request (HttpRequest): HTTP request to be executed

        Raises:
            refresh_error: If refresh token is expired

        Returns:
            Tuple[bool, Dict[str, Any]]: Tuple of execution status and response
        """
        try:
            response = request.execute(num_retries=self.MAX_NUM_TRIES)
        except RefreshError as refresh_error:
            refresh_token_expired.send(sender=self.__class__, user_mailbot_profile_id=self.user_mailbot_profile_id)
            raise refresh_error
        except HttpError as http_error:
            # Network request connected with Gmail API, got error in response
            return False, {"reason": http_error.reason, "status_code": http_error.status_code, "uri": http_error.uri}
        except Exception as exc:
            # Network request fails
            logger.exception(
                "Error while making HTTP connection with Gmail API",
                extra={"user": self.email, "exception_details": exc},
            )
            return False, {}
        else:
            return True, response

    def get_labels(self) -> list:
        """
        Get all labels for user.

        Returns:
            list: List of dict containing label id and name

        Raises:
            MailBotHTTPError: If any error occurs
        """
        request: HttpRequest = self.service.users().labels().list(userId=self.email, fields="labels.id,labels.name")
        executed, response = self.http_execute_safe(request)
        if executed:
            return response["labels"]
        else:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def create_label(self, label_name: str) -> Dict[str, Any]:
        """
        Create a new label for user.

        Args:
            label_name (str): Name of label to be created

        Returns:
            Dict[str, Any]: Response containing label data

        Raises:
            MailBotHTTPError: If any error occurs
        """
        body = {"labelListVisibility": "labelShow", "messageListVisibility": "show", "name": label_name}
        request: HttpRequest = self.service.users().labels().create(userId=self.email, body=body)
        executed, response = self.http_execute_safe(request)
        if executed:
            return response
        else:
            status_code = response.get("status_code")
            reason = response.get("reason")
            if status_code == HTTP_409_CONFLICT:
                raise LabelNameAlreadyExistError(label_name=label_name, reason=reason, status_code=status_code)
            else:
                raise MailBotHTTPError(reason=reason, status_code=status_code)

    def delete_label(self, label_id: str):
        """
        Immediately and permanently deletes the specified label and removes it from any messages and threads that it is applied to.

        Args:
            label_id (str): The ID of the label to delete.
        """
        request: HttpRequest = self.service.users().labels().delete(userId=self.email, id=label_id)
        executed, response = self.http_execute_safe(request)
        if not executed:
            reason = response.get("reason", "")
            status_code = response.get("status_code", "")
            if status_code == HTTP_404_NOT_FOUND:
                logger.info(f"Label id {label_id} for user {self.email} not found")
            else:
                raise MailBotHTTPError(reason=reason, status_code=status_code)

    def get_label_stats(self, label_id: str) -> Dict[str, Any]:
        """
        Get label stats for user. This includes total number of messages and unread count for label.

        Args:
            label_id (str): Label id

        Returns:
            dict: Response containing label stats

        Raises:
            MailBotHTTPError: If any error occurs
        """
        request: HttpRequest = self.service.users().labels().get(userId=self.email, id=label_id)
        executed, response = self.http_execute_safe(request)
        if executed:
            return response
        else:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def patch_label(self, label_id: str, new_label_name: str) -> Dict[str, Any]:
        """
        Update label name for user.

        Args:
            label_id (str): Label id
            new_label_name (str): New label name

        Returns:
            dict: Response containing updated label data

        Raises:
            MailBotHTTPError: If any error occurs
        """
        request: HttpRequest = (
            self.service.users().labels().patch(userId=self.email, id=label_id, body={"name": new_label_name})
        )
        executed, response = self.http_execute_safe(request)
        if executed:
            return response
        else:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def get_message_ids(self, label_ids: List[str], max_results: int, page_token: str = None, **kwargs):
        """
        Get message ids for user.

        Args:
            label_ids (List[str]): List of label ids
            max_results (int): Maximum number of messages to be returned
            page_token (str, optional): Page token to retrieve a specific page of results in the list. Defaults to None.

        Returns:
            dict: Response containing message ids and next page token
        """
        request: HttpRequest = (
            self.service.users()
            .messages()
            .list(
                userId=self.email,
                maxResults=max_results,
                pageToken=page_token,
                labelIds=label_ids,
                fields="messages.id,nextPageToken",
                **kwargs,
            )
        )
        executed, response = self.http_execute_safe(request)
        if executed:
            return response
        else:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def list_draft_messages(self, page_token: str = None):
        """
        List draft messages for a user
        Args:
            page_token (str, optional): Page token to retrieve a specific page of results in the list. Defaults to None
        Returns:
            dict: Response containing draft ids and next page token
        """
        request: HttpRequest = (
            self.service.users()
            .drafts()
            .list(
                userId=self.email,
                pageToken=page_token,
            )
        )
        executed, response = self.http_execute_safe(request)
        if executed:
            return response
        else:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def get_messages(self, message_ids: Iterable[str], fields_to_select: Iterable[str]):
        """
        Get messages for user.

        Args:
            message_ids (Iterable[str]): List of message ids
            fields_to_select (Iterable[str]): List of fields to be selected

        5000 messages take 120 seconds
        41.66 messages per seconds
        0.024 seconds per message

        Returns:
            list: List of messages
        """
        retry_message_ids = []
        current_message_ids = []
        current_message_ids.extend(message_ids)
        responses = []

        def _batch_get_callback(request_id: str, message: Dict[str, Any], http_error: Optional[HttpError]):
            """
            Callback inner function for batch HTTP request.
            Add the message in list if no HTTP error, else add the request_id to retry list
            Args:
                request_id: Unique id for this connection that was passed while adding the request
                message: Message response for the request corresponding to request_id
                http_error: HTTP error if any
            """
            if http_error:
                # Retry only if server error (5xx) or due to rateLimitExceeded (403), Too many requests (429)
                if http_error.status_code >= 500 or http_error.status_code in [403, 429]:
                    retry_message_ids.append(request_id)
                elif http_error.status_code == 404:
                    logger.info(f"message_id {request_id} for user {self.email} not found")
                else:
                    # preconditionFailed
                    if http_error.status_code == 400 and http_error.reason == "preconditionFailed":
                        logger.info(f"Precondition failed for message_id {request_id} for user {self.email}")
                    else:
                        logger.exception(
                            "HTTP error while fetching single message",
                            extra={
                                "email": self.email,
                                "message_id": request_id,
                                "error_details": http_error.error_details,
                            },
                        )
            else:
                responses.append(message)

        for retry_num in range(self.MAX_NUM_TRIES + 1):
            if retry_num > 0:
                # Sleep before retrying.
                sleep_time = random.random() * (2**retry_num)
                logger.info(f"Sleeping {sleep_time} seconds before retry {retry_num} of {self.MAX_NUM_TRIES}")
                time.sleep(sleep_time)
            for batch_idx, batch_range in batch_enumerate(len(current_message_ids), self.MAX_BATCH_LIMIT):
                batch_request: BatchHttpRequest = self.service.new_batch_http_request(callback=_batch_get_callback)
                for message_id in current_message_ids[batch_range]:
                    try:
                        batch_request.add(
                            request=self.service.users()
                            .messages()
                            .get(userId=self.email, id=message_id, fields=",".join(fields_to_select)),
                            request_id=message_id,
                        )
                    except KeyError:
                        if current_message_ids[batch_range].count(message_id) != 1:
                            logger.info(
                                f"message_id {message_id} for user {self.email} is already included for batch request"
                            )
                        else:
                            logger.exception(
                                "request_id is not unique", extra={"email": self.email, "message_id": message_id}
                            )
                try:
                    batch_request.execute()
                except HttpError as http_error:
                    retry_message_ids.extend(current_message_ids[batch_range])
                    logger.info(
                        f"Batch HTTP error while fetching messages for email {self.email} due to {http_error.error_details}"
                    )
            if not retry_message_ids:
                break
            if retry_num != self.MAX_NUM_TRIES:
                current_message_ids = retry_message_ids[:]
                retry_message_ids.clear()

        if retry_message_ids:
            logger.info(f"Unsuccessful to fetch {len(retry_message_ids)}/{len(message_ids)} messages")
        return responses

    def get_message(self, message_id, fields_to_select):
        """
        Get single message for user.

        Args:
            message_id (str): Message id
            fields_to_select (Iterable[str]): List of fields to be selected

        Returns:
            dict: Message response

        Raises:
            MessageIdNotFoundError: If message id is not found
            InvalidSelectFieldsError: If any of the select fields is invalid
            MailBotHTTPError: If any other error occurs
        """
        request: HttpRequest = (
            self.service.users().messages().get(userId=self.email, id=message_id, fields=",".join(fields_to_select))
        )
        executed, response = self.http_execute_safe(request)
        if not executed:
            reason = response.get("reason", "")
            status_code = response.get("status_code", "")
            if status_code == HTTP_404_NOT_FOUND or reason.startswith("Invalid id value"):
                raise MessageIdNotFoundError(message_id=message_id, reason=reason, status_code=status_code)
            elif reason.startswith("Invalid field"):
                raise InvalidSelectFieldsError(select_fields=fields_to_select, reason=reason, status_code=status_code)
            else:
                raise MailBotHTTPError(reason=reason, status_code=status_code)
        return response

    def rename_label(self, label_id, new_name):
        """
        Rename label for user with new name.

        Args:
            label_id (str): Label id
            new_name (str): New name for label

        Raises:
            MailBotHTTPError: If any error occurs
        """
        request: HttpRequest = (
            self.service.users().labels().update(userId=self.email, id=label_id, body={"name": new_name})
        )
        executed, response = self.http_execute_safe(request)
        if not executed:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def batch_update_labels(
        self, message_ids: List[str], add_label_ids: Optional[Iterable[str]], remove_label_ids: Optional[Iterable[str]]
    ) -> List[str]:
        """
        Modifies the labels on the specified messages.
        Quota units consumed: unspecified
        References:
            https://developers.google.com/gmail/api/reference/rest/v1/users.messages/batchModify
            https://coda.io/d/_dcEguq84KnW/Google-API-HTTPRequest_suRHl
        Args:
            message_ids(List[str]): The IDs of the messages to modify. There is a limit of 1000 ids per request.
            add_label_ids: A list of label IDs to add to messages.
            remove_label_ids: A list of label IDs to remove from messages.
        Returns:
            List[str]: Successfully moved messages.
        """
        successfully_moved = []
        for _, batch_range in batch_enumerate(len(message_ids), 1000):
            body = {"ids": message_ids[batch_range]}
            if add_label_ids:
                body["addLabelIds"] = list(add_label_ids)
            if remove_label_ids:
                body["removeLabelIds"] = list(remove_label_ids)
            request: HttpRequest = self.service.users().messages().batchModify(userId=self.email, body=body)
            executed, response = self.http_execute_safe(request)
            if executed:
                successfully_moved.extend(message_ids[batch_range])
            else:
                logger.exception(
                    "Error while modifying labels of batch messages",
                    extra={
                        "user": self.email,
                        "message_ids": message_ids[batch_range],
                        "add_label_ids": add_label_ids,
                        "remove_label_ids": remove_label_ids,
                        "reason": response.get("reason", ""),
                        "status_code": response.get("status_code", ""),
                    },
                )
        return successfully_moved

    def update_labels(
        self, message_id: str, add_label_ids: Optional[Iterable[str]], remove_label_ids: Optional[Iterable[str]]
    ):
        """
        Update labels for message.

        Args:
            message_id (str): Message id for which labels are to be updated
            add_label_ids (Optional[Iterable[str]]): Add label ids to message
            remove_label_ids (Optional[Iterable[str]]): Remove label ids from message

        Raises:
            MessageIdNotFoundError: If message id is not found
            ConflictingLabelIdError: If any label in add and remove label ids are same
            LabelIdNotFoundError: If label id is not found
            ModifyMessageLabelsError: If any other error occurs
        """
        body = {}
        if not add_label_ids and not remove_label_ids:
            # If both add and remove label_ids are empty then return True
            logger.exception(
                "Both add and remove label_ids are empty while modifying labels of message",
                extra={"user": self.email, "message_id": message_id},
            )
            return True
        if add_label_ids:
            body["addLabelIds"] = list(add_label_ids)
        if remove_label_ids:
            body["removeLabelIds"] = list(remove_label_ids)
        request: HttpRequest = self.service.users().messages().modify(userId=self.email, id=message_id, body=body)
        executed, response = self.http_execute_safe(request)
        if not executed:
            reason = response.get("reason", "")
            status_code = response.get("status_code", "")
            if reason.startswith("Requested entity was not found") or reason.startswith("Invalid id value"):
                raise MessageIdNotFoundError(message_id=message_id, reason=reason, status_code=status_code)
            elif reason.startswith("Cannot both add and remove the same label"):
                raise ConflictingLabelIdError(
                    message_id=message_id,
                    added_label_ids=add_label_ids,
                    removed_label_ids=remove_label_ids,
                    reason=reason,
                    status_code=status_code,
                )
            elif reason.startswith("labelId not found") or reason.startswith("Invalid label"):
                raise LabelIdNotFoundError(
                    message_id=message_id,
                    added_label_ids=add_label_ids,
                    removed_label_ids=remove_label_ids,
                    reason=reason,
                    status_code=status_code,
                )
            else:
                raise ModifyMessageLabelsError(
                    message_id=message_id,
                    added_label_ids=add_label_ids,
                    removed_label_ids=remove_label_ids,
                    reason=reason,
                    status_code=status_code,
                )

    def watch_channel(self, label_ids):
        """
        Watch channel for user.

        Args:
            label_ids (list): List of label ids to be watched

        Returns:
            dict: Response containing expiration time and history id

        Raises:
            MailBotHTTPError: If any error occurs
        """
        body = {
            "labelIds": label_ids,
            "topicName": settings.GOOGLE_PUSH_SERVER_TOPIC_NAME,
            "labelFilterBehavior": "INCLUDE",
        }
        request: HttpRequest = self.service.users().watch(userId=self.email, body=body)
        executed, response = self.http_execute_safe(request)
        if executed:
            return response
        else:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def unwatch_channel(self):
        """
        Unwatch channel for user.

        Raises:
            MailBotHTTPError: If any error occurs
        """
        request: HttpRequest = self.service.users().stop(userId=self.email)
        executed, response = self.http_execute_safe(request)
        if not executed:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def history_list(self, start_history_id, page_token=None):
        """
        List history for user starting from start_history_id.

        Args:
            start_history_id (str): History id from which history is to be fetched
            page_token (str, optional): Page token to retrieve a specific page of results in the list. Defaults to None.

        Returns:
            dict: Response containing history id and next page token

        Raises:
            MailBotHTTPError: If any error occurs
        """
        request: HttpRequest = (
            self.service.users()
            .history()
            .list(
                userId=self.email,
                startHistoryId=start_history_id,
                pageToken=page_token,
                historyTypes=["messageAdded", "labelAdded", "labelRemoved"],
            )
        )
        executed, response = self.http_execute_safe(request)
        if executed:
            return response
        else:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    @property
    def latest_history_id(self):
        """
        Get latest history ID of the user's mailbox using users.getProfile Gmail API endpoint.
        It uses the least amount of quota units, so it's recommended to use this function
        instead of calling listHistory if the sole purpose is to check the latest history ID.
        Returns:
            int: History ID of current mailbox
        """
        request: HttpRequest = self.service.users().getProfile(userId=self.email, fields="historyId")
        executed, response = self.http_execute_safe(request)
        if executed:
            return int(response["historyId"])
        else:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def get_auth_tokens(self):
        """
        Gets authentication tokens from credentials object
        """
        return {"access_token": self.credential.token, "refresh_token": self.credential.refresh_token}

    def get_message_ids_from_thread(self, thread_id):
        request = self.service.users().threads().get(userId=self.email, id=thread_id)
        executed, response = self.http_execute_safe(request)
        if executed:
            return [message["id"] for message in response["messages"]]
        else:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def get_attachment(self, message_id: str, attachment_id: str):
        request = (
            self.service.users().messages().attachments().get(userId=self.email, messageId=message_id, id=attachment_id)
        )
        executed, response = self.http_execute_safe(request)
        if executed:
            return response["data"]
        else:
            raise MailBotHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))
