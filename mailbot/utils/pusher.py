from enum import Enum
from typing import Any, Dict, Optional

from constance import config as constance_config

from execfn.common.pusher import BasePrivateChannel


class SenderProfileEvents(Enum):
    DATA = "data"


class OnboardingEvents(Enum):
    COMPLETE = "onboarding-complete"


class SenderProfileChannel(BasePrivateChannel):
    name = "sender-profiles"
    allowed_events = (SenderProfileEvents.DATA, OnboardingEvents.COMPLETE)

    def should_trigger_event(self, event_name, data: Optional[Dict[str, Any]] = None, **kwargs) -> bool:
        if event_name == SenderProfileEvents.DATA:
            messages_scanned = kwargs.get("messages_scanned", 0)
            if messages_scanned > 0 and messages_scanned % constance_config.PUSHER_UPDATE_STATISTICS_THRESHOLD == 0:
                return True
            else:
                return False
        return True


def get_private_channel(channel_name, user_id) -> BasePrivateChannel:
    if channel_name == SenderProfileChannel().get_channel_name(user_id=user_id):
        return SenderProfileChannel()
    else:
        raise ValueError(f"Channel name: {channel_name} is not supported")
