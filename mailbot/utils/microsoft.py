import logging

from O365.utils.token import BaseTokenBackend
from django.contrib.auth import get_user_model

User = get_user_model()
logger = logging.getLogger(__name__)


class DatabaseTokenBackend(BaseTokenBackend):
    """
    Database based backend for storing, refreshing etc. by O365 library.
    """

    def __init__(self, user_mailbot_profile):
        super().__init__()
        self.user_mailbot_profile = user_mailbot_profile

    def load_token(self):
        token = None
        try:
            authentication: dict = self.user_mailbot_profile.granted_authentication.copy()
            authentication.update(
                {
                    "access_token": self.user_mailbot_profile.access_token,
                    "refresh_token": self.user_mailbot_profile.refresh_token,
                }
            )
            token = self.token_constructor(authentication)
        except Exception as exc:
            logger.exception("Token could not be retrieved from the backend", extra={"exception_details": exc})

        return token

    def save_token(self):
        """
        Saves the token dict in the store
        :return bool: Success / Failure
        """
        if self.token is None:
            raise ValueError('You have to set the "token" first.')
        if access_token := self.token.pop("access_token", None):
            self.user_mailbot_profile.access_token = access_token
        if refresh_token := self.token.pop("refresh_token", None):
            self.user_mailbot_profile.refresh_token = refresh_token
        self.user_mailbot_profile.granted_authentication = self.token
        self.user_mailbot_profile.save(
            update_fields=["encrypted_access_token", "encrypted_refresh_token", "granted_authentication"]
        )
