import logging

import stripe
from django.utils import timezone

from mailbot.models import MailBotLoginLink, UserMailBotProfile, SecondaryMailBotProfilesThrough
from payments.models import StripePrice, StripeSubscription, StripeCustomer

logger = logging.getLogger(__name__)


def create_free_user(user_email, price_nickname="free_forever", cancel_at: int = None):
    price = StripePrice.objects.get(nickname=price_nickname)
    customer = stripe.Customer.create(email=user_email)
    kwargs = {
        "customer": customer.id,
        "items": [
            {
                "price": price.id,
            }
        ],
    }
    if cancel_at:
        kwargs["cancel_at"] = cancel_at
    subscription = stripe.Subscription.create(**kwargs)
    return subscription


def check_login_code(code, user):
    """
    Check signup code validity and create subscription for user.

    Args:
        code: Code used while signing up
        user: User instance who used the code

    Returns:
        bool: Whether signup code is valid or not
    """
    try:
        mailbot_login_link = MailBotLoginLink.objects.get(code=code)
    except MailBotLoginLink.DoesNotExist:
        logger.info(f"Mailbot login link does not exist for code {code}")
        return False
    else:
        if (
            mailbot_login_link.max_redemptions
            and mailbot_login_link.times_redeemed + 1 > mailbot_login_link.max_redemptions
        ):
            logger.info(f"Mailbot login link with code {code} usage maximum limit reached")
            return False
        if mailbot_login_link.user_email and mailbot_login_link.user_email != user.email:
            logger.info(f"Mailbot login link with code {code} is not intended for user {user.email}")
            return False
        if mailbot_login_link.expires_at and timezone.now() > mailbot_login_link.expires_at:
            logger.info(f"Mailbot login link with code {code} is expired")
            return False
        if StripeSubscription.objects.filter(
            status__in=[StripeSubscription.STATUS_ACTIVE, StripeSubscription.STATUS_PAST_DUE],
            cancel_at_period_end=False,
            customer__user=user,
        ).exists():
            logger.info(f"User {user.email} already have an active subscription")
            return False
        if "redeemed_by" not in mailbot_login_link.metadata:
            mailbot_login_link.metadata["redeemed_by"] = []
        elif user.email in mailbot_login_link.metadata["redeemed_by"]:
            logger.info(f"Mailbot login link with code {code} is already redeemed by user {user.email}")
            return False

        mailbot_login_link.times_redeemed += 1
        mailbot_login_link.metadata["redeemed_by"].append(user.email)
        mailbot_login_link.save()

        cancel_at = (
            int(mailbot_login_link.subscription_expires_at.timestamp())
            if mailbot_login_link.subscription_expires_at
            else None
        )
        create_free_user(
            user_email=user.email,
            cancel_at=cancel_at,
        )
        return True


def get_active_subscription(user_mailbot_profile: UserMailBotProfile, check_cancel_at_period_end=True):
    try:
        secondary_mailbot_profile = SecondaryMailBotProfilesThrough.objects.select_related(
            "primary_mailbot_profile__user"
        ).get(secondary_mailbot_profile=user_mailbot_profile)
    except SecondaryMailBotProfilesThrough.DoesNotExist:
        primary_user = user_mailbot_profile.user
    else:
        primary_user = secondary_mailbot_profile.primary_mailbot_profile.user
    try:
        filter_kwargs = {
            "customer_id": primary_user.stripe_customer.id,
            "status__in": [StripeSubscription.STATUS_ACTIVE, StripeSubscription.STATUS_PAST_DUE],
        }
        if check_cancel_at_period_end:
            filter_kwargs["cancel_at_period_end"] = False
        return StripeSubscription.objects.get(**filter_kwargs)
    except (StripeCustomer.DoesNotExist, StripeSubscription.DoesNotExist):
        return None
