import datetime
import json
import logging
from abc import ABC, abstractmethod

from django.conf import settings
from django.utils import timezone

from applications.models import EmailTemplate, ScheduledTask, Application
from execfn.common.applications import ApplicationTag
from mailbot.models import UserMailBotAnalytics
from mailbot.utils.defaults import (
    MailBotMessageCategory,
    MailBotMessageHeaders,
    MailBotProfileMetadataKey,
    MailBotScheduledTasks,
    AnalyticsStatisticsKey,
)
from payments.models import StripeSubscription

logger = logging.getLogger(__name__)


class BaseEmail(ABC):
    def __init__(self, user_mailbot_profile):
        self.user_mailbot_profile = user_mailbot_profile

    @abstractmethod
    def get_template_tag(self):
        """
        EmailTemplate database tag for the email to send
        """
        pass

    @abstractmethod
    def get_mailbot_header_category(self):
        """
        Mailbot category name for identifying internal email while processing through label engine
        """
        pass

    @abstractmethod
    def should_send_email(self):
        """
        Whether email should be sent when the time comes
        """
        pass

    def should_schedule_email(self):
        """
        Whether email should be scheduled.
        In most cases this is same as should_send_email, except, for example, when scheduling emails before trial.
        """
        return self.should_send_email()

    @abstractmethod
    def get_start_time(self):
        """
        Time at which email should be sent
        """
        pass

    def schedule_email(self):
        """
        Schedule email based on start time and template tag
        """
        if not self.should_schedule_email():
            return
        start_time = self.get_start_time()
        tag = self.get_template_tag()
        try:
            task = ScheduledTask.objects.get(
                user=self.user_mailbot_profile.user,
                periodic_task__task=MailBotScheduledTasks.SEND_MAIL.value,
                periodic_task__enabled=True,
                periodic_task__kwargs=json.dumps(
                    {
                        "user_mailbot_profile_id": self.user_mailbot_profile.id,
                        "template_tag": tag,
                    }
                ),
            )
        except ScheduledTask.DoesNotExist:
            # Create the task if not already exist
            ScheduledTask.create_one_off_task(
                user=self.user_mailbot_profile.user,
                start_time=start_time,
                task=MailBotScheduledTasks.SEND_MAIL.value,
                task_kwargs={
                    "user_mailbot_profile_id": self.user_mailbot_profile.id,
                    "template_tag": tag,
                },
            )
            logger.info(f"Scheduled {tag} email at {start_time} for user {self.user_mailbot_profile.user.email}")
        else:
            # If task already exists then reschedule the task
            task.reschedule_one_off_task(start_time=start_time)
            logger.info(f"Rescheduled {tag} email to {start_time} for user {self.user_mailbot_profile.user.email}")

    def get_email_context(self):
        """
        Context for populating the dynamic fields in email
        """
        return {"name": self.user_mailbot_profile.user.first_name, "frontend_base_url": settings.FRONTEND_BASE_URL}

    def send_email(self):
        """
        Send an email using template tag and context
        """
        if not self.should_send_email():
            return
        tag = self.get_template_tag()
        message_sent = EmailTemplate.send_email_using_template(
            user=self.user_mailbot_profile.user,
            context=self.get_email_context(),
            tag=tag,
            to=self.user_mailbot_profile.user.email,
            application=Application.objects.get(tag=ApplicationTag.MailBot.value),
            headers={MailBotMessageHeaders.MESSAGE_CATEGORY.value: self.get_mailbot_header_category()},
        )
        logger.info(f"{tag} email sent successfully for user {self.user_mailbot_profile.user.email}")


class InsufficientPermissionsEmail(BaseEmail):
    def get_template_tag(self):
        return "insufficient_permissions"

    def get_mailbot_header_category(self):
        return MailBotMessageCategory.INSUFFICIENT_PERMISSIONS.value

    def should_send_email(self):
        if self.user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.ALL_SCOPES_GRANTED.value) is False:
            return True
        return False

    def get_start_time(self):
        # Send email if user has not granted all scopes within one hour
        start_time = timezone.now() + timezone.timedelta(hours=1)
        return start_time


class CreditCardMissingEmail(BaseEmail):
    def get_template_tag(self):
        return "credit_card_missing"

    def get_mailbot_header_category(self):
        return MailBotMessageCategory.CREDIT_CARD_MISSING.value

    def should_send_email(self):
        if self.user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.LAST_CHECKOUT_SESSION_CREATED_AT.value):
            # Send email if subscription not created and checkout session is created
            return not StripeSubscription.objects.filter(customer__user=self.user_mailbot_profile.user).exists()
        return False

    def should_schedule_email(self):
        if self.user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.LAST_CHECKOUT_SESSION_CREATED_AT.value):
            return True
        return False

    def get_start_time(self):
        last_checkout_session_created_at = self.user_mailbot_profile.metadata.get(
            MailBotProfileMetadataKey.LAST_CHECKOUT_SESSION_CREATED_AT.value
        )
        last_checkout_session_created_at_datetime = datetime.datetime.fromisoformat(last_checkout_session_created_at)
        return last_checkout_session_created_at_datetime + timezone.timedelta(hours=1)


class TrialExpiryTMinus1Email(BaseEmail):
    def get_template_tag(self):
        return "trial_expiry_t_minus_1"

    def get_mailbot_header_category(self):
        return MailBotMessageCategory.TRIAL_EXPIRY.value

    def should_send_email(self):
        # On T-1, subscription should still be active (i.e., user has not canceled the trial)
        trial_active = StripeSubscription.objects.filter(
            customer__user=self.user_mailbot_profile.user,
            status=StripeSubscription.STATUS_ACTIVE,
            cancel_at_period_end=False,
            price__nickname="freebie",
        ).exists()
        return trial_active

    def get_email_context(self):
        analytics: UserMailBotAnalytics = UserMailBotAnalytics.objects.get(
            user_mailbot_profile=self.user_mailbot_profile
        )
        context = super().get_email_context()
        context.update(
            {
                "emails_scanned": f"{analytics.statistics.get(AnalyticsStatisticsKey.MESSAGES_SCANNED.value, 0):,}",
                "emails_zapped": f"{analytics.statistics.get(AnalyticsStatisticsKey.MESSAGES_ZAPPED.value, 0):,}",
                "emails_deleted": f"{analytics.statistics.get(AnalyticsStatisticsKey.TRASHED_COUNT.value, 0):,}",
                "senders_unsubscribed": f"{analytics.statistics.get(AnalyticsStatisticsKey.SENDERS_UNSUBSCRIBED.value, 0):,}",
            }
        )
        return context

    def get_start_time(self):
        trial_subscription: StripeSubscription = StripeSubscription.objects.get(
            customer__user=self.user_mailbot_profile.user,
            status=StripeSubscription.STATUS_ACTIVE,
            cancel_at_period_end=False,
            price__nickname="freebie",
        )
        return trial_subscription.current_period_end - timezone.timedelta(days=1)


class TrialExpiryT0Email(BaseEmail):
    def get_template_tag(self):
        return "trial_expiry_t_0"

    def get_start_time(self):
        trial_subscription: StripeSubscription = StripeSubscription.objects.get(
            customer__user=self.user_mailbot_profile.user,
            status=StripeSubscription.STATUS_ACTIVE,
            cancel_at_period_end=False,
            price__nickname="freebie",
        )
        return trial_subscription.current_period_end - timezone.timedelta(hours=12)

    def should_send_email(self):
        # On T0, subscription should still be active (i.e., user has not canceled the trial)
        trial_active = StripeSubscription.objects.filter(
            customer__user=self.user_mailbot_profile.user,
            status=StripeSubscription.STATUS_ACTIVE,
            cancel_at_period_end=False,
            price__nickname="freebie",
        ).exists()
        return trial_active

    def get_mailbot_header_category(self):
        return MailBotMessageCategory.TRIAL_EXPIRY.value


class TrialExpiredBaseEmail(BaseEmail):
    def should_send_email(self):
        # On T+x, subscription will be canceled
        trial_expired = StripeSubscription.objects.filter(
            customer__user=self.user_mailbot_profile.user,
            status=StripeSubscription.STATUS_CANCELED,
            cancel_at_period_end=False,  # cancel_at_period_end will be True if user has canceled the subscription
            price__nickname="freebie",
        ).exists()
        # Skip email if user has canceled the subscription or upgraded it
        if not trial_expired:
            return False
        # Skip email if user has any other active subscription
        active_subscription_exists = StripeSubscription.objects.filter(
            customer__user=self.user_mailbot_profile.user,
            status=StripeSubscription.STATUS_ACTIVE,
        ).exists()
        return not active_subscription_exists

    def should_schedule_email(self):
        # Free trial subscription exists
        return StripeSubscription.objects.filter(
            customer__user=self.user_mailbot_profile.user,
            cancel_at_period_end=False,
            price__nickname="freebie",
        ).exists()

    @abstractmethod
    def get_template_tag(self):
        pass

    @abstractmethod
    def get_days_after_expiry(self) -> int:
        pass

    def get_start_time(self):
        trial_subscription: StripeSubscription = StripeSubscription.objects.get(
            customer__user=self.user_mailbot_profile.user,
            cancel_at_period_end=False,  # cancel_at_period_end will be True if user has canceled the subscription
            price__nickname="freebie",
        )
        return trial_subscription.current_period_end + timezone.timedelta(days=self.get_days_after_expiry())

    def get_mailbot_header_category(self):
        return MailBotMessageCategory.TRIAL_EXPIRY.value


class TrialExpiryTPlus1Email(TrialExpiredBaseEmail):
    def get_template_tag(self):
        return "trial_expiry_t_plus_1"

    def get_days_after_expiry(self) -> int:
        return 1


class TrialExpiryTPlus5Email(TrialExpiredBaseEmail):
    def get_template_tag(self):
        return "trial_expiry_t_plus_5"

    def get_days_after_expiry(self) -> int:
        return 5


class TrialExpiryTPlus10Email(TrialExpiredBaseEmail):
    def get_template_tag(self):
        return "trial_expiry_t_plus_10"

    def get_days_after_expiry(self) -> int:
        return 10


def send_scheduled_email(user_mailbot_profile, template_tag):
    for cls in (
        InsufficientPermissionsEmail,
        CreditCardMissingEmail,
        TrialExpiryTMinus1Email,
        TrialExpiryT0Email,
        TrialExpiryTPlus1Email,
        TrialExpiryTPlus5Email,
        TrialExpiryTPlus10Email,
    ):
        cls_instance = cls(user_mailbot_profile=user_mailbot_profile)
        if cls_instance.get_template_tag() == template_tag:
            cls_instance.send_email()


def schedule_trial_emails(user_mailbot_profile):
    for cls in (
        TrialExpiryTMinus1Email,
        TrialExpiryT0Email,
        TrialExpiryTPlus1Email,
        TrialExpiryTPlus5Email,
        TrialExpiryTPlus10Email,
    ):
        cls_instance = cls(user_mailbot_profile=user_mailbot_profile)
        cls_instance.schedule_email()
