import time
import random
import logging
from typing import List, Optional, Tuple, Dict, Any

logger = logging.getLogger(__name__)

# Import BaseModel and Field from pydantic (V2)
from pydantic import BaseModel, Field

# Update import: Remove JsonOutputParser, add <PERSON>ydanti<PERSON><PERSON>utputParser
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.runnables import Runnable
from langchain_core.language_models import BaseChatModel

# Add import for ChatPromptTemplate
from langchain_core.prompts import ChatPromptTemplate


# --- Configuration ---

EMAIL_ANALYSIS_PROMPT_TEMPLATE = """Analyze this email's FROM, SUBJECT line and BODY to determine the appropriate categories and required actions. Use **all three** when choosing the two best categories.

Return a JSON object with ONLY the following:
1. "category_1" - First category that best describes this email
2. "category_2" - Second category that best describes this email
3. "required_action" - Only if one of the categories is "Urgent Action Required", "Important Work Task", "Critical Account/Security Alert", or "Personal Message", specify what action the user needs to take based on this email

EMAIL CATEGORIES:
Consider ALL THREE - FROM, SUBJECT AND BODY content when determining email categories. Be very strict and cautious in your assessment - reserve high-priority categories only for truly important communications.

STRICT CATEGORY GUIDELINES:

- 'Urgent Action Required': Must have a genuine time-critical action needed from the recipient specifically, with clear consequences for not acting quickly. Must be from a trusted, verified sender. Never use this for marketing "limited time offers," automated alerts, or mass emails with artificial urgency.

- 'Important Work Task': Only for direct, personalized assignments or requests requiring the recipient's specific expertise or action. Must be from a colleague, manager, or established work contact directly to the recipient (not CC'd or mass-distributed). The task must be clearly defined with specific deliverables or expectations.

- 'Critical Account/Security Alert': Reserve for legitimate security concerns from verified sources about the recipient's accounts or systems. Must have specific details that indicate authenticity. Never use for generic warnings, marketing disguised as security alerts, or phishing attempts.

- 'Time-Sensitive Meeting/Event': Only for scheduled meetings/events that require the recipient's participation, with clear date/time information, from a genuine organizer. Must specifically invite the recipient (not mass announcements about events).

- **Important Transactional (2 pts)** • Purchases, invoices, **job‑application or recruiting correspondence**, confirmed service/support tickets, contracts, banking activity, welcome emails to a clearly subscribed service by user, etc.

- 'Highly Relevant Project Update': Only for personalized, substantive updates on projects the recipient is actively involved in. Must contain specific, actionable information from verifiable project stakeholders. Never use for newsletter-style updates or mass announcements.

- 'Standard Work Communication': Only for genuine work-related exchanges between direct colleagues or business contacts that aren't urgent but are still personalized. Never use for mass internal communications, newsletters, or promotional content even if work-related.

- 'Personal Message': Only for genuinely personal, non-work communications directed specifically to the recipient from a person they know. Must have personal content or context. Never use for personalized marketing that simulates personal communication.

Always default to lower-priority categories (Newsletter/Digest, Marketing/Promotional, Sales Outreach, Non-Critical Subscription, Low Priority Update) when any of these conditions apply:
- The email is sent to multiple/mass recipients
- Contains unsubscribe links or "view in browser" options
- Uses templated formatting typical of marketing emails
- Contains promotional language, offers, or sales pitches
- Is clearly automated rather than personally written
- Has generic greetings rather than personal addressing
- Contains primarily FYI information with no specific action required
- Is from an unknown or commercial sender without established relationship

If the email is clearly important and you have already chosen one high‑priority label, **do not downgrade the second label to "Low Priority Update" just to fill the slot**. Pick the next most accurate category, even if it is also high priority.

Assign two category labels from the following list that best describe this email:
- Urgent Action Required
- Important Work Task
- Critical Account/Security Alert
- Time-Sensitive Meeting/Event
- Important Transactional
- Highly Relevant Project Update
- Standard Work Communication
- Personal Message
- Newsletter/Digest
- Marketing/Promotional
- Sales Outreach
- Spam/Suspicious
- Social Media Notification
- Non-Critical Subscription
- Low Priority Update

FROM: {sender_email}
Subject: {subject}

Email body:
{email_body}
{format_instructions}
"""

# --- Data Structures ---


class EmailAnalysisResult(BaseModel):  # Ensure this inherits from the pydantic.BaseModel imported above
    """Structured result of email analysis."""

    category_1: str = Field(description="First category that best describes this email")
    category_2: str = Field(description="Second category that best describes this email")
    required_action: Optional[str] = Field(None, description="Action required, if applicable based on category")


# --- Analyzer Class ---


class EmailAnalyzer:
    """
    Analyzes email content to determine categories and required actions using a specified LLM.
    """

    # Backoff parameters
    MAX_RETRIES = 5
    INITIAL_BACKOFF_DELAY = 5.0  # seconds
    MAX_BACKOFF_DELAY = 60.0  # seconds

    MAX_CATEGORY_RETRIES = 1

    def __init__(self, llm: BaseChatModel):
        """
        Initializes the EmailAnalyzer.

        Args:
            llm: An instance of a Langchain BaseChatModel (e.g., ChatOpenAI, ChatGoogleGenerativeAI, ChatAnthropic).
        """
        if not isinstance(llm, BaseChatModel):
            raise TypeError("llm must be an instance of BaseChatModel")

        self.llm = llm
        self.prompt_template = ChatPromptTemplate.from_template(EMAIL_ANALYSIS_PROMPT_TEMPLATE)
        # Switch to PydanticOutputParser
        self.output_parser = PydanticOutputParser(pydantic_object=EmailAnalysisResult)

        # Include parser instructions in the prompt
        prompt_with_format_instructions = self.prompt_template.partial(
            format_instructions=self.output_parser.get_format_instructions()
        )

        # Update the chain to include format instructions
        self.chain: Runnable = prompt_with_format_instructions | self.llm | self.output_parser

    # Remove the _validate_output method as PydanticOutputParser handles it

    def _is_valid_result(self, result: EmailAnalysisResult) -> bool:
        return result.category_1 and result.category_2

    def analyze_single(self, sender_email: str, subject: str, email_body: str) -> EmailAnalysisResult:
        """
        Analyzes a single email.

        Args:
            sender_email: The email address of the sender.
            subject: The subject line of the email.
            email_body: The body content of the email.

        Returns:
            An EmailAnalysisResult object.

        Raises:
            Exception: If the LLM call, parsing, or validation fails after all retries.
        """
        input_data = {
            "sender_email": sender_email or "",
            "subject": subject or "",
            "email_body": email_body or "",
        }
        # Total attempts = initial attempt + retries
        TOTAL_ATTEMPTS = 1 + self.MAX_CATEGORY_RETRIES
        for attempt in range(TOTAL_ATTEMPTS):
            try:
                # Simulate LLM chain response for testing purposes
                result = self.chain.invoke(input_data)

                if not self._is_valid_result(result):
                    logger.info(f"Attempt {attempt}: Incomplete analysis (empty categories). Retrying...")
                    continue

                return result

            except Exception as e:
                logger.error(f"Attempt {attempt}: Error during email analysis: {e}")
                raise

    def analyze_batch(
        self, emails: List[Tuple[str, str, str, str]], category_retry_attempts: int = 0
    ) -> Dict[str, EmailAnalysisResult]:
        """
        Analyzes a batch of emails and returns results mapped by message_id,
        with exponential backoff for retries.

        Args:
            emails: A list of tuples, where each tuple contains (message_id, sender_email, subject, email_body).
        Returns:
            A dictionary mapping message_id (str) to its corresponding EmailAnalysisResult object.

        Raises:
            Exception: If the batch LLM call, parsing, or validation fails after all retries.
        """
        if not emails or category_retry_attempts > self.MAX_CATEGORY_RETRIES:
            return {}
        # Separate message_ids from the input data for the chain
        message_ids = [email[0] for email in emails]
        batch_input = [
            {
                "sender_email": sender_email or "",
                "subject": subject or "",
                "email_body": body or "",
                # format_instructions is now handled by partial application in __init__
            }
            for _, sender_email, subject, body in emails  # Use _ to ignore message_id here
        ]

        attempts = 0
        last_exception = None
        while attempts < self.MAX_RETRIES:
            try:
                # Use the batch processing capability of the Runnable chain
                # The chain now directly returns a list of validated Pydantic objects or raises an error
                results: List[EmailAnalysisResult] = self.chain.batch(batch_input, config={"max_concurrency": 5})

                failed_emails = []
                results_map = {}
                for index, result in enumerate(results):
                    if not self._is_valid_result(result):
                        failed_emails.append(emails[index])
                    else:
                        results_map[message_ids[index]] = result

                if failed_emails and category_retry_attempts < self.MAX_CATEGORY_RETRIES:
                    results_map.update(self.analyze_batch(failed_emails, category_retry_attempts + 1))
                return results_map

            except Exception as e:  # Catch potential errors (e.g., rate limits, API errors)
                # Ideally, catch more specific exceptions if known (e.g., RateLimitError from the LLM library)
                last_exception = e
                attempts += 1
                if attempts >= self.MAX_RETRIES:
                    logger.error(
                        f"Batch email analysis failed after {self.MAX_RETRIES} attempts. Last error: {e}"
                    )  # Log final failure
                    raise last_exception  # Re-raise the last exception after exhausting retries

                # Calculate backoff time with jitter
                backoff_time = min(self.INITIAL_BACKOFF_DELAY * (2 ** (attempts - 1)), self.MAX_BACKOFF_DELAY)
                jitter = random.uniform(0, backoff_time * 0.1)  # Add up to 10% jitter
                sleep_time = backoff_time + jitter

                logger.error(
                    f"Attempt {attempts} failed. Retrying in {sleep_time:.2f} seconds... Error: {e}"
                )  # Log retry attempt
                time.sleep(sleep_time)

        # This part should theoretically not be reached if MAX_RETRIES > 0,
        # but added for completeness in case MAX_RETRIES is set to 0 or less.
        logger.error("Batch email analysis failed after exhausting retries.")
        if last_exception:
            raise last_exception
        else:
            # Should not happen if loop ran at least once, but handle defensively
            raise Exception("Batch email analysis failed without a specific exception after retries.")
