from typing import Any, Dict

from mailbot.algorithms.base import BaseAlgorithm
from mailbot.models import MailBotGeneric<PERSON>abel, UserMailBotProfile
from mailbot.utils.defaults import MailBotMessageLabelReason
from mailbot.utils.label_engine.actions.base import BaseStateAction
from mailbot.utils.message_parser import ParsedMessage


class DefaultAction(BaseStateAction):
    @staticmethod
    def execute(
        parsed_message: ParsedMessage, filter_result: Dict[str, Any], algorithm: BaseAlgorithm, **kwargs
    ) -> Dict[str, Any]:
        user_mailbot_profile = UserMailBotProfile.objects.get(id=parsed_message.user_mailbot_profile_id)
        message_categories = kwargs.get("message_categories", [])
        whitelist_label = algorithm.default_action_label(user_mailbot_profile, message_categories)
        return DefaultAction.base_action(whitelist_label)

    @staticmethod
    def base_action(whitelist_label: MailBotGenericLabel) -> Dict[str, Any]:
        return {
            "message_labelled_due_to": MailBotMessageLabelReason.NO_FILTER.value,
            "label_name": whitelist_label.value,
        }
