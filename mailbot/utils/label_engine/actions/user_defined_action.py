from typing import Any, Dict

from applications.utils.email import get_normalized_email
from mailbot.models import MailBotGenericLabel, Message, SenderProfile
from mailbot.utils.label_engine.actions.base import BaseStateAction
from mailbot.utils.message_parser import ParsedMessage


class UserDefinedAction(BaseStateAction):
    @staticmethod
    def execute(parsed_message: ParsedMessage, filter_result: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        sender_email = parsed_message.from_name_email[1]
        normalized_sender_email = get_normalized_email(sender_email)
        sender_profile = SenderProfile.objects.get(
            normalized_sender_email=normalized_sender_email,
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
        )
        label_name = MailBotGenericLabel.WHITE_LIST.value
        mark_as_starred = False
        mark_as_read = False
        if sender_profile.user_action == SenderProfile.USER_ACTION_ARCHIVE:
            label_name = Message.GENERIC_LABEL_ARCHIVE
        elif sender_profile.user_action == SenderProfile.USER_ACTION_MARK_AS_SPAM:
            label_name = Message.GENERIC_LABEL_SPAM
        elif (
            sender_profile.user_action == SenderProfile.USER_ACTION_MOVE_TO_TRASH
            or sender_profile.user_action == SenderProfile.USER_ACTION_AUTO_DELETE
        ):
            label_name = Message.GENERIC_LABEL_TRASH
        elif sender_profile.user_action == SenderProfile.USER_ACTION_MOVE_TO_INBOX:
            label_name = MailBotGenericLabel.WHITE_LIST.value
        elif sender_profile.user_action == SenderProfile.USER_ACTION_MOVE_TO_ZAPPED:
            label_name = MailBotGenericLabel.ZAPPED.value
        elif sender_profile.user_action == SenderProfile.USER_ACTION_MARK_AS_READ:
            mark_as_read = True
        elif sender_profile.user_action == SenderProfile.USER_ACTION_MARK_AS_STARRED:
            mark_as_starred = True
        return {
            "label_name": label_name,
            "message_labelled_due_to": sender_profile.user_action_reason,
            "mark_as_starred": mark_as_starred,
            "mark_as_read": mark_as_read,
        }
