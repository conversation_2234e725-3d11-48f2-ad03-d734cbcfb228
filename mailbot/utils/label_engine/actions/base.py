from abc import ABC, abstractmethod
from typing import Any, Dict, Type, Union

from mailbot.algorithms.base import BaseAlgorithm


class BaseStateAction(ABC):
    """
    Base class for state action.

    Notes:
        Action should only do one thing. Consider adding multiple actions if needed.
        Avoid using check conditions in actions and only perform them using filters.
    """

    @staticmethod
    @abstractmethod
    def execute(state, filter_result: Dict[str, Any], algorithm: BaseAlgorithm, **kwargs) -> Dict[str, Any]:
        """
        Executes an action based on state, possibly modifying it.
        Args:
            state: State currently processing
            filter_result: Extra details from filters that maybe required by actions
            algorithm: The algorithm instance.
            kwargs: Additional parameters such as message_categories

        Returns:
            dict: Result of actions that must include `label_name` and `message_labelled_due_to` with other action specific data.
        """
        pass

    def __str__(self):
        return self.__class__.__name__


BaseActionType = Union[Type[BaseStateAction], BaseStateAction]
