from typing import Dict, Any, List
import logging
from applications.utils.email import get_normalized_email
from mailbot.algorithms.base import BaseAlgorithm
from mailbot.models import MailBotGenericLabel, Message, SenderProfile, MessageCategory, UserMailBotProfile
from mailbot.utils.defaults import MailBotMessageLabelReason
from mailbot.utils.label_engine.actions.base import BaseStateAction
from mailbot.utils.message_parser import ParsedMessage
from django.db.models import Count, Q
from django.contrib.postgres.aggregates import ArrayAgg

logger = logging.getLogger(__name__)


class ReadFractionAction(BaseStateAction):
    """
    Assign a label to message based on read fraction for current sender.
    """

    @staticmethod
    def execute(
        parsed_message: ParsedMessage, filter_result: Dict[str, Any], algorithm: BaseAlgorithm, **kwargs
    ) -> Dict[str, Any]:
        """
        Executes the default action based on the sender, user profile, and email categories.

        New Algo Logic:
        1. Fetch open rate for given email categories.
        2. Determine if both categories are marked important.
        3. Classify email based on open rate and category importance.
        4. Return the appropriate label.
        """
        message_categories = kwargs.get("message_categories", [])
        sender_email = parsed_message.from_name_email[1]
        normalized_sender_email = get_normalized_email(sender_email)
        sender_profile = SenderProfile.objects.get(
            normalized_sender_email=normalized_sender_email,
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
        )
        return ReadFractionAction.base_action(
            parsed_message=parsed_message,
            sender_profile=sender_profile,
            message_categories=message_categories,
            algorithm=algorithm,
        )

    @staticmethod
    def base_action(
        sender_profile: SenderProfile,
        parsed_message: ParsedMessage = None,
        message_categories: List[MessageCategory] = None,
        algorithm: BaseAlgorithm = None,
    ) -> Dict[str, Any]:
        action_result = {"message_labelled_due_to": MailBotMessageLabelReason.READ_FRACTION_FILTER.value}
        should_whitelist, include_in_digest = algorithm.should_whitelist_read_fraction_action(
            sender_profile, parsed_message.user_mailbot_profile_id, message_categories
        )
        action_result["label_name"] = (
            MailBotGenericLabel.WHITE_LIST.value if should_whitelist else MailBotGenericLabel.ZAPPED.value
        )
        action_result["include_in_digest"] = include_in_digest
        if algorithm and algorithm.should_show_critical_alert_overlay(message_categories):
            action_result["show_lucene_overlay"] = True
        return action_result
