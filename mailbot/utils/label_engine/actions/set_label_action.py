from typing import Any, Dict, Optional

from mailbot.utils.defaults import MailBotMessageLabelReason
from mailbot.utils.label_engine.actions.base import BaseStateAction
from mailbot.utils.message_parser import ParsedMessage


class SetLabelAction(BaseStateAction):
    """
    Set label and reason on message.
    """

    def __init__(self, message_labelled_due_to: MailBotMessageLabelReason, label_name: Optional[str] = None):
        self.label_name = label_name
        self.message_labelled_due_to = message_labelled_due_to

    def execute(self, parsed_message: ParsedMessage, filter_result: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        action_result = {}
        filter_result_label = filter_result.pop("label_name", "")
        if filter_result_label:
            action_result["label_name"] = filter_result_label
        elif self.label_name:
            action_result["label_name"] = self.label_name
        else:
            raise ValueError("Label name is required for SetLabelAction")
        if self.message_labelled_due_to:
            action_result["message_labelled_due_to"] = self.message_labelled_due_to.value
        # Set data computed in satisfied filter result to action result
        action_result = {**action_result, **filter_result}
        return action_result
