import logging
from typing import Any, Dict, List

from mailbot.algorithms.base import BaseAlgorithm
from mailbot.models import Message, MailBotGenericLabel
from mailbot.utils.defaults import MailBotMessageLabelReason
from mailbot.utils.label_engine.actions.base import BaseStateAction
from mailbot.utils.label_engine.actions.first_time_sender_action import FirstTimeSenderAction
from mailbot.utils.label_engine.actions.read_fraction_action import ReadFractionAction
from mailbot.utils.label_engine.actions.set_label_action import SetLabelAction
from mailbot.utils.label_engine.filters.base import AllFilter
from mailbot.utils.label_engine.filters.domain_training_filter import DomainTrainingFilter
from mailbot.utils.label_engine.filters.exception_list_filter import ExceptionListFilter
from mailbot.utils.label_engine.filters.first_time_sender_filter import FirstTimeSenderFilter
from mailbot.utils.label_engine.filters.read_fraction_filter import ReadFractionFilter
from mailbot.utils.label_engine.filters.user_training_filter import UserTrainingFilter
from mailbot.utils.label_engine.actions.default_action import DefaultAction
from mailbot.utils.label_engine.rules.base import Rule
from mailbot.utils.label_engine.rules.rule_driver import RuleDriver
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class SameThreadAction(BaseStateAction):
    """
    Assign label based on same thread messages.
    """

    @staticmethod
    def get_label_for_latest(
        parsed_message: ParsedMessage, message_categories: List[str], algorithm: BaseAlgorithm
    ) -> Dict[str, Any]:
        """
        Find out what label would have been assigned to the latest message in a thread if it's not considered a part of a thread.
        Args:
            parsed_message (ParsedMessage): The latest message in a thread.
            message_categories (List[str]): The categories of the message.
            algorithm (BaseAlgorithm): The algorithm instance.

        Returns:
            Dict[str, Any]: Mapping containing label name, labeling reason and related fields.
        """
        extra_rules = [
            Rule(
                priority=1,
                filter_expression=AllFilter([UserTrainingFilter]),
                actions=[SetLabelAction(message_labelled_due_to=MailBotMessageLabelReason.USER_TRAINING_FILTER)],
            ),
            Rule(
                priority=2,
                filter_expression=AllFilter([DomainTrainingFilter]),
                actions=[SetLabelAction(message_labelled_due_to=MailBotMessageLabelReason.DOMAIN_TRAINING_FILTER)],
            ),
            Rule(
                priority=3,
                filter_expression=AllFilter([ExceptionListFilter]),
                actions=[SetLabelAction(message_labelled_due_to=MailBotMessageLabelReason.EXCEPTION_LIST)],
            ),
            Rule(priority=4, filter_expression=AllFilter([FirstTimeSenderFilter]), actions=[FirstTimeSenderAction]),
            Rule(priority=5, filter_expression=AllFilter([ReadFractionFilter]), actions=[ReadFractionAction]),
            Rule(
                priority=6,
                filter_expression=AllFilter([]),
                actions=[DefaultAction],
            ),
        ]
        rule_driver = RuleDriver(state=parsed_message, rules=extra_rules)
        return rule_driver.sort_by_priorities().apply_first(message_categories=message_categories, algorithm=algorithm)

    @staticmethod
    def execute(
        parsed_message: ParsedMessage, filter_result: Dict[str, Any], algorithm: BaseAlgorithm, **kwargs
    ) -> Dict[str, Any]:
        """
        We assign whole thread the label of higher priority between the label in which thread is already placed,
        and the label current message would have been assigned based on later filters in priority in main label engine.
        """
        message_categories = kwargs.get("message_categories", [])
        action_result = {
            "message_labelled_due_to": MailBotMessageLabelReason.SAME_THREAD_FILTER.value,
            "label_name": MailBotGenericLabel.ZAPPED.value,
        }
        label_ranks = {
            MailBotGenericLabel.WHITE_LIST.value: 1,
            MailBotGenericLabel.ZAPPED.value: 2,
        }
        conversation_messages = Message.objects.filter(
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id, thread_id=parsed_message.thread_id
        )
        for conversation_message in conversation_messages:
            if conversation_message.message_id == parsed_message.message_id:
                continue
            generic_label = conversation_message.generic_label
            if generic_label == Message.GENERIC_LABEL_SENT:
                action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
            elif generic_label in label_ranks.keys():
                if label_ranks[generic_label] < label_ranks[action_result["label_name"]]:
                    action_result["label_name"] = generic_label
        current_thread_label = action_result["label_name"]
        if current_thread_label != MailBotGenericLabel.WHITE_LIST.value:
            latest_message_in_thread_label = SameThreadAction.get_label_for_latest(
                parsed_message=parsed_message, message_categories=message_categories, algorithm=algorithm
            ).get("label_name")
            if label_ranks[latest_message_in_thread_label] < label_ranks[current_thread_label]:
                logger.info(
                    f"Moving whole thread {parsed_message.thread_id} to {latest_message_in_thread_label} for user {parsed_message.user_email}"
                )
                action_result["move_thread_to"] = latest_message_in_thread_label
                action_result["label_name"] = latest_message_in_thread_label
        return action_result
