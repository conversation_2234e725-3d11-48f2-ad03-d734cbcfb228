from typing import Any, Dict
import logging

from mailbot.algorithms.base import BaseAlgorithm
from mailbot.models import MailBotGeneric<PERSON>abel, UserMailBotProfile
from mailbot.utils.defaults import MailBotMessageLabelReason
from mailbot.utils.label_engine.actions.base import BaseStateAction
from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.email import contains_unsubscribe_link

logger = logging.getLogger(__name__)


class FirstTimeSenderAction(BaseStateAction):
    """
    Assign first time sender label as set by user in their preference.
    """

    @staticmethod
    def execute(
        parsed_message: ParsedMessage, filter_result: Dict[str, Any], algorithm: BaseAlgorithm, **kwargs
    ) -> Dict[str, Any]:
        user_mailbot_profile = UserMailBotProfile.objects.get(id=parsed_message.user_mailbot_profile_id)
        message_categories = kwargs.get("message_categories", [])
        has_unsubscribe_link = contains_unsubscribe_link(parsed_message.html_body or parsed_message.text_body)
        message_has_important_categories = algorithm._has_important_categories(message_categories)
        return FirstTimeSenderAction.base_action(
            user_mailbot_profile=user_mailbot_profile,
            message_has_important_categories=message_has_important_categories,
            has_unsubscribe_link=has_unsubscribe_link,
        )

    @staticmethod
    def base_action(
        user_mailbot_profile: UserMailBotProfile,
        message_has_important_categories=False,
        has_unsubscribe_link=False,
    ) -> Dict[str, Any]:
        action_result = {"message_labelled_due_to": MailBotMessageLabelReason.FIRST_TIME_SENDER_FILTER.value}
        first_time_sender_treatment = user_mailbot_profile.preferences.get(
            "first_time_sender_treatment", "low_priority_with_overlay"
        )
        first_time_sender_overlay_limit = user_mailbot_profile.preferences.get(
            "first_time_sender_overlay_limit", "no-limit"
        )
        auto_responder_enabled = user_mailbot_profile.preferences.get("ai_auto_responder_enabled")
        action_result["auto_responder_enabled"] = auto_responder_enabled
        action_result["first_time_sender_treatment"] = first_time_sender_treatment
        action_result["first_time_sender_overlay_limit"] = first_time_sender_overlay_limit

        if has_unsubscribe_link:
            logger.info("Email contains unsubscribe link")
            action_result["has_unsubscribe_link"] = has_unsubscribe_link
        if first_time_sender_treatment == "inbox":
            action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
        elif first_time_sender_treatment == "low_priority":
            action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
        elif first_time_sender_treatment == "low_priority_with_overlay":
            # If both the message is not important then action will be low priority and not send the overlay
            if not message_has_important_categories:
                action_result["first_time_sender_treatment"] = "low_priority"
            action_result["label_name"] = MailBotGenericLabel.ZAPPED.value
        return action_result
