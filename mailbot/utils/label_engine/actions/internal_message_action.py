from typing import Any, Dict
import logging
from mailbot.models import MailBotGenericLabel, UserMailBotProfile
from mailbot.utils.defaults import MailBotMessageCategory, MailBotMessageHeaders, MailBotMessageLabelReason
from mailbot.utils.label_engine.actions.base import BaseStateAction
from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.email import get_secondary_email_label

logger = logging.getLogger(__name__)


class InternalMessageAction(BaseStateAction):
    """
    Action that handles internal message labeling within the system.

    Key responsibilities:
    • Process internal messages generated by the system
    • Distinguish between standard internal messages and secondary account messages
    • Apply appropriate labels based on message source

    Behavior:
    • Standard internal messages -> WHITE_LIST label
    • Both types are marked with INTERNAL_MESSAGE_FILTER reason
    """

    @staticmethod
    def execute(parsed_message: ParsedMessage, filter_result: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Processes a parsed email message to apply internal filtering and labeling logic.
         All internal messages are labeled as WHITE_LIST. If the message is forwarded from a secondary account,
         an additional label specific to the secondary email is also attached to the message.
        Returns:
            Dict[str, Any]: A dictionary containing details about the labeling applied,
            including reasons and label names.
        """
        action_result = {}
        action_result["message_labelled_due_to"] = MailBotMessageLabelReason.INTERNAL_MESSAGE_FILTER.value
        action_result["label_name"] = MailBotGenericLabel.WHITE_LIST.value
        # If the message is forwarded from a secondary account, add a label specific to the secondary email
        if parsed_message.message_headers.get(MailBotMessageHeaders.MESSAGE_CATEGORY.value) in [
            MailBotMessageCategory.FORWARD_FROM_SECONDARY_MAILBOX.value,
            MailBotMessageCategory.DIGEST_FROM_SECONDARY_MAILBOX.value,
        ]:
            secondary_mailbot_profile_id = parsed_message.message_headers.get(
                MailBotMessageHeaders.MAILBOT_PROFILE_ID.value
            )
            secondary_mailbot_profile = UserMailBotProfile.objects.prefetch_related("user").get(
                id=secondary_mailbot_profile_id
            )
            secondary_account_email = secondary_mailbot_profile.user.email
            action_result["custom_labels"] = [get_secondary_email_label(secondary_account_email)]

        return action_result
