from typing import List, Union, Dict, Any

from mailbot.algorithms.base import BaseAlgorithm
from mailbot.utils.label_engine.actions.base import BaseActionType
from mailbot.utils.label_engine.filters.base import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NegateFilter


class Rule:
    """
    Rule class represent a list of actions that should be executed when passed expression evaluates to true.
    """

    def __init__(
        self, priority: int, filter_expression: Union[Negate<PERSON>ilter, AnyFilter, AllFilter], actions: List[BaseActionType]
    ):
        """
        Initializes the rule
        Args:
            priority(int): priority of a rule is used when multiple rules may evaluate to true but only highest priority
            must execute. `1` is for highest priority.
            filter_expression: filter are represented as a nested expression of `NOT, OR, AND` filters
            actions: list of actions to execute if filter evaluates to true
        """
        self.priority = priority
        self.filter_expression = filter_expression
        self.actions = actions

    def evaluate(self, base_state, algorithm: BaseAlgorithm, **kwargs) -> Dict[str, Any]:
        """
        Evaluates the filter expression for the current base state

        Args:
            base_state: state representation
            algorithm: The algorithm instance.
            kwargs: Additional parameters that may be needed for evaluation, including message_categories
        """
        return self.filter_expression.evaluate(base_state, algorithm=algorithm, **kwargs)

    def condition(self, filter_result: Dict[str, Any]) -> bool:
        return self.filter_expression.condition(filter_result)

    def execute(self, base_state, filter_result, algorithm: BaseAlgorithm, **kwargs):
        """
        Execute each action for the current base state.
        Args:
            filter_result: Extra information from filters
            base_state: state representation
            algorithm: The algorithm instance.
            kwargs: Additional parameters that may be needed for actions, including message_categories

        Returns:
            dict: Union of action results

        Warnings:
            Don't use conflicting actions within a single rule, otherwise resulting state may become unpredictable.
        """
        action_results = {}
        for action in self.actions:
            action_result = action.execute(base_state, filter_result, algorithm=algorithm, **kwargs)
            action_results.update(action_result)
        return action_results
