from typing import List

from mailbot.algorithms.base import BaseAlgorithm
from mailbot.utils.label_engine.rules.base import Rule


class RuleDriver:
    """
    Rule Driver applies rule(s) based on base_state passed to it.
    """

    def __init__(self, state, rules: List[Rule]):
        """
        Args:
            state: stateful representation
            rules: list of rules
        """
        self.state = state
        self.rules = rules

    def sort_by_priorities(self):
        """Sort rules according to their priority with the lowest number representing the highest priority"""
        self.rules = sorted(self.rules, key=lambda x: x.priority)
        return self

    def apply_all(self, algorithm: BaseAlgorithm, **kwargs):
        """
        Apply all rules that evaluates to true.
        Each action will have access to information from previous truly evaluated filters.

        Args:
            **kwargs: Additional parameters that may be passed to filters and actions. Expected to contain 'algorithm'.

        Returns:
            dict: Final result will be union of all truly evaluated filters' actions.
        """
        algorithm = kwargs.pop("algorithm", None)
        if not algorithm:
            pass

        filter_results = {}
        action_results = {}
        for rule in self.rules:
            filter_result = rule.evaluate(self.state, algorithm=algorithm, **kwargs)
            if rule.condition(filter_result):
                filter_results.update(filter_result)
                action_result = rule.execute(self.state, filter_results, algorithm=algorithm, **kwargs)
                action_results.update(action_result)
        return filter_results

    def apply_first(self, algorithm: BaseAlgorithm, **kwargs):
        """
        Apply rule that evaluates to true first

        Args:
            **kwargs: Additional parameters that may be passed to filters and actions. Expected to contain 'algorithm'.

        Returns:
            dict: Result of the first rule that evaluates to true
        """
        for rule in self.rules:
            filter_result = rule.evaluate(self.state, algorithm=algorithm, **kwargs)
            if rule.condition(filter_result):
                action_result = rule.execute(self.state, filter_result, algorithm=algorithm, **kwargs)
                return action_result
        return {}
