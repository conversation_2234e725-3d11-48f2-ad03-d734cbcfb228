from django.utils.functional import LazyObject

from mailbot.models import MailBotGenericLabel
from mailbot.utils.defaults import MailBotMessageLabelReason
from mailbot.utils.label_engine.actions import (
    SetLabelAction,
    ReadFractionAction,
    SameThreadAction,
    FirstTimeSenderAction,
    UserDefinedAction,
    DefaultAction,
    InternalMessageAction,
)
from mailbot.utils.label_engine.filters import (
    DomainTrainingFilter,
    ExceptionListFilter,
    InternalMessageFilter,
    ReadFractionFilter,
    LuceneFilter,
    SameThreadFilter,
    UserTrainingFilter,
    FirstTimeSenderFilter,
    AllFilter,
    CalendarInviteFilter,
    SentMessageFilter,
    UserDefinedActionFilter,
)
from mailbot.utils.label_engine.rules import Rule


class MailBotLabelingRules(LazyObject):
    def _setup(self):
        self._wrapped = {
            "main": [
                Rule(
                    priority=1,
                    filter_expression=AllFilter([InternalMessageFilter]),
                    actions=[InternalMessageAction],
                ),
                Rule(
                    priority=2,
                    filter_expression=AllFilter([UserDefinedActionFilter]),
                    actions=[UserDefinedAction],
                ),
                Rule(
                    priority=3,
                    filter_expression=AllFilter([LuceneFilter]),
                    actions=[
                        SetLabelAction(
                            label_name=MailBotGenericLabel.WHITE_LIST.value,
                            message_labelled_due_to=MailBotMessageLabelReason.LUCENE_FILTER,
                        )
                    ],
                ),
                Rule(priority=4, filter_expression=AllFilter([SameThreadFilter]), actions=[SameThreadAction]),
                Rule(
                    priority=5,
                    filter_expression=AllFilter([UserTrainingFilter]),
                    actions=[SetLabelAction(message_labelled_due_to=MailBotMessageLabelReason.USER_TRAINING_FILTER)],
                ),
                Rule(
                    priority=6,
                    filter_expression=AllFilter([SentMessageFilter]),
                    actions=[
                        SetLabelAction(
                            label_name=MailBotGenericLabel.WHITE_LIST.value,
                            message_labelled_due_to=MailBotMessageLabelReason.SENT_MESSAGE_FILTER,
                        )
                    ],
                ),
                Rule(
                    priority=7,
                    filter_expression=AllFilter([DomainTrainingFilter]),
                    actions=[SetLabelAction(message_labelled_due_to=MailBotMessageLabelReason.DOMAIN_TRAINING_FILTER)],
                ),
                Rule(
                    priority=8,
                    filter_expression=AllFilter([ExceptionListFilter]),
                    actions=[SetLabelAction(message_labelled_due_to=MailBotMessageLabelReason.EXCEPTION_LIST)],
                ),
                Rule(priority=9, filter_expression=AllFilter([FirstTimeSenderFilter]), actions=[FirstTimeSenderAction]),
                Rule(priority=10, filter_expression=AllFilter([ReadFractionFilter]), actions=[ReadFractionAction]),
                Rule(
                    priority=11,
                    filter_expression=AllFilter([]),
                    actions=[DefaultAction],
                ),
            ],
        }


MAILBOT_LABELING_RULES_REGISTRY = MailBotLabelingRules()

__all__ = ["MAILBOT_LABELING_RULES_REGISTRY"]
