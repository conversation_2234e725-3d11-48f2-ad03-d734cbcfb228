import logging
from typing import Any, Dict, Optional

from django.conf import settings

from applications.utils.email import get_email_domain
from mailbot.models import DomainTraining, MailBotGenericLabel, SenderProfile
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class DomainTrainingFilter(BaseStateFilter):
    @staticmethod
    def check_for_domain_training(sender_email: str, user_mailbot_profile_id) -> Optional[str]:
        """Check if sender domain is in global white list or local white list.

        Args:
            sender_email (str): Email of the sender.
            user_mailbot_profile_id (int): UserMailBotProfile ID for which to check domain training.

        Returns:
            Optional[str]: Label to assign if sender domain matches any whitelist.
        """
        sender_domain = get_email_domain(sender_email)
        logger.info(f"Checking domain training for email domain {sender_domain}")
        if sender_domain in settings.GENERAL_EMAIL_DOMAINS:
            # We never trained for general domains
            return
        # Check whether the email is in default domain whitelist.
        if sender_domain in settings.EMAIL_DOMAIN_WHITELIST:
            logger.info(f"The domain {sender_domain} is whitelisted by default.")
            return MailBotGenericLabel.WHITE_LIST.value
        try:
            domain_training = DomainTraining.objects.get(
                user_mailbot_profile_id=user_mailbot_profile_id, sender_domain=sender_domain
            )
        except DomainTraining.DoesNotExist:
            return None
        else:
            return domain_training.label_name

    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        return DomainTrainingFilter.base_filter(
            sender_email=parsed_message.from_name_email[1],
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
        )

    @staticmethod
    def base_filter(
        sender_profile: Optional[SenderProfile] = None,
        sender_email: Optional[str] = None,
        user_mailbot_profile_id: Optional[int] = None,
    ):
        if sender_profile:
            sender_email = sender_profile.sender_email
            user_mailbot_profile_id = sender_profile.user_mailbot_profile_id
        elif not sender_email or not user_mailbot_profile_id:
            raise ValueError("Either sender_profile or sender_email and user_mailbot_profile_id are required")
        if domain_training_label := DomainTrainingFilter.check_for_domain_training(
            sender_email=sender_email,
            user_mailbot_profile_id=user_mailbot_profile_id,
        ):
            return {"condition": True, "label_name": domain_training_label}
        else:
            return {"condition": False}
