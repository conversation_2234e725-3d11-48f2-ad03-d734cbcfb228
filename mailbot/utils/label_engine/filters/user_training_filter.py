import logging
from typing import Any, Dict

from applications.utils.email import get_normalized_email
from mailbot.models import SenderProfile
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class UserTrainingFilter(BaseStateFilter):
    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        sender_email = parsed_message.from_name_email[1]
        normalized_sender_email = get_normalized_email(sender_email)
        sender_profile = SenderProfile.objects.get(
            normalized_sender_email=normalized_sender_email,
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
        )
        return UserTrainingFilter.base_filter(sender_profile)

    @staticmethod
    def base_filter(sender_profile: SenderProfile):
        if user_training := sender_profile.user_training:
            return {"condition": True, "label_name": user_training}
        else:
            return {"condition": False}
