import logging
from typing import Any, Dict, Optional

from constance import config as constance_config
from django.conf import settings

from applications.utils.email import get_email_domain, get_normalized_email
from mailbot.models import SenderProfile
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class SentMessageFilter(BaseStateFilter):
    """
    A filter class to determine if a message should be labeled based on the number of emails sent to the sender's email and domain.
    """

    @staticmethod
    def check_emails_sent_to_domain(
        user_email_domain: str, sender_email_domain: str, emails_sent_to_sender_domain: int
    ) -> bool:
        """
        Check if enough emails have been sent to the sender domain.

        Args:
            user_email_domain: Domain of the user's email address
            sender_email_domain: Domain of the sender's email address
            emails_sent_to_sender_domain: Number of emails that have been sent to the sender domain

        Returns:
            bool: True if enough emails have been sent to the sender, False otherwise
        """
        if emails_sent_to_sender_domain >= constance_config.SENT_MESSAGE_FILTER_DOMAIN_THRESHOLD:
            # If the number of sent emails to the same domain of sender are greater than the threshold then whitelist the current message
            logger.info(
                f"Sent message filter satisfied, no. of emails sent to sender domain {sender_email_domain} is {emails_sent_to_sender_domain}"
            )
            return True
        elif emails_sent_to_sender_domain > 0 and sender_email_domain == user_email_domain:
            # If the user has sent at least 1 email to the same domain as himself, then whitelist that domain
            logger.info(
                f"Sent message filter satisfied, sender domain {sender_email_domain} is same as user's email domain"
            )
            return True
        return False

    @staticmethod
    def check_emails_sent_to_sender(
        sender_profile: Optional[SenderProfile] = None,
        user_mailbot_profile_id: Optional[int] = None,
        sender_email: Optional[str] = None,
    ) -> bool:
        """
        Checks if emails have been sent to the current sender.

        Args:
            sender_profile (SenderProfile): The sender profile of the current sender.
            user_mailbot_profile_id (int): The ID of the user's mailbot profile.
            sender_email (str): The email address of the sender.

        Returns:
            bool: True if emails have been sent to the sender, False otherwise.
        """
        if sender_profile:
            return sender_profile.sent_count > 0
        elif sender_email is not None and user_mailbot_profile_id is not None:
            normalized_sender_email = get_normalized_email(sender_email)
            return SenderProfile.objects.filter(
                normalized_sender_email__iexact=normalized_sender_email,
                user_mailbot_profile_id=user_mailbot_profile_id,
                sent_count__gt=0,
            ).exists()
        else:
            raise ValueError("Either sender profile or (sender email and user_mailbot_profile_id) should be provided")

    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        sender_email = parsed_message.from_name_email[1]
        return SentMessageFilter.base_filter(
            user_email=parsed_message.user_email,
            sender_email=sender_email,
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
        )

    @staticmethod
    def base_filter(user_email, sender_profile=None, sender_email=None, user_mailbot_profile_id=None):
        # Either sender profile or (sender email and user_mailbot_profile_id) should be provided
        if sender_profile:
            sender_email = sender_profile.sender_email
            user_mailbot_profile_id = sender_profile.user_mailbot_profile_id
        elif not sender_email or not user_mailbot_profile_id:
            raise ValueError("Either sender profile or (sender email and user_mailbot_profile_id) should be provided")
        sender_email_domain = get_email_domain(sender_email)
        # If the sender's domain is in the list of general email domains then we only check for current sender
        if sender_email_domain in settings.GENERAL_EMAIL_DOMAINS:
            if SentMessageFilter.check_emails_sent_to_sender(sender_profile, user_mailbot_profile_id, sender_email):
                return {"condition": True}
        else:
            # If the sender's domain is not in the list of general email domains, then we check filter based on domain as well
            user_email_domain = get_email_domain(user_email)
            # Count the number of emails sent to the sender's domain
            emails_sent_to_sender_domain = SenderProfile.objects.filter(
                normalized_sender_email__endswith=sender_email_domain,
                user_mailbot_profile_id=user_mailbot_profile_id,
                sent_count__gt=0,
            ).count()
            # Check if conditions for whitelisting the sender's domain are met
            if SentMessageFilter.check_emails_sent_to_domain(
                user_email_domain, sender_email_domain, emails_sent_to_sender_domain
            ):
                return {"condition": True}
            # Check for the exact sender only if the sent emails to the sender domain are greater than 0
            elif emails_sent_to_sender_domain > 0 and SentMessageFilter.check_emails_sent_to_sender(
                sender_profile, user_mailbot_profile_id, sender_email
            ):
                return {"condition": True}
        # If none of the conditions are met, return False
        return {"condition": False}
