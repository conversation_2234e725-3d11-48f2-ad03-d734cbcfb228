import logging
from typing import Any, Dict

from django.db.models import Q

from applications.models import ExceptionList
from applications.utils.email import get_normalized_email
from mailbot.models import SenderProfile
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class ExceptionListFilter(BaseStateFilter):
    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        normalized_sender_email = get_normalized_email(parsed_message.from_name_email[1])
        sender_profile = SenderProfile.objects.get(
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
            normalized_sender_email=normalized_sender_email,
        )
        return ExceptionListFilter.base_filter(sender_profile)

    @staticmethod
    def base_filter(sender_profile: SenderProfile):
        domain_extension_q = Q(type=ExceptionList.TYPE_DOMAIN_EXTENSION) & Q(exception_address=sender_profile.sender_domain.split(".")[-1])
        domain_q = Q(type=ExceptionList.TYPE_DOMAIN) & Q(exception_address=sender_profile.sender_domain)
        email_q = Q(type=ExceptionList.TYPE_EMAIL) & Q(exception_address=sender_profile.sender_email)
        exception_list = list(ExceptionList.objects.filter(domain_extension_q | domain_q | email_q))
        if exception_list:
            idx = 0
            if len(exception_list) == 2:
                if exception_list[1].type == ExceptionList.TYPE_EMAIL:
                    idx = 1
            logger.info(f"Exception list found for {exception_list[idx].type} {exception_list[idx].exception_address}")
            return {"condition": True, "label_name": exception_list[idx].label_name}
        else:
            return {"condition": False}
