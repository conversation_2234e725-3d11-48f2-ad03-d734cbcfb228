import logging
from typing import Any, Dict

from mailbot.utils.defaults import MailBotMessageHeaders
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class InternalMessageFilter(BaseStateFilter):
    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        if category := parsed_message.message_headers.get(MailBotMessageHeaders.MESSAGE_CATEGORY.value):
            logger.info(f"Skipping the processing of mail sent by mailbot for category {category}")
            return {"condition": True}
        return {"condition": False}
