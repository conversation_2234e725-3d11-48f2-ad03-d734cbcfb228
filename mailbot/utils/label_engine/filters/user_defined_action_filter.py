import logging
from typing import Any, Dict

from applications.utils.email import get_normalized_email
from mailbot.models import SenderProfile
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class UserDefinedActionFilter(BaseStateFilter):
    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        sender_email = parsed_message.from_name_email[1]
        normalized_sender_email = get_normalized_email(sender_email)
        sender_profile = SenderProfile.objects.get(
            normalized_sender_email=normalized_sender_email,
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
        )
        if sender_profile.user_action:
            if sender_profile.user_action_reason == SenderProfile.USER_ACTION_REASON_UNSUBSCRIBED:
                # check if the email has unsubscribed headers, if yes, then move to trash
                # else it could be important email (like password reset, etc.)
                if parsed_message.unsubscribe_link is None and parsed_message.unsubscribe_link_mail_to is None:
                    return {"condition": False}
            logger.info("User action filter satisfied")
            return {"condition": True}
        else:
            return {"condition": False}
