import logging
from typing import Any, Dict

from mailbot.utils.email import has_ics_extension_attachment
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class CalendarInviteFilter(BaseStateFilter):
    """
    White list emails if they have calendar invite with .ics extension
    TODO: [HYu101GM](Add auto archive for calendar invite emails after that calendar event ends)
    """

    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        if has_ics_extension_attachment(parsed_message):
            logger.info("Calendar invite attachment found")
            return {"condition": True}
        return {"condition": False}
