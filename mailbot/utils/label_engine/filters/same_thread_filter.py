import logging
from typing import Any, Dict

from django.conf import settings

from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class SameThreadFilter(BaseStateFilter):
    @staticmethod
    def evaluate(parsed_message: ParsedMessage, **kwargs) -> Dict[str, Any]:
        if parsed_message.service_provider == settings.SERVICE_PROVIDER_GOOGLE:
            if parsed_message.message_id != parsed_message.thread_id:
                return {"condition": True}
        elif parsed_message.service_provider == settings.SERVICE_PROVIDER_MICROSOFT:
            # In outlook thread ID is not same as first message ID, but is common for all messages in a thread
            is_non_first_message_in_conversation = parsed_message.message_headers.get("In-Reply-To")
            if is_non_first_message_in_conversation:
                return {"condition": True}
        return {"condition": False}
