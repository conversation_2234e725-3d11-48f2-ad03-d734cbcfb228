import logging

from typing import Any, Dict
from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from applications.utils.email import get_normalized_email
from execfn import ApplicationTag
from mailbot.algorithms.base import BaseAlgorithm
from mailbot.models import SenderProfile
from mailbot.utils.email import contains_unsubscribe_link
from mailbot.utils.label_engine.filters.base import BaseStateFilter
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class FirstTimeSenderFilter(BaseStateFilter):
    @staticmethod
    def evaluate(parsed_message: ParsedMessage, algorithm: BaseAlgorithm, **kwargs) -> Dict[str, Any]:
        if not check_feature(
            user_id=parsed_message.user_id,
            feature_flag=MailBotFeatureFlag.FIRST_TIME_SENDER_OVERLAY,
            application_tag=ApplicationTag.MailBot,
        ):
            return {"condition": False}
        normalized_sender_email = get_normalized_email(parsed_message.from_name_email[1])
        sender_profile = SenderProfile.objects.select_related("user_mailbot_profile").get(
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
            normalized_sender_email=normalized_sender_email,
        )
        return FirstTimeSenderFilter.base_filter(
            sender_profile=sender_profile, algorithm=algorithm, parsed_message=parsed_message
        )

    @staticmethod
    def base_filter(
        sender_profile: SenderProfile,
        algorithm: BaseAlgorithm,
        parsed_message: ParsedMessage = None,
    ):
        is_first_message = sender_profile.total_count == 1
        message_body = parsed_message.html_body or parsed_message.text_body
        has_unsubscribe_link = parsed_message and contains_unsubscribe_link(message_body)
        if is_first_message and has_unsubscribe_link:
            logger.info("Email contains unsubscribe link")
            return {"condition": is_first_message, "has_unsubscribe_link": has_unsubscribe_link}
        elif is_first_message:
            return {"condition": is_first_message}
        else:
            should_send_fts_overlay = algorithm.should_send_fts_overlay(sender_profile)
            return {"condition": should_send_fts_overlay}
