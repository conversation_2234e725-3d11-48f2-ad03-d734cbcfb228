from applications.models import ExceptionList
from execfn.common.utils.slack import send_slack_message
from mailbot.models import Sender<PERSON><PERSON><PERSON><PERSON>, MailBotGenericLabel
from mailbot.utils.defaults import MAILBOT_INSIGHTS_SLACK_CHANNEL_ID, MailBotInsightsSlackMessageFormat


def check_exception_list_trained(sender_profile: SenderProfile, user_training: str):
    """
    Check if the sender is in the exception list when trained by the user
    and send a Slack message to the user training channel.

    Args:
        sender_profile (SenderProfile): The sender profile to check
        user_training (str): The user training to send to the Slack channel
    """
    if user_training == MailBotGenericLabel.WHITE_LIST.value:
        return
    # Check if the sender email is in the exception list
    exception_list = ExceptionList.objects.filter(
        type=ExceptionList.TYPE_EMAIL, exception_address=sender_profile.sender_email
    )
    if exception_list.exists():
        send_slack_message(
            channel_id=MAILBOT_INSIGHTS_SLACK_CHANNEL_ID,
            text=MailBotInsightsSlackMessageFormat.EXCEPTION_LIST_ADDRESS_TRAINED.value.format(
                user_email=sender_profile.user_mailbot_profile.user.email,
                exception_type=ExceptionList.TYPE_EMAIL,
                exception_address=sender_profile.sender_email,
                user_training=user_training,
            ),
        )
    # Check if the sender domain is in the exception list
    exception_list = ExceptionList.objects.filter(
        type=ExceptionList.TYPE_DOMAIN, exception_address=sender_profile.sender_domain
    )
    if exception_list.exists():
        send_slack_message(
            channel_id=MAILBOT_INSIGHTS_SLACK_CHANNEL_ID,
            text=MailBotInsightsSlackMessageFormat.EXCEPTION_LIST_ADDRESS_TRAINED.value.format(
                user_email=sender_profile.user_mailbot_profile.user.email,
                exception_type=ExceptionList.TYPE_DOMAIN,
                exception_address=sender_profile.sender_domain,
                user_training=user_training,
            ),
        )
