from applications.feature_flags.defaults import MailBotFeatureFlag
from mailbot.utils.rule_engine.filters.base import BaseFilter


class OtpFilter(BaseFilter):
    """
    Identify whether an email is related to verification.
    Verification emails are ones which contain an OTP / Code or contain a link to verify email.
    It should not be an acknowledgement of verification done.
    """

    def __init__(self):
        super().__init__(tag="OTP", flag_name=MailBotFeatureFlag.LUCENE_OTP, check_in_subject=True)
