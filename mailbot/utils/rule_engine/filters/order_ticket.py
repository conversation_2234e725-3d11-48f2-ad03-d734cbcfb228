from applications.feature_flags.defaults import MailBotFeatureFlag
from mailbot.utils.rule_engine.filters.base import BaseFilter


class OrderTicketFilter(BaseFilter):
    """
    Identify whether the following email is an order email or not.
    An order email is one that confirms a booking for a specific event or reservation with an associated date and time.
    """

    def __init__(self):
        super().__init__(
            tag="OrderTicket",
            flag_name=MailBotFeatureFlag.LUCENE_ORDER_TICKET,
            check_in_subject=True,
            check_in_body=True,
        )
