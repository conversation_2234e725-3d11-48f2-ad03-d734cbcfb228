from applications.feature_flags.defaults import MailBotFeatureFlag
from mailbot.utils.rule_engine.filters.base import BaseFilter


class PaymentsFilter(BaseFilter):
    """ """

    def __init__(self):
        super().__init__(
            tag="PaymentAlert",
            flag_name=MailBotFeatureFlag.LUCENE_PAYMENT_ALERT,
            check_in_body=True,
            check_in_subject=True,
            check_date_in_text=True,
        )
