from applications.feature_flags.defaults import MailBotFeatureFlag
from mailbot.utils.rule_engine.filters.base import BaseFilter


class BankStatementFilter(BaseFilter):
    """
    Identifies whether an email contains a statement from a financial service provider.
    """

    def __init__(self):
        super().__init__(
            tag="BankStatement",
            flag_name=MailBotFeatureFlag.LUCENE_BANK_STATEMENTS,
            check_in_subject=True,
            check_in_body=True,
            check_date_in_text=True,
        )
