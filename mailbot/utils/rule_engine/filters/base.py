import logging
from typing import List, Tuple, Optional

from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from execfn import ApplicationTag
from mailbot.models import AnalyticsEvents
from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.rule_engine.filters.utils import has_date_in_text
from mailbot.utils.search import search_phrases_all
from nlp.models import LuceneFilter
from nlp.utils.ner import binary_classification

logger = logging.getLogger(__name__)


class BaseFilter:
    """
    The BaseFilter class provides a foundation for filter implementations.
    """

    def __init__(
        self,
        tag,
        flag_name: MailBotFeatureFlag = None,
        check_in_subject=False,
        check_in_body=False,
        check_date_in_text=False,
    ):
        self.tag = tag
        self.flag_name = flag_name
        self.check_in_subject = check_in_subject
        self.check_in_body = check_in_body
        self.check_date_in_text = check_date_in_text

    def evaluate(self, parsed_message: ParsedMessage, lucene_filters):
        """
        Evaluate if the given email is of a particular tag
        Returns:
            A boolean representing if the given email is of particular tag
        """
        if not check_feature(
            user_id=parsed_message.user_id, feature_flag=self.flag_name, application_tag=ApplicationTag.MailBot
        ):
            return False
        try:
            lucene_filter: LuceneFilter = lucene_filters.all().get(tag=self.tag)
        except LuceneFilter.DoesNotExist:
            logger.exception("Lucene filter does not exist in the database", extra={"tag": self.tag})
            return False
        else:
            lucene_result = False
            phrases: List[str] = lucene_filter.phrases or []
            phrases: List[List[str]] = [[x.strip() for x in phrase.split(",")] for phrase in phrases]
            prompt_template = lucene_filter.prompt_template
            matched_phrases = []

            if self.check_in_subject:
                subject_query_result, matched_phrases = self.search_in_subject(parsed_message, phrases)
                lucene_result = lucene_result or subject_query_result

            if not lucene_result and self.check_in_body:
                body_query_result, matched_phrases = self.search_in_body(parsed_message, phrases)
                lucene_result = lucene_result or body_query_result

            if lucene_result and self.check_date_in_text:
                has_date_result = has_date_in_text(parsed_message.text_body)
                lucene_result = lucene_result and has_date_result
            if matched_phrases:
                logger.info(
                    f"Matched phrases for Message ID {parsed_message.message_id} for tag {self.tag} are {matched_phrases}"
                )
            analytics_event_metadata = {
                "message_id": parsed_message.message_id,
                "matched_phrases": matched_phrases,
                "text_filter_result": lucene_result,
                "llm_result": None,
                "tag": self.tag,
                "message_received_at": parsed_message.received_at.isoformat(),
            }
            if lucene_result and prompt_template:
                llm_result = self.validate_with_llm(
                    parsed_message=parsed_message, prompt_template=prompt_template, lucene_result=lucene_result
                )
                lucene_result &= llm_result
                analytics_event_metadata["llm_result"] = llm_result
            # TODO : Move this to activity log service
            AnalyticsEvents.create_analytics_event(
                event_type=AnalyticsEvents.EVENT_TYPE_LUCENE_FILTER,
                user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
                metadata=analytics_event_metadata,
            )
            return lucene_result

    def search_in_body(
        self, parsed_message: ParsedMessage, body_phrases: List[List[str]]
    ) -> Tuple[bool, Optional[list]]:
        """
        Searches for specific phrases in the body of an email.

        Returns:
            A boolean representing if the phrases match email's body and the matching phrases list
        """
        if body_phrases is None:
            return False, None
        # All items should match from at least one sublist
        result = False
        matched_phrases = []
        for phrases in body_phrases:
            result, matched_phrases = search_phrases_all(
                document_id=parsed_message.message_id, text=parsed_message.text_body, phrases=phrases
            )
            if result:
                break
        return result, matched_phrases

    def search_in_subject(
        self, parsed_message: ParsedMessage, subject_phrases: List[List[str]]
    ) -> Tuple[bool, Optional[list]]:
        """
        Searches for specific phrases in the subject of an email.

        Returns:
            A boolean representing if the phrases match email's subject and the matching phrases list
        """
        if subject_phrases is None:
            return False, None
        # All items should match from any at least sublist
        result = False
        matched_phrases = []
        for phrases in subject_phrases:
            result, matched_phrases = search_phrases_all(
                document_id=parsed_message.message_id, text=parsed_message.subject, phrases=phrases
            )
            if result:
                break
        return result, matched_phrases

    def validate_with_llm(self, parsed_message: ParsedMessage, prompt_template: str, lucene_result: bool):
        """
        Use llm to verify if tag correctly flagged by lucene.

        Returns:
            bool: True if tag correctly flagged by
        """
        logger.info(f"Evaluating Message ID {parsed_message.message_id} and TAG {self.tag} with LLM")
        llm_result = False
        try:
            if BaseFilter.verify_tag(parsed_message, prompt_template):
                llm_result = True
                logger.info(f"LLM Result is true for {parsed_message.message_id} and tag :{self.tag}")
            else:
                logger.info(
                    f"Lucene predicted email as {lucene_result} and llm predicted as false for id : {parsed_message.message_id}"
                )
            return llm_result
        except Exception:
            logger.exception("Error occurred while evaluating tag using llm")
            # If LLM API fails then consider lucene as incorrect categorisation
            # TODO: Retry API call or catch exception in the calling function
            return False

    @staticmethod
    def verify_tag(parsed_message: ParsedMessage, prompt_template: str):
        """
        Reduces false positives by using LLM to validate the correct flagging of a specific Lucene tag for the email.
        Args:
            parsed_message : The user's parsed email
            prompt_template : Unique prompt for each individual lucene tag

        Returns:
            A boolean representing if satisfying condition or not
        """
        conversation = "\n".join([parsed_message.subject, parsed_message.text_body])
        return binary_classification(conversation, prompt_template)
