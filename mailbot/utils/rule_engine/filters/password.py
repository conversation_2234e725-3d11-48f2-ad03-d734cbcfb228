from applications.feature_flags.defaults import MailBotFeatureFlag
from mailbot.utils.rule_engine.filters.base import BaseFilter


class PasswordFilter(BaseFilter):
    """
    Identify whether an email is related to resetting password or creating password or confirmation of password change.

    Consider the characteristics of legitimate password reset emails, such as the specific wording in the subject line (e.g., 'Password Reset', 'Reset Your Password'),
    the presence of clear instructions and time-sensitive links in the email body, the absence of requests for personal information,
    the professional language and tone, and the inclusion of contact information for customer support.

    Also, if the email confirms to the user that their password was changed or that an attempt to change password was made, then also, it's a password email.
    """

    def __init__(self):
        super().__init__(tag="Password", flag_name=MailBotFeatureFlag.LUCENE_PASSWORD, check_in_subject=True)
