from dateutil.parser import parse


def has_date_in_text(text: str) -> bool:
    """
    Search if any of the word match with date using dateutil parser in given text

    Args:
        text: text string to search

    Returns:
        A boolean representing if any of the word in text matched with date
    """
    result = False
    for word in text.split(" "):
        try:
            # Checking if the word is the part of date
            if parse(word, fuzzy=True, ignoretz=True):
                result = True
                break
        except:
            pass
    return result
