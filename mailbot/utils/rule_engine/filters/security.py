from applications.feature_flags.defaults import MailBotFeatureFlag
from mailbot.utils.rule_engine.filters.base import BaseFilter


class SecurityFilter(BaseFilter):
    """
    Identify whether an email is related to security alert or not.
    Security alerts are ones that contain information related to log in from a new device, location, or IP address.
    """

    def __init__(self):
        super().__init__(tag="Security", flag_name=MailBotFeatureFlag.LUCENE_SECURITY_ALERT, check_in_subject=True)
