from mailbot.utils.email import has_ics_extension_attachment
from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.rule_engine.filters.base import BaseFilter


class CalendarEventFilter(BaseFilter):
    """
    Implements filtering functionality for calendar event emails.
    """

    def __init__(self):
        super().__init__(tag="CalendarInvite")

    def evaluate(self, parsed_message: ParsedMessage, lucene_filters):
        """
        Evaluate if the given message has an ICS extension attached.

        Returns:
            bool: Whether the given email has an ICS extension
        """

        # Searching for a .ics file extension and return if found
        return has_ics_extension_attachment(parsed_message=parsed_message)
