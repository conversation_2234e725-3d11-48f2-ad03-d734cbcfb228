def apply_operator(lhs, operator, rhs=None):
    if operator == "&":
        return int(lhs) and int(rhs)
    elif operator == "|":
        return int(lhs) or int(rhs)
    else:
        return int(not int(lhs))


def evaluate_logical_expression(expression):
    """
    Calculates the result of a logical expression.

    Args:
        expression (str): A logical expression in the format of ((operand, operator, operand), operator, (operand, operator, operand)).
            The allowed operators are '&' (AND), '|' (OR), and '!' (NOT).
            The operands can be either 0 or 1.
        Example: ((1&0)|(!0))

    Returns:
        int: The result of the logical expression, which will be either 0 or 1.

    Raises:
        ValueError: If the expression is not provided in the correct format or contains invalid operands or operators.
    """
    stack = []
    for token in expression:
        if token == ")":
            stack_elements = []

            while stack and stack[-1] != "(":
                stack_elements.append(stack.pop())

            stack.pop()
            stack_elements.reverse()

            if len(stack_elements) == 3:
                lhs, operator, rhs = stack_elements
                result = apply_operator(lhs, operator, rhs)
            elif len(stack_elements) == 2:
                operator, lhs = stack_elements
                result = apply_operator(lhs, operator)
            else:
                result = stack_elements[0]

            stack.append(result)
        else:
            stack.append(token)
    return stack[0]
