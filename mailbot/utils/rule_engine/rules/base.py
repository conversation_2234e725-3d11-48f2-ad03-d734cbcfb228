import importlib
import re
from abc import ABC

from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.rule_engine.rules.helper import evaluate_logical_expression
from nlp.models import LuceneFilter

package_path = "mailbot.utils.rule_engine.filters"


class BaseRule(ABC):
    """
    The BaseRule dynamically retrieves classes based on the class name string and evaluates the rules using logical expressions.
    """

    OPERATORS = {"OR": "|", "AND": "&", "NOT": "!"}

    def __init__(self):
        self.rules = ""
        self.passed_filters: tuple = ()
        self.failed_filters: tuple = ()

    def set_rules(self, rules):
        # TODO: Validate Rules- https://trello.com/c/dEpy48eW/409
        self.rules = rules

    def evaluate(self, parsed_message: ParsedMessage):
        """
        Evaluates the rules expression and returns the result as a boolean value.

        The evaluate method performs the following steps:
        1. Takes the rules expression and converts it into a logical boolean expression.
        2. Evaluates the logical boolean expression using the provided arguments.
        3. Returns the result of the evaluation as a boolean value.

        Returns:
            bool: Returns 1 if the rules expression evaluates to True.
                Returns False otherwise.
        """
        expression = self.rules

        # Add regex to parse all the strings of rules into list
        rules_items = re.findall(r"\b(?!AND|OR|NOT\b)\w+", expression)
        lucene_filters = LuceneFilter.objects.all()
        for item in rules_items:
            # Dynamically retrieving class using the class name string.
            module = importlib.import_module(package_path)
            class_name = getattr(module, item)

            class_object = class_name()
            result = class_object.evaluate(parsed_message, lucene_filters)
            # for evaluation of final expression, we need integer values for the boolean results
            bool_result = str(int(result))
            expression = expression.replace(item, bool_result)

            if result:
                self.passed_filters += (class_object,)
            else:
                self.failed_filters += (class_object,)

        # Replace Logical operator from expression
        for key, value in self.OPERATORS.items():
            expression = expression.replace(key, value)

        # Remove space from expression
        expression = expression.replace(" ", "")
        # updated expression example = "((1&0)|(!0))"
        return evaluate_logical_expression(expression)
