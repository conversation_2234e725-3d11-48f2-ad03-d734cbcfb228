from django.conf import settings
from webpush import send_user_notification

from mailbot.utils.defaults import GmailKnownLabelName, OutlookKnownLabelName


def should_send_inbox_notification(user_mailbot_profile, parsed_message):
    """
    Determine whether PWA inbox notification is to be sent based on the latest label of the message.
    Args:
        user_mailbot_profile: User mailbot profile for user to whom to send the notification
        parsed_message: Latest parsed message from the API after some mailbot action.

    Returns:
        bool: Whether notification should be sent or not
    """
    if parsed_message.service_provider == settings.SERVICE_PROVIDER_GOOGLE:
        if GmailKnownLabelName.INBOX.value in parsed_message.label_ids:
            return True
    else:
        if OutlookKnownLabelName.INBOX.value in user_mailbot_profile.label_mappings.get(parsed_message.label_ids[0]):
            return True
    return False


def send_inbox_notification(service_provider, user, message_id, sender_name, sender_email, subject, text_body):
    """
    Send PWA inbox notification to the specified user.
    Args:
        user: Current user
        service_provider: Service provider used by the user
        message_id: Message ID of the message
        sender_name: Sender name who sent the message
        sender_email: Sender email who sent the message
        subject: Subject of the message
        text_body: Message body in text format
    """
    if service_provider == settings.SERVICE_PROVIDER_GOOGLE:
        mailbox_url = f"https://mail.google.com/mail/u/{user.email}/#inbox/{message_id}"
    else:
        mailbox_url = "https://outlook.live.com/mail/0/inbox"
    payload = {
        "sender_name": sender_name[:100],
        "sender_email": sender_email[:100],
        "subject": subject[:100],
        "body": text_body[:100],
        "mailbox_url": mailbox_url,
    }
    send_user_notification(user, payload, ttl=60)
