import logging

from pydantic import BaseModel, Field
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.runnables import Runnable
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
from django.conf import settings
from django.utils.functional import SimpleLazyObject

from mailbot.utils.defaults import InboxOverlayLabel

INBOX_OVERLAY_PROMPT_TEMPLATE = """You are an experienced personal assistant who is reviewing the email their boss got. For this email, you will help your boss by summarising this email and providing strictly these 3 outputs based on the following instructions:
1. One liner summary of the email, make sure to add most critical information in the summary so user doesnt have to read the entire email. The summary should not exceed 100 characters.
2. Label based on Eisenhower Matrix (Do now, Schedule, Delegate or Eliminate)
3. Action that executive needs to take. If no action is required, say "No action". The action should not exceed 100 words.

{format_instructions}

Email Subject: {subject}
Email Body: {body}
"""

logger = logging.getLogger(__name__)


class InboxOverlayResult(BaseModel):
    summary: str = Field(description="One liner summary of the email")
    label: InboxOverlayLabel = Field(description="Label based on Eisenhower Matrix")
    action: str = Field(description="Action that executive needs to take. If no action is required, say 'No action'")


class InboxOverlay:
    def __init__(self):
        """
        Initializes the InboxOverlay.

        Args:
            llm: An instance of a Langchain BaseChatModel (e.g., ChatOpenAI, ChatGoogleGenerativeAI, ChatAnthropic).
        """

        self.llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash", google_api_key=settings.GOOGLE_GEMINI_API_KEY)

        self.prompt_template = ChatPromptTemplate.from_template(INBOX_OVERLAY_PROMPT_TEMPLATE)
        self.output_parser = PydanticOutputParser(pydantic_object=InboxOverlayResult)

        prompt_with_format_instructions = self.prompt_template.partial(
            format_instructions=self.output_parser.get_format_instructions()
        )

        self.chain: Runnable = prompt_with_format_instructions | self.llm | self.output_parser

    def get_result(self, subject: str, body: str) -> InboxOverlayResult:
        try:
            input_data = {"subject": subject or "", "body": body or ""}
            result = self.chain.invoke(input_data)
            return result
        except Exception as e:
            logger.exception("Error during getting inbox overlay result", extra={"error": e})
            raise


_inbox_overlay_instance = SimpleLazyObject(lambda: InboxOverlay())


def get_inbox_overlay_instance() -> InboxOverlay:
    """
    Get the singleton instance of InboxOverlay (lazy loaded via SimpleLazyObject).
    """
    return _inbox_overlay_instance
