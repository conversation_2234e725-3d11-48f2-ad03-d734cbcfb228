"""
Handler for the "Lucene Alert" overlay in MailBot.

This module defines the logic for displaying an overlay to users when they receive an email that matches the
`lucene_alert_filter`. The overlay provides users with information and options for handling potentially important
or sensitive emails identified by Lucene-based filtering rules.

Key Features:
- Presents an overlay to the user for emails matching Lucene alert criteria.
- Supports forwarding the overlay to the user's primary account if the user has enabled this preference.
- Automatically archives the original email after processing, as determined by the overlay handler.

Related:
    - `lucene_alert_filter`: Determines when this overlay should be shown.
    - `forward_lucene_alert_to_primary`: Triggered if the user opts to forward the overlay to their primary account.

This module is intended to be used as part of the MailBot overlay system for enhancing user awareness and control
when interacting with emails flagged by advanced filtering rules.
"""
from typing import Dict, Any
from mailbot.utils.overlays.base import BaseOverlay
from mailbot.utils.defaults import MailBotMessageCategory, MailBotMessageHeaders, MailBotTemplateTag
from mailbot.utils.jwt import jwt_encode
from django.conf import settings
from django.utils import timezone
from mailbot.utils.email_context import MailOperationType, MailBotGenericLabel


class LuceneAlert(BaseOverlay):
    """
    Handler for Lucene Alert overlay emails.
    """

    TEMPLATE_TAG = MailBotTemplateTag.LUCENE_ALERT.value

    def __init__(self, *args, **kwargs):
        """
        Initialize the handler.
        """
        super().__init__(*args, **kwargs)

    @property
    def to_name(self):
        return f"{self.user.get_full_name()} <{self.user.email}>"

    @property
    def from_name(self):
        return f"{self.sender_name} (via EmailZap)"

    def build_context(self) -> Dict[str, Any]:
        """
        Build the context for the Lucene Alert overlay.

        Returns:
            Dict[str, Any]: The context dictionary
        """
        all_recipients = self.get_recipients()
        other_recipients = ", ".join([recipient for recipient in all_recipients if recipient != self.user_email])

        skip_archive_token = jwt_encode(
            payload={
                "exp": timezone.now() + timezone.timedelta(days=1),
                "user_mailbot_profile_id": self.user_mailbot_profile.id,
                "operation": MailOperationType.CANCEL_LUCENE_ALERT.value,
                "message_id": self.parsed_message.message_id,
                "new_label": MailBotGenericLabel.WHITE_LIST.value,
            }
        )
        skip_archive_sender_token = jwt_encode(
            payload={
                "exp": timezone.now() + timezone.timedelta(days=1),
                "operation": MailOperationType.CANCEL_LUCENE_ALERT_FOR_SENDER.value,
                "user_mailbot_profile_id": self.user_mailbot_profile.id,
                "message_id": self.parsed_message.message_id,
                "new_label": MailBotGenericLabel.WHITE_LIST.value,
            }
        )
        skip_archive_domain_token = jwt_encode(
            payload={
                "exp": timezone.now() + timezone.timedelta(days=1),
                "operation": MailOperationType.CANCEL_LUCENE_ALERT_FOR_DOMAIN.value,
                "user_mailbot_profile_id": self.user_mailbot_profile.id,
                "message_id": self.parsed_message.message_id,
                "new_label": MailBotGenericLabel.WHITE_LIST.value,
            }
        )
        return {
            "frontend_base_url": settings.FRONTEND_BASE_URL,
            "message_subject": self.parsed_message.subject,
            "message_body": self.parsed_message.html_body or self.parsed_message.text_body,
            "sender_email": self.sender_email,
            "general_domain": self.sender_domain in settings.GENERAL_EMAIL_DOMAINS,
            "skip_archive_token": skip_archive_token,
            "skip_archive_sender_token": skip_archive_sender_token,
            "skip_archive_domain_token": skip_archive_domain_token,
            "preview_text": self.parsed_message.text_body[:150],
            "other_recipients": other_recipients,
        }

    def get_headers(self) -> Dict[str, str]:
        """
        Get the headers for the overlay email.

        Returns:
            Dict[str, str]: The headers for the email
        """
        headers = super().get_headers()
        headers[MailBotMessageHeaders.MESSAGE_CATEGORY.value] = MailBotMessageCategory.LUCENE_ALERT.value
        return headers

    def should_archive_original_message(self) -> bool:
        """
        Determine if the original email should be auto-archived.

        Returns:
            bool: True if the original email should be auto-archived
        """
        return True
