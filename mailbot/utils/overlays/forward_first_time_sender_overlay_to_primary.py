"""
Handler for forwarding First Time Sender overlays from secondary accounts to primary accounts.

This module defines the logic for forwarding First Time Sender overlays to a user's primary account
when they receive an email on a secondary account from a first-time sender.

Key Features:
- Forwards First Time Sender overlays from secondary email accounts to the user's primary account.
- Maintains the same overlay appearance and functionality as the standard First Time Sender overlay.
- Includes metadata about the secondary account in the forwarded overlay.
- Preserves all sender profile and overlay options functionality.

Use Cases:
- Users who want to monitor first-time senders across multiple email accounts.
- Organizations that want central visibility into new senders across various addresses.

This module is part of the multi-account management feature in MailBot, allowing users to
maintain oversight of new communications across all their email accounts from a primary account.
"""

from .mixins import ForwardToPrimaryMixin
from .first_time_sender_overlay import FirstTimeSenderOverlay


class ForwardFirstTimeSenderOverlayToPrimary(ForwardToPrimaryMixin, FirstTimeSenderOverlay):
    """
    Handler for forwarding First Time Sender overlays from secondary accounts to primary accounts.

    This class extends the FirstTimeSenderOverlay using ForwardToPrimaryMixin to handle the forwarding of
    First Time Sender overlays triggered on secondary email accounts to the user's primary account. It maintains
    the same context and appearance as regular First Time Sender overlays while adding metadata about the
    secondary account from which the email originated.

    The mixin automatically handles:
    - Changing the recipient from secondary to primary user
    - Adding secondary account metadata to headers and context
    - Using the same template as the base FirstTimeSenderOverlay
    - Preserving sender profile and overlay options functionality
    """

    def __init__(self, *args, **kwargs):
        """
        Initialize the handler.

        Additional kwargs:
            sender_profile: The sender profile
            options_for_overlay: Options to show in the overlay
        """
        super().__init__(*args, **kwargs)
