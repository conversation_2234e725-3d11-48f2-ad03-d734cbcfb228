"""
Registry for overlay handlers.

This module provides a centralized registry for overlay handlers that are discovered
and loaded once when the Django app starts up.
"""
import logging
import importlib
import inspect
import os
from pathlib import Path
from typing import Dict, Type, Optional
from mailbot.utils.overlays.base import BaseOverlay
from mailbot.utils.overlays.mixins import ForwardToPrimaryMixin

logger = logging.getLogger(__name__)


class OverlayRegistry:
    """
    Registry for overlay handlers.

    This class manages the discovery and storage of overlay handler classes.
    Handlers are discovered once at app startup and cached for efficient access.
    """

    _handlers: Dict[str, Type] = {}
    _is_initialized = False

    @classmethod
    def initialize(cls) -> None:
        """
        Initialize the registry by discovering all overlay handlers.

        This method should be called once during Django app startup.
        """
        if cls._is_initialized:
            logger.debug("Overlay registry already initialized, skipping discovery")
            return

        logger.info("Initializing overlay registry...")
        cls._discover_handlers()
        cls._is_initialized = True
        logger.info(f"Overlay registry initialized with {len(cls._handlers)} handlers: {list(cls._handlers.keys())}")

    @classmethod
    def _discover_handlers(cls) -> None:
        """
        Discover all overlay handlers in the overlays directory.

        Each handler must either:
        - inherit from BaseOverlay (directly or via mixins)
        - use ForwardToPrimaryMixin
        """
        try:
            overlay_package = importlib.import_module("mailbot.utils.overlays")
            overlay_path = Path(overlay_package.__file__).parent

            for filename in os.listdir(overlay_path):
                if filename.endswith(".py") and not filename.startswith("__"):
                    module_name = filename[:-3]
                    module_path = f"mailbot.utils.overlays.{module_name}"

                    try:
                        module = importlib.import_module(module_path)

                        for name, obj in inspect.getmembers(module, inspect.isclass):
                            if obj.__module__ != module.__name__:
                                continue  # skip imported classes

                            # Check subclass relationship
                            is_overlay = issubclass(obj, BaseOverlay) and obj is not BaseOverlay
                            uses_mixin = ForwardToPrimaryMixin in inspect.getmro(obj)
                            if is_overlay or uses_mixin:
                                key = f"{module_name}"
                                cls._handlers[key] = obj

                    except (ImportError, AttributeError) as e:
                        logger.warning(f"Failed to import overlay module {module_path}: {e}")

        except Exception as e:
            logger.error(f"Error discovering overlay handlers: {e}")

    @classmethod
    def get_handler(cls, overlay_key: str) -> Optional[Type]:
        """
        Get an overlay handler by key.

        Args:
            overlay_key: The key of the overlay handler to retrieve

        Returns:
            Optional[Type]: The overlay handler class, or None if not found
        """
        if not cls._is_initialized:
            logger.warning("Overlay registry not initialized, initializing now...")
            cls.initialize()
        return cls._handlers.get(overlay_key)

    @classmethod
    def get_all_handlers(cls) -> Dict[str, Type]:
        """
        Get all registered overlay handlers.

        Returns:
            Dict[str, Type]: Dictionary mapping overlay keys to handler classes
        """
        if not cls._is_initialized:
            logger.warning("Overlay registry not initialized, initializing now...")
            cls.initialize()

        return cls._handlers.copy()

    @classmethod
    def is_initialized(cls) -> bool:
        """
        Check if the registry has been initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return cls._is_initialized

    @classmethod
    def reset(cls) -> None:
        """
        Reset the registry (mainly for testing purposes).
        """
        cls._handlers.clear()
        cls._is_initialized = False
