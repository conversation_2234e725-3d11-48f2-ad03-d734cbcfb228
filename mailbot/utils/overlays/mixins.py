"""
Mixins for overlay functionality.

This module provides reusable mixins that can be combined with overlay classes
to add specific behaviors like forwarding to primary accounts.
"""
from typing import Dict, Any, Optional
from mailbot.utils.defaults import MailBotMessageCategory, MailBotMessageHeaders


class ForwardToPrimaryMixin:
    """
    Mixin that adds forwarding behavior to any overlay.

    When mixed into an overlay class, it modifies the overlay to:
    1. Send to the user's primary account instead of the current user
    2. Add secondary account metadata to headers
    3. Include secondary account information in context

    Subclasses can override specific behaviors as needed.
    """

    # Override this in subclasses if the forward version uses a different template
    FORWARD_TEMPLATE_TAG: Optional[str] = None

    @property
    def TEMPLATE_TAG(self):
        """Use forward template if specified, otherwise use the base template."""
        return self.FORWARD_TEMPLATE_TAG or super().TEMPLATE_TAG

    @property
    def to_name(self):
        """Override to send to primary user instead of current user."""
        return self.primary_user.email

    def get_headers(self) -> Dict[str, str]:
        """
        Extend base headers with secondary account metadata.

        Returns:
            Dict[str, str]: Headers including secondary account information
        """
        headers = super().get_headers()
        headers[
            MailBotMessageHeaders.MESSAGE_CATEGORY.value
        ] = MailBotMessageCategory.FORWARD_FROM_SECONDARY_MAILBOX.value
        return headers

    def build_context(self) -> Dict[str, Any]:
        """
        Build context with secondary account information.

        Default implementation extends the base context with secondary account data.
        Subclasses can override this method entirely for specialized context building.

        Returns:
            Dict[str, Any]: Context with secondary account metadata added
        """
        context = super().build_context()
        # Add secondary account email to context so templates can reference it
        context["secondary_account_email"] = self.service.email
        return context

    def should_archive_original_message(self) -> bool:
        """
        Determine if the original email should be auto-archived after sending the overlay.

        Returns:
            bool: True if the original email should be auto-archived, False otherwise
        """
        return False


class CustomForwardContextMixin:
    """
    Mixin for forward overlays that need completely custom context building.

    This mixin provides a framework for forward overlays that can't simply extend
    the base overlay's context and need to use specialized context builders.
    """

    def build_forward_context(self) -> Dict[str, Any]:
        """
        Build context specifically for forward overlays.

        Subclasses should override this method to provide their own context building logic.

        Returns:
            Dict[str, Any]: The context dictionary for the forward overlay
        """
        raise NotImplementedError("Subclasses must implement build_forward_context()")

    def build_context(self) -> Dict[str, Any]:
        """
        Use the forward-specific context builder instead of the base one.

        Returns:
            Dict[str, Any]: The context dictionary
        """
        return self.build_forward_context()
