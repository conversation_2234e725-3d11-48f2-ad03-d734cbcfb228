"""
Handler for the "First Time Sender" overlay in MailBot.

This module defines the logic for displaying an overlay to users when they receive an email from a sender they have not previously interacted with.
The overlay is triggered based on the `first_time_sender_filter` and provides multiple options for the user to handle the new sender's email.

Key Features:
- Presents actionable options to the user for handling emails from new senders.
- Supports forwarding the overlay to the user's primary account if the user has enabled this preference.
- Updates sender metadata and domain training records after the overlay is sent.

Related:
    - `first_time_sender_filter`: Determines when this overlay should be shown.
    - `forward_first_time_sender_overlay_to_primary`: Triggered if the user opts to forward the overlay to their primary account.

This module is intended to be used as part of the MailBot overlay system for enhancing user control and security when interacting with unknown senders.
"""
import logging
from typing import Dict, Any

from django.conf import settings
from mailbot.models import DomainTraining, MailBotGenericLabel
from mailbot.utils.defaults import MailBotMessageHeaders, MailBotTemplateTag, MailBotMessageCategory, MailOperationType
from mailbot.utils.overlays.base import BaseOverlay
from mailbot.utils.jwt import jwt_encode
from django.utils import timezone

logger = logging.getLogger(__name__)


class FirstTimeSenderOverlay(BaseOverlay):
    """
    Handler for First Time Sender overlay emails.
    """

    TEMPLATE_TAG = MailBotTemplateTag.FIRST_TIME_SENDER_OVERLAY.value

    def __init__(self, *args, **kwargs):
        """
        Initialize the handler.

        Args:
            service: The email service to use
            parsed_message: The parsed email message
            db_message: Optional database message object
            **kwargs: Additional keyword arguments including:
                sender_profile: The sender profile
                options_for_overlay: Options to show in the overlay
        """
        # Extract our specific kwargs before calling parent
        self.sender_profile = kwargs.pop("sender_profile", None)
        self.options_for_overlay = kwargs.pop("options_for_overlay", None)

        # Call parent with only the expected parameters
        super().__init__(*args, **kwargs)

    @property
    def to_name(self):
        return f"{self.user.get_full_name()} <{self.user.email}>"

    @property
    def from_name(self):
        return f"{self.sender_name} (via EmailZap)"

    def build_context(self) -> Dict[str, Any]:
        """
        Get context for first time sender overlay for current message.
        Returns:
            dict: Context used to render first time sender overlay email.
        """
        token_for_user_training = jwt_encode(
            payload={
                "exp": timezone.now() + timezone.timedelta(days=1),
                "operation": MailOperationType.TRAINING_THROUGH_FIRST_TIME_SENDER.value,
                "user_mailbot_profile_id": self.user_mailbot_profile.id,
                "message_id": self.parsed_message.message_id,
                "new_label": MailBotGenericLabel.WHITE_LIST.value,
            }
        )
        token_for_domain_training = jwt_encode(
            payload={
                "exp": timezone.now() + timezone.timedelta(days=1),
                "user_mailbot_profile_id": self.user_mailbot_profile.id,
                "operation": MailOperationType.DOMAIN_TRAINING_THROUGH_FIRST_TIME_SENDER.value,
                "message_id": self.parsed_message.message_id,
                "new_label": MailBotGenericLabel.WHITE_LIST.value,
            }
        )
        token_for_enable_domain_alerts = jwt_encode(
            payload={
                "exp": timezone.now() + timezone.timedelta(days=1),
                "user_mailbot_profile_id": self.user_mailbot_profile.id,
                "operation": MailOperationType.PREFERENCES_UPDATE_FOR_FIRST_TIME_SENDER.value,
                "message_id": self.parsed_message.message_id,
                "send_alerts_for_domain": True,
            }
        )
        token_for_disable_domain_alerts = jwt_encode(
            payload={
                "exp": timezone.now() + timezone.timedelta(days=1),
                "user_mailbot_profile_id": self.user_mailbot_profile.id,
                "operation": MailOperationType.PREFERENCES_UPDATE_FOR_FIRST_TIME_SENDER.value,
                "message_id": self.parsed_message.message_id,
                "send_alerts_for_domain": False,
            }
        )
        all_recipients = self.get_recipients()
        other_recipients = ", ".join([recipient for recipient in all_recipients if recipient != self.user_email])

        return {
            "frontend_base_url": settings.FRONTEND_BASE_URL,
            "token_for_user_training": token_for_user_training,
            "token_for_domain_training": token_for_domain_training,
            "token_for_enable_domain_alerts": token_for_enable_domain_alerts,
            "token_for_disable_domain_alerts": token_for_disable_domain_alerts,
            "message_subject": self.parsed_message.subject,
            "message_body": self.parsed_message.html_body or self.parsed_message.text_body,
            "sender_email": self.sender_email,
            "sender_domain": self.sender_domain,
            "options": self.options_for_overlay,
            "backend_base_url": settings.BACKEND_BASE_URL,
            "preview_text": self.parsed_message.text_body[:150],
            "other_recipients": other_recipients,
        }

    def get_headers(self) -> Dict[str, str]:
        """
        Get the headers for the overlay email.

        Returns:
            Dict[str, str]: The headers for the email
        """
        headers = super().get_headers()
        headers[MailBotMessageHeaders.MESSAGE_CATEGORY.value] = MailBotMessageCategory.FIRST_TIME_SENDER_OVERLAY.value
        return headers

    def post_send(self) -> None:
        """
        Post-send actions after sending the overlay.

        Args:
            message_id: The ID of the sent overlay message
        """
        # Create or update domain training
        # TODO: Move this to the mail-operations API; it should be triggered when the user performs a mail operation, not during overlay sending
        DomainTraining.objects.get_or_create(
            user_mailbot_profile=self.service.user_mailbot_profile, sender_domain=self.sender_domain
        )
        # Increment overlay sent count
        self.sender_profile.metadata["fts_overlay_sent_count"] = (
            self.sender_profile.metadata.get("fts_overlay_sent_count", 0) + 1
        )
        self.sender_profile.save(update_fields=["metadata"])
        logger.info(
            f"First Time Sender Overlay sent count for sender {self.sender_domain} is {self.sender_profile.metadata['fts_overlay_sent_count']}."
        )
