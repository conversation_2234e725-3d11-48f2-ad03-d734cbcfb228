"""
Handler for forwarding inbox emails from secondary accounts to primary accounts in MailBot.

This module defines the logic for displaying an overlay when a user receives an email in their secondary account's inbox and has opted to forward such emails to their primary account.
The overlay provides relevant context and options for the forwarded message, ensuring a seamless user experience across multiple accounts.

Key Features:
- Presents an overlay to the user when forwarding emails from a secondary to a primary account.
- Sets appropriate headers to indicate the message category and the originating secondary account.
- Includes thread-aware logic to prevent duplicate overlay text in email threads.

"""
from typing import Dict, Any

from django.conf import settings
from mailbot.models import Message
from mailbot.utils.defaults import MailBotTemplateTag
from .mixins import ForwardToPrimaryMixin, CustomForwardContextMixin
from .inbox_overlay import InboxOverlay


class ForwardInboxSecondaryToPrimary(ForwardToPrimaryMixin, CustomForwardContextMixin, InboxOverlay):
    """
    Handler for forwarding inbox emails from secondary accounts to primary accounts.

    This class extends InboxOverlay using ForwardToPrimaryMixin and CustomForwardContextMixin to handle
    the specialized forwarding of inbox emails from secondary accounts to primary accounts. It includes
    custom thread-aware logic and uses a specialized template and context builder.

    Key features:
    - Uses a different template (FORWARD_INBOX_SECONDARY_TO_PRIMARY)
    - Has custom context building logic with thread awareness
    - Includes specialized headers for forwarded inbox emails
    """

    FORWARD_TEMPLATE_TAG = MailBotTemplateTag.FORWARD_INBOX_SECONDARY_TO_PRIMARY.value

    def __init__(self, *args, **kwargs):
        """
        Initialize the handler.
        """
        super().__init__(*args, **kwargs)

    def _should_display_overlay_text(self) -> bool:
        """
        If the message is a thread message, check if the first message in the thread has a forwarded overlay message id.
        If it does, do not display the overlay text.

        Returns:
            bool: True if the context is appropriate for thread messages, False otherwise
        """
        if self.parsed_message.message_id != self.parsed_message.thread_id:
            first_thread_message = (
                Message.objects.filter(message_id=self.parsed_message.thread_id).only("metadata").first()
            )
            if first_thread_message and first_thread_message.metadata.get("forwarded_overlay_message_id", None):
                return False
        return True

    def build_forward_context(self) -> Dict[str, Any]:
        """
        Build context specifically for forwarding inbox emails to primary accounts.

        Returns:
            Dict[str, Any]: The context dictionary
        """
        message_body = self.parsed_message.html_body or self.parsed_message.text_body

        # Get other recipients
        all_recipients = self.get_recipients()
        other_recipients = ", ".join([recipient for recipient in all_recipients if recipient != self.user_email])

        # Check if this is a thread message
        display_overlay_text = self._should_display_overlay_text()

        return {
            "frontend_base_url": settings.FRONTEND_BASE_URL,
            "message_subject": self.parsed_message.subject,
            "message_body": message_body,
            "sender_email": self.sender_email,
            "sender_domain": self.sender_domain,
            "secondary_account_email": self.service.email,
            "backend_base_url": settings.BACKEND_BASE_URL,
            "preview_text": self.parsed_message.text_body[:150],
            "display_overlay_text": display_overlay_text,
            "other_recipients": other_recipients,
        }


def should_archive_original_message(ref) -> bool:
    """
    Determine if the original email should be auto-archived after sending the overlay.

    Returns:
        bool: True if the original email should be auto-archived, False otherwise
    """
    return False
