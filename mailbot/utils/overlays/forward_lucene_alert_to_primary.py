"""
Handler for forwarding Lucene Alert overlays from secondary accounts to primary accounts.

This module defines the logic for forwarding Lucene Alert overlays to a user's primary account
when they receive an email on a secondary account that matches the `lucene_alert_filter`.

Key Features:
- Forwards Lucene Alert overlays from secondary email accounts to the user's primary account.
- Maintains the same overlay appearance and functionality as the standard Lucene Alert.
- Includes metadata about the secondary account in the forwarded overlay.
- Gives the primary account user full context and control over emails received by secondary accounts.

Use Cases:
- Users who want to monitor important or sensitive emails across multiple email accounts.
- Organizations that want central visibility into emails flagged by Lucene filters across various addresses.

This module is part of the multi-account management feature in MailBot, allowing users to
maintain oversight of critical communications across all their email accounts from a primary account.
"""

from mailbot.utils.overlays.mixins import ForwardToPrimaryMixin
from mailbot.utils.overlays.lucene_alert import LuceneAlert


class ForwardLuceneAlertToPrimary(ForwardToPrimaryMixin, LuceneAlert):
    """
    Handler for forwarding Lucene Alert overlays from secondary accounts to primary accounts.

    This class extends the LuceneAlert overlay using ForwardToPrimaryMixin to handle the forwarding of Lucene Alert
    overlays triggered on secondary email accounts to the user's primary account. It maintains
    the same context and appearance as regular Lucene Alerts while adding metadata about the
    secondary account from which the email originated.

    The mixin automatically handles:
    - Changing the recipient from secondary to primary user
    - Adding secondary account metadata to headers and context
    - Using the same template as the base LuceneAlert
    """

    def __init__(self, *args, **kwargs):
        """
        Initialize the handler.
        """
        super().__init__(*args, **kwargs)
