"""
Service for sending overlay emails.
"""
import logging
from typing import Optional
from mailbot.models import Message
from mailbot.service.base import BaseMessageService
from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.overlays.registry import OverlayRegistry

logger = logging.getLogger(__name__)


class OverlayService:
    """
    Service for sending overlay emails.

    This class provides a unified interface for sending overlay emails using the strategy pattern.
    Overlay handlers are discovered and loaded at Django app startup via the OverlayRegistry.
    """

    def __init__(self, service: BaseMessageService, db_message: Message, parsed_message: ParsedMessage):
        """
        Initialize the overlay service.

        Args:
            service: The email service to use
            db_message: The database message object
            parsed_message: The parsed message object
        """
        self.service = service
        self.db_message = db_message
        self.parsed_message = parsed_message

    def send_overlay(self, overlay_key: str, **kwargs) -> Optional[str]:
        """
        Send an overlay email.

        Args:
            overlay_key: The key of the overlay to send
            **kwargs: Additional arguments to pass to the overlay handler

        Returns:
            Optional[str]: The message ID of the sent email, or None if sending failed
        """
        try:
            # Get the handler class from the registry
            handler_class = OverlayRegistry.get_handler(overlay_key)
            if handler_class is None:
                logger.error(f"No overlay handler found for key: {overlay_key}")
                return None

            handler = handler_class(
                service=self.service,
                parsed_message=self.parsed_message,
                db_message=self.db_message,
                **kwargs,
            )

            logger.info(f"Sending overlay {overlay_key} for message {self.parsed_message.message_id}.")
            return handler.send()
        except Exception as e:
            logger.error(f"Failed to send overlay '{overlay_key}': {e}")
            return None
