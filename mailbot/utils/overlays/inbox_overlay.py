"""
Handler for the "Inbox Overlay" in MailBot.

This module defines the logic for displaying an overlay to users when they receive an email in their inbox and have opted to extract action items from the email.

Key Features:
- Presents an overlay to the user for actionable emails, with a label reflecting the selected action item.
- Builds a context for the overlay template, including email subject, body, sender, preview, summary, and other recipients.
- Automatically archives the original email after sending the overlay.
- We don't forward the overlay to the primary account for inbox overlay.
"""
from typing import Dict, Any

from mailbot.utils.overlays.base import BaseOverlay
from mailbot.utils.defaults import InboxOverlayLabel, MailBotMessageCategory, MailBotMessageHeaders
from mailbot.utils.email_overlay import get_inbox_overlay_instance


class InboxOverlay(BaseOverlay):
    TEMPLATE_TAG = None
    """
    Base handler for inbox overlay emails.
    """

    def __init__(self, *args, **kwargs):
        """
        Initialize the handler.

        Additional kwargs:
            overlay_label: The specific inbox overlay label (Do Now, Schedule, etc.)
        """
        self.overlay_label = kwargs.pop("overlay_label", None)
        super().__init__(*args, **kwargs)

    @property
    def to_name(self):
        return f"{self.user.get_full_name()} <{self.user.email}>"

    @property
    def from_name(self):
        return f"{self.sender_name}"

    def build_context(self) -> Dict[str, Any]:
        """
        Build the context for the inbox overlay.

        Returns:
            Dict[str, Any]: The context dictionary
        """
        # Use the existing inbox overlay context builder
        inbox_overlay = get_inbox_overlay_instance()
        inbox_overlay_result = inbox_overlay.get_result(
            subject=self.parsed_message.subject, body=self.parsed_message.text_body
        )
        context = {
            "label": inbox_overlay_result.label,
            "action": inbox_overlay_result.action,
            "summary": inbox_overlay_result.summary,
        }

        # If the context builder didn't determine a label or it doesn't match ours,
        # override it with our specific label
        if not context.get("label") or context["label"].value != self.overlay_label:
            for label in InboxOverlayLabel:
                if label.value == self.overlay_label:
                    context["label"] = label
                    break
        self.TEMPLATE_TAG = f"overlay_{context['label'].value.lower().replace(' ', '_')}"
        # Add additional context fields needed for the template
        message_body = self.parsed_message.html_body or self.parsed_message.text_body
        all_recipients = self.get_recipients()
        other_recipients = ", ".join([recipient for recipient in all_recipients if recipient != self.user_email])
        context.update(
            {
                "email_subject": self.parsed_message.subject,
                "email_body": message_body,
                "email_sender": self.sender_email,
                "preview_text": self.parsed_message.text_body[:150],
                "summary_text": context.get("summary"),
                "other_recipients": other_recipients,
            }
        )

        return context

    def get_headers(self) -> Dict[str, str]:
        """
        Get the headers for the overlay email.

        Returns:
            Dict[str, str]: The headers for the email
        """
        headers = super().get_headers()
        headers[MailBotMessageHeaders.MESSAGE_CATEGORY.value] = MailBotMessageCategory.INBOX_OVERLAY.value
        return headers

    def should_archive_original_message(self) -> bool:
        """
        Determine if the original email should be auto-archived after sending the overlay.

        Returns:
            bool: True if the original email should be auto-archived, False otherwise
        """
        return True
