"""
Base class for all overlay email handlers.
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple, Optional

from django.contrib.auth import get_user_model
from applications.models import EmailTemplate
from execfn import ApplicationTag
from mailbot.models import Message
from mailbot.service.base import BaseMessageService
from mailbot.utils.defaults import MailBotMessageHeaders
from mailbot.utils.message_parser import ParsedMessage
from applications.utils.email import get_email_domain
from mailbot.utils.profiles import get_primary_user

User = get_user_model()
logger = logging.getLogger(__name__)


class BaseOverlay(ABC):
    """
    Base class for all overlay email handlers.

    This abstract class defines the interface that all overlay handlers must implement,
    as well as common functionality shared across different overlay types.
    """

    TEMPLATE_TAG = None

    def __init__(
        self,
        service: BaseMessageService,
        parsed_message: ParsedMessage,
        db_message: Optional[Message] = None,
    ):
        """
        Initialize the overlay handler.

        Args:
            service: The email service to use
            parsed_message: The parsed email message
            application: Optional application instance
            db_message: Optional database message object
        """
        self.service = service
        self.user = service.user
        self.user_mailbot_profile = service.user_mailbot_profile
        self.parsed_message = parsed_message
        self.sender_name, self.sender_email = parsed_message.from_name_email
        self.sender_domain = get_email_domain(self.sender_email)
        self.application_tag = ApplicationTag.MailBot.value
        self.db_message = db_message
        self.user_email = self.service.email
        self.primary_user = get_primary_user(self.user.user_mailbot_profile)

    @property
    @abstractmethod
    def to_name(self):
        pass

    @property
    @abstractmethod
    def from_name(self):
        pass

    @abstractmethod
    def build_context(self) -> Dict[str, Any]:
        """
        Build the context dictionary for the overlay template.

        Returns:
            Dict[str, Any]: The context dictionary for the template
        """
        pass

    def get_headers(self) -> Dict[str, str]:
        """
        Get the headers for the overlay email.

        Returns:
            Dict[str, str]: The headers for the email
        """
        return {
            MailBotMessageHeaders.ORIGINAL_MESSAGE_ID.value: self.parsed_message.message_id,
            MailBotMessageHeaders.MAILBOT_PROFILE_ID.value: self.user_mailbot_profile.id,
            "Reply-To": self.sender_email,
        }

    def get_recipients(self):
        """
        Extracts a list of email addresses that were also recipients of the message,
        excluding the current user's email.
        Returns:
            str: A string of email addresses the message was also sent to,
                    excluding the user's own email address.
        """
        all_recipients = (
            self.parsed_message.to_name_email + self.parsed_message.cc_name_email + self.parsed_message.bcc_name_email
        )
        return [email for _, email in all_recipients]

    def get_attachments(self) -> List[Tuple[str, bytes, str]]:
        """
        Get attachments from the original email to include in the overlay.

        Returns:
            List[Tuple[str, bytes, str]]: List of attachments as (filename, content, mime_type)
        """
        return self.service.get_attachments(self.parsed_message)

    def post_send(self) -> None:
        """
        Post-send actions after sending the overlay.

        Args:
            message_id: The ID of the sent overlay message
        """
        pass

    def should_archive_original_message(self) -> bool:
        """
        Determine if the original email should be auto-archived after sending the overlay.

        Returns:
            bool: True if the original email should be auto-archived, False otherwise
        """
        return False

    def archive_original_message(self) -> bool:
        """
        Archive the original message.

        Returns:
            bool: True if archiving was successful, False otherwise
        """
        if not self.should_archive_original_message():
            return False

        try:
            result = self.service.archive_message(self.parsed_message.message_id)
            if result:
                logger.info(f"Archived original message {self.parsed_message.message_id}")
            else:
                logger.error(f"Failed to archive original message {self.parsed_message.message_id}")
            return result
        except Exception as e:
            logger.exception(f"Error archiving message {self.parsed_message.message_id}: {str(e)}")
            return False

    def send(self) -> Optional[str]:
        """
        Send the overlay email.

        This method orchestrates the entire process of sending an overlay email:
        1. Build the context
        2. Get the details of the email template
        3. Send the email
        4. Post-send actions
        6. Archive original message if needed

        Returns:
            Optional[str]: The message ID of the sent email, or None if sending failed
        """
        try:
            context = self.build_context()
            headers = self.get_headers()
            attachments = self.get_attachments()

            # Get the email template
            email_template = EmailTemplate.objects.get(application__tag=self.application_tag, tag=self.TEMPLATE_TAG)

            # Send the email
            message_id = email_template.send_email(
                user=self.user,
                context=context,
                to=self.to_name,
                from_name=self.from_name,
                headers=headers,
                attachments=attachments,
            )

            self.post_send()

            logger.info(f"Successfully sent {self.TEMPLATE_TAG} overlay for message {self.parsed_message.message_id}")
            # Archive original message if needed
            self.archive_original_message()

            return message_id

        except Exception as exc:
            logger.exception(f"Failed to send overlay", extra={"user": self.service.email, "error_details": str(exc)})
            return None
