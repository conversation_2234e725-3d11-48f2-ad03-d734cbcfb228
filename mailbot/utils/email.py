import logging
import re
from typing import Dict, Any

from django.conf import settings

from mailbot.utils.defaults import MailBotMessageHeaders
from mailbot.utils.message_parser import ParsedMessage
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)


def get_mailbot_sent_mail_headers(message_headers: Dict[str, Any]):
    """Generate extra message metadata for mails sent by MailBot.

    Args:
        message_headers (Dict[str, Any]): Message headers from API

    Returns:
        dict: Extra metadata to be saved in mailbot message
    """
    if not message_headers:
        return {}
    mailbot_message_headers = {}
    for message_header_name, message_header_value in message_headers.items():
        if mailbot_message_header := MailBotMessageHeaders._value2member_map_.get(message_header_name):  # noqa
            mailbot_message_headers[mailbot_message_header.name] = message_header_value
    return mailbot_message_headers


def has_ics_extension_attachment(parsed_message: ParsedMessage) -> bool:
    """
    Searches for a .ics file extension in email attachments.

    Returns:
        A boolean representing if the .ics extension attachment found in email's body
    """
    for attachment in parsed_message.attachments:
        if parsed_message.service_provider == settings.SERVICE_PROVIDER_GOOGLE and attachment.get(
            "filename", ""
        ).endswith(".ics"):
            return True
        if parsed_message.service_provider == settings.SERVICE_PROVIDER_MICROSOFT and attachment.name.endswith(".ics"):
            return True
    return False


def remove_quoted_text(message_text: str) -> str:
    """
    Remove the quoted text from forwards and replies in an email
    Args:
        message_text: email message

    Returns:
        cleaned text
    """
    # TODO: Find a better way to do this
    message_text = " ".join(message_text.split("\n"))
    # Convert message text to single line
    message_text = re.sub(r"On .* wrote:.*", "", message_text)
    cleaned_message = re.sub(r"^>.*", "", message_text)
    return cleaned_message.strip()


def contains_unsubscribe_link(html_content):
    soup = BeautifulSoup(html_content, "html.parser")

    # Check for links containing 'unsubscribe' in their href attribute or text
    unsubscribe_links = soup.find_all("a", href=lambda href: href and "unsubscribe" in href.lower())
    unsubscribe_text = soup.find_all("a", string=lambda text: text and "unsubscribe" in text.lower())

    return bool(unsubscribe_links or unsubscribe_text)


def get_secondary_email_label(email_address: str) -> str:
    """
    Generate a label for a secondary email account based on the email address.

    - For general domains (e.g., Gmail, Outlook), returns the username (before '@').
    - For other domains, returns the portion of the domain after '@' excluding the last segment (e.g., 'ai.stripe' from 'ai.stripe.com').
    - If the email is malformed, returns the full email address.

    Args:
        email_address (str): The email address of the secondary account.

    Returns:
        str: A simplified label derived from the email address.
    """
    try:
        username, domain = email_address.split("@", 1)
        domain = domain.lower()

        if domain in settings.GENERAL_EMAIL_DOMAINS:
            return username
        else:
            domain_parts = domain.split(".")
            if len(domain_parts) > 1:
                return ".".join(domain_parts[:-1])
            return domain
    except ValueError:
        logger.warning(f"Could not parse secondary account email: {email_address}. Using full email as label.")
        return email_address
