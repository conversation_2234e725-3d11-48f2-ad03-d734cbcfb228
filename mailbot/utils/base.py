import logging

from django.conf import settings
from django.db import IntegrityError
from django.shortcuts import get_object_or_404
from django.utils import timezone

from applications.models import ScheduledTask, Application
from applications.utils.tracking_parameters import prepare_tracking_parameters
from execfn import ApplicationTag
from execfn.settings import APP_ENV_DEV, APP_ENV_PROD
from mailbot.api.serializers import SecondaryMailbotProfilePreferencesSerializer
from mailbot.models import UserMailBotProfile, SecondaryMailBotProfilesThrough
from mailbot.signals import on_mailbot_activate, on_mailbot_deactivate
from mailbot.tasks import batch_analyze_email_message, watch_channels, unwatch_channels, on_refresh_token_renew
from mailbot.utils.check import active_subscription_exists
from mailbot.utils.defaults import (
    GMAIL_ACCESS_REVOKED_AFTER_DAYS,
    REAUTHORIZATION_REMINDER_EMAILS_SCHEDULE,
    MailBotScheduledTasks,
    MailBotProfileMetadataKey,
)
from mailbot.utils.email_scheduler import InsufficientPermissionsEmail
from applications.utils.base import batch_enumerate

logger = logging.getLogger(__name__)


def activate_mailbot(
    user_mailbot_profile: UserMailBotProfile, run_watch_channels=True, check_active_subscription_exists=True
):
    """
    Activate mailbot for the given profile

    Args :
        user_mailbot_profile : mailbot profile that need to be activated
        run_watch_channels: Whether to subscribe watch events or not
        check_active_subscription_exists: Whether to check for active subscription before activating the mailbot
    """
    if check_active_subscription_exists:
        if not active_subscription_exists(user_mailbot_profile=user_mailbot_profile):
            logger.info(
                f"Not activating mailbot for user {user_mailbot_profile.user.email} as subscription is not active"
            )
            return
    if user_mailbot_profile.preferences["mailbot_enabled"]:
        logger.info(f"Mailbot is already enabled for user {user_mailbot_profile.user.email}")
    else:
        logger.info(f"Enabling mailbot for user {user_mailbot_profile.user.email}")
        user_mailbot_profile.preferences["mailbot_enabled"] = True
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.LAST_ENABLED_AT.value] = timezone.now().isoformat()
        user_mailbot_profile.save(update_fields=["preferences", "metadata"])
        if run_watch_channels:
            watch_channels.run(user_mailbot_profile.id)
        on_mailbot_activate.send(sender=activate_mailbot, user_mailbot_profile_id=user_mailbot_profile.id)


def deactivate_mailbot(user_mailbot_profile: UserMailBotProfile):
    """
    Deactivate mailbot for the given profile

    Args :
        user_mailbot_profile : mailbot profile that need to be activated
    """
    unwatch_channels.run(user_mailbot_profile.id)
    if not user_mailbot_profile.preferences["mailbot_enabled"]:
        logger.info(f"Mailbot is already disabled for user {user_mailbot_profile.user.email}")
    else:
        logger.info(f"Disabling mailbot for user {user_mailbot_profile.user.email}")
        user_mailbot_profile.preferences["mailbot_enabled"] = False
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.LAST_DISABLED_AT.value] = timezone.now().isoformat()
        user_mailbot_profile.save(update_fields=["preferences", "metadata"])
        on_mailbot_deactivate.send(sender=deactivate_mailbot, user_mailbot_profile_id=user_mailbot_profile.id)


def check_mailbot_scopes(user, required_scopes: list, granted_scopes: list):
    """
    Checks if the given user has the required scopes for mailbot access.

    Args:
        user: user whose scopes need to be checked
        required_scopes: list of required scopes
        granted_scopes: list of granted scopes

    Returns:
        dict: information about the granted scopes
    """
    try:
        user_mailbot_profile = UserMailBotProfile.objects.get(user=user)
    except UserMailBotProfile.DoesNotExist:
        were_all_scopes_granted = False
    else:
        # TODO : Check with Ritik if default should be False here
        were_all_scopes_granted = user_mailbot_profile.metadata.get(
            MailBotProfileMetadataKey.ALL_SCOPES_GRANTED.value, True
        )
    non_granted_scopes = set(required_scopes).difference(set(granted_scopes))
    all_scopes_granted = len(non_granted_scopes) == 0
    all_scopes_granted_for_first_time = were_all_scopes_granted is False and all_scopes_granted is True
    return {
        "all_scopes_granted": all_scopes_granted,
        "non_granted_scopes": list(non_granted_scopes),
        "all_scopes_granted_for_first_time": all_scopes_granted_for_first_time,
    }


def create_and_update_scheduled_tasks(user_mailbot_profile, user):
    """
    We need to refresh user credentials every 7 days on dev and cancel all existing profile reactivation reminders
    in case they were set previously.
    """
    # Dev credentials expire in 7 days for Google, schedule reauthorization email 24h before it
    if settings.APP_ENV == APP_ENV_DEV and user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_GOOGLE:
        # Dev credentials expire in 7 days, schedule reauthorization email 24h before it
        try:
            task = ScheduledTask.objects.get(
                user=user,
                periodic_task__task=MailBotScheduledTasks.SEND_REAUTHORIZATION_DEV_MAIL.value,
                periodic_task__enabled=True,
            )
            task.reschedule_one_off_task(start_time=timezone.now() + timezone.timedelta(days=6))
        except ScheduledTask.DoesNotExist:
            ScheduledTask.create_one_off_task(
                user=user,
                task=MailBotScheduledTasks.SEND_REAUTHORIZATION_DEV_MAIL.value,
                task_args=(user_mailbot_profile.id,),
                start_time=timezone.now() + timezone.timedelta(days=6),
            )
        except ScheduledTask.MultipleObjectsReturned:
            logger.exception(
                f"Multiple reauthorization mails scheduled for user.",
                extra={"email": user.email},
            )
    elif settings.APP_ENV == APP_ENV_PROD and user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_GOOGLE:
        # Prod credentials expire in 6 months
        try:
            task = ScheduledTask.objects.get(
                user=user,
                periodic_task__task=MailBotScheduledTasks.SEND_REAUTHORIZATION_PROD_MAIL.value,
                periodic_task__enabled=True,
            )
            # First task gets spawned based on first element of the schedule setting and
            # further tasks are spawned based on the other elements of the schedule setting
            task.reschedule_one_off_task(
                start_time=timezone.now()
                + timezone.timedelta(days=GMAIL_ACCESS_REVOKED_AFTER_DAYS - REAUTHORIZATION_REMINDER_EMAILS_SCHEDULE[1])
            )
        except ScheduledTask.DoesNotExist:
            ScheduledTask.create_one_off_task(
                user=user,
                task=MailBotScheduledTasks.SEND_REAUTHORIZATION_PROD_MAIL.value,
                task_args=(user_mailbot_profile.id,),
                start_time=timezone.now()
                + timezone.timedelta(
                    days=GMAIL_ACCESS_REVOKED_AFTER_DAYS - REAUTHORIZATION_REMINDER_EMAILS_SCHEDULE[1]
                ),
            )
        except ScheduledTask.MultipleObjectsReturned:
            logger.exception(
                f"Multiple reauthorization mails scheduled for user.",
                extra={"email": user.email},
            )
    # Cancel reactivate mailbot reminder emails if scheduled
    try:
        task = ScheduledTask.objects.get(
            user=user,
            periodic_task__task=MailBotScheduledTasks.SEND_REACTIVATE_REMINDER_MAIL,
            periodic_task__enabled=True,
        )
        task.disable()
        logger.info(f"Cancelled reactivation reminder email for user {user.email}")
    except ScheduledTask.DoesNotExist:
        pass
    except ScheduledTask.MultipleObjectsReturned:
        logger.exception(
            f"Multiple reactivation reminder emails scheduled for user.",
            extra={"email": user.email},
        )
    # Schedule insufficient permissions email
    InsufficientPermissionsEmail(user_mailbot_profile=user_mailbot_profile).schedule_email()


def set_tracking_parameters(user_mailbot_profile, new_parameters):
    """Sets tracking parameters in the metadata section of mailbot profile after some cleanup.

    Args:
        user_mailbot_profile: User Mailbot Profile
        new_parameters: New parameters dictionary
    """
    old_parameters = user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.TRACKING_PARAMETERS.value, {})
    tracking_parameters = prepare_tracking_parameters(old_parameters, new_parameters)
    user_mailbot_profile.metadata[MailBotProfileMetadataKey.TRACKING_PARAMETERS.value] = tracking_parameters
    user_mailbot_profile.save(update_fields=["metadata"])


def pre_login(user_mailbot_profile, mailbot_profile_created, state_information):
    """
    Before logging in the user, if new profile has been created
    1. we need to associate user to primary profile
    2. create default subscription
    3. initialize onboarding tasks

    else, if the profile already exists and refresh token had expired previously
    1. we need to re-enable mailbot
    2. re-subscribe to watch gmail events
    """
    metadata = state_information.get("metadata", {})
    preferences = {}
    if metadata.get("forwarding_policy"):
        preferences["forwarding_policy"] = metadata.get("forwarding_policy")
    if metadata.get("digest_frequency"):
        preferences["digest_frequency"] = metadata.get("digest_frequency")

    if mailbot_profile_created:
        # Link with primary profile if provided in the state
        if primary_mailbot_profile_id := metadata.get("primary_mailbot_profile_id"):
            # count of secondary profiles + primary profile
            linked_profile_count = (
                SecondaryMailBotProfilesThrough.objects.filter(
                    primary_mailbot_profile_id=primary_mailbot_profile_id
                ).count()
                + 1
            )
            linked_profile_max_count = Application.objects.get(
                tag=ApplicationTag.MailBot.value
            ).linked_accounts_max_count
            if linked_profile_count < linked_profile_max_count:
                try:
                    SecondaryMailBotProfilesThrough.objects.get_or_create(
                        primary_mailbot_profile_id=primary_mailbot_profile_id,
                        secondary_mailbot_profile_id=user_mailbot_profile.id,
                    )
                    instance = get_object_or_404(UserMailBotProfile, id=primary_mailbot_profile_id)
                    serializer = SecondaryMailbotProfilePreferencesSerializer(data=preferences, partial=True)
                    serializer.is_valid(raise_exception=True)
                    instance.secondary_profile_preferences.update(serializer.validated_data)
                    instance.save(update_fields=["secondary_profile_preferences"])
                except IntegrityError:
                    logger.exception("Invalid primary mailbot profile mapping")
            else:
                raise ValueError("Max linked profile limit exceeded")
    if not mailbot_profile_created and user_mailbot_profile.metadata.get(
        MailBotProfileMetadataKey.REFRESH_TOKEN_EXPIRED.value
    ):
        # User mailbot existed and user re-authorized after refresh token expired
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.REFRESH_TOKEN_EXPIRED.value] = False
        user_mailbot_profile.save(update_fields=["metadata"])
        on_refresh_token_renew.run(user_mailbot_profile.id)
        activate_mailbot(user_mailbot_profile)
    if new_parameters := metadata.get("tracking_parameters"):
        set_tracking_parameters(user_mailbot_profile, new_parameters)
    if stripe_payment_plan := metadata.get("stripe_payment_plan"):
        user_mailbot_profile.refresh_from_db()
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.STRIPE_PAYMENT_PLAN.value] = stripe_payment_plan
        user_mailbot_profile.save()


def analyze_messages_in_chunks(
    profile_id,
    message_ids,
):
    """
    Analyze message IDs in chunks by dispatching a background task for each chunk.

    Args:
        message_ids (list): List of message IDs to process.
        profile_id (int): ID of the profile to pass to the task.
    """
    chunk_size = 2000
    for batch_num, chunk_slice in batch_enumerate(len(message_ids), chunk_size):
        chunk = message_ids[chunk_slice]
        try:
            batch_analyze_email_message.delay(profile_id, chunk)
            logger.info(f"Scheduled batch_analyze_email_message for chunk {batch_num + 1} with {len(chunk)} messages")
        except Exception as e:
            logger.exception(
                f"Error scheduling batch_analyze_email_message for profile_id: {profile_id} and chunk {batch_num + 1}: {str(e)}"
            )
