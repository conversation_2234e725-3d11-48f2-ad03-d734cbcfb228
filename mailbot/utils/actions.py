from abc import ABC, abstractmethod
from django.contrib.auth import get_user_model
from mailbot.models import (
    SenderProfile,
    SenderUnsubscribeDetail,
)
from mailbot.tasks import unsubscribe_sender
from mailbot.utils.statistics import update_statistics_in_analytics
from mailbot.service.factory import MessageServiceFactory
from mailbot.utils.defaults import AnalyticsStatisticsKey, ActionTypes
from mailbot.models import UserActionLog

import logging

logger = logging.getLogger(__name__)


User = get_user_model()


class BaseAction(ABC):

    @abstractmethod
    def execute(self, data) -> dict:
        """
        Execute the action for a single sender profile.
        """
        pass

    @abstractmethod
    def bulk_execute(self, data) -> dict:
        """
        Execute the action for multiple sender profiles.
        # TODO: Add a limit of <NUMBER_OF_SENDER_PROFILES> a user can bulk <action_type> at a time.
        """
        pass

    def set_future_action(self, user_action, user_action_reason, sender_profile: SenderProfile):
        if user_action == "algo_decide":
            sender_profile.user_action = None
        else:
            sender_profile.user_action = user_action
        sender_profile.save(update_fields=["user_action"])

        UserActionLog.objects.create(
            user_mailbot_profile=sender_profile.user_mailbot_profile,
            sender_profile=sender_profile,
            user_action=user_action,
            user_action_reason=user_action_reason,
        )


class DeleteAction(BaseAction):

    def execute(self, sender_profile: SenderProfile) -> dict:
        if not sender_profile.metadata.get("deleted"):
            sender_profile.metadata["deleted"] = True
            sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_MARKED_DELETED
            message_service = MessageServiceFactory.get_message_service(sender_profile.user_mailbot_profile)
            messages_trashed = message_service.trash_historical_messages(sender_profile.sender_email)

            update_statistics_in_analytics(
                user_mailbot_profile=sender_profile.user_mailbot_profile,
                key=AnalyticsStatisticsKey.TRASHED_COUNT.value,
                value=messages_trashed,
            )

            sender_profile.save(update_fields=["user_action_reason", "metadata"])

            return {
                "status": "success",
                "message": "Messages deleted successfully",
                "trashed_count": messages_trashed,
                "sender_profile_id": sender_profile.id,
            }
        else:
            return {
                "status": "success",
                "message": "Future preference for delete action updated successfully",
                "sender_profile_id": sender_profile.id,
            }

    def bulk_execute(self, data) -> dict:
        try:
            sender_profile_ids = data.get("sender_profile_ids", [])
            user_action = data.get("user_action")

            sender_profiles = SenderProfile.objects.select_related("user_mailbot_profile__user").filter(
                id__in=sender_profile_ids
            )
            for sender_profile in sender_profiles:
                self.execute(sender_profile)

            if user_action:
                for sender_profile in sender_profiles:
                    self.set_future_action(user_action, SenderProfile.USER_ACTION_REASON_MARKED_DELETED, sender_profile)

            return {
                "overall_status": "completed",
            }
        except Exception as e:
            logger.error(f"An unexpected error occurred while processing bulk delete action: {e}", exc_info=True)
            raise e


class UnsubscribeAction(BaseAction):

    def execute(self, sender_profile: SenderProfile) -> dict:
        is_unsubscribed: bool = SenderUnsubscribeDetail.objects.filter(
            sender_profile=sender_profile, unsubscribed=True
        ).exists()
        if not is_unsubscribed:
            unsubscribe_sender.delay(sender_profile.id)
            sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_UNSUBSCRIBED
            update_statistics_in_analytics(
                user_mailbot_profile=sender_profile.user_mailbot_profile,
                key=AnalyticsStatisticsKey.SENDERS_UNSUBSCRIBED.value,
                value=1,
            )
            sender_profile.save(update_fields=["user_action_reason"])

    def bulk_execute(self, data) -> dict:
        try:
            sender_profile_ids = data.get("sender_profile_ids", [])
            user_action = data.get("user_action")

            sender_profiles = SenderProfile.objects.select_related("user_mailbot_profile__user").filter(
                id__in=sender_profile_ids
            )
            for sender_profile in sender_profiles:
                self.execute(sender_profile)

            if user_action:
                for sender_profile in sender_profiles:
                    self.set_future_action(user_action, SenderProfile.USER_ACTION_REASON_UNSUBSCRIBED, sender_profile)
            return {
                "overall_status": "completed",
            }
        except Exception as e:
            logger.error(f"An unexpected error occurred while processing bulk unsubscribe action: {e}", exc_info=True)
            raise e


class ManageSenderAction(BaseAction):

    def execute(self, data) -> dict:
        pass

    def bulk_execute(self, data) -> dict:
        try:
            sender_profile_ids = data.get("sender_profile_ids", [])
            user_action = data.get("user_action")
            sender_profiles = SenderProfile.objects.select_related("user_mailbot_profile__user").filter(
                id__in=sender_profile_ids
            )
            for sender_profile in sender_profiles:
                self.set_future_action(user_action, SenderProfile.USER_ACTION_REASON_MANAGE_SENDER, sender_profile)
                sender_profile.user_action_reason = SenderProfile.USER_ACTION_REASON_MANAGE_SENDER
                sender_profile.save(update_fields=["user_action_reason"])

            return {
                "overall_status": "completed",
            }
        except Exception as e:
            logger.error(f"An unexpected error occurred while processing bulk manage sender action: {e}", exc_info=True)
            raise e


class ActionProcessor:
    ACTION_MAP = {
        ActionTypes.DELETE.value: DeleteAction,
        ActionTypes.UNSUBSCRIBE.value: UnsubscribeAction,
        ActionTypes.MANAGE_SENDER.value: ManageSenderAction,
    }

    def _load_action(self, action_type: str) -> BaseAction:
        action_class = self.ACTION_MAP.get(action_type)
        if action_class:
            return action_class()
        raise ValueError(f"Unsupported action_type: {action_type}")

    def process(self, data: dict, user) -> dict:
        try:
            action_instance = self._load_action(data.get("action_type"))
            logger.info(f"Processing action: {data.get('action_type')}, user: {user.email}")

            result = action_instance.bulk_execute(data.get("details"))

            logger.info(f"Action {data.get('action_type')} executed successfully.")
            return result

        except Exception as e:
            logger.error(f"Unexpected error during action {data.get('action_type')}: {e}", exc_info=True)
            raise e
