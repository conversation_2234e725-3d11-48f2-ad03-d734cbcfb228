import logging

from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from execfn import ApplicationTag
from jarvis.tasks import queue_jarvis_workflow_for_execution
from mailbot.models import UserMailBotProfile, Message

logger = logging.getLogger(__name__)


def execute_new_email_jarvis_workflows(user_mailbot_profile: UserMailBotProfile, db_message: Message):
    """
    Execute new-email event driven Jarvis Workflows
    Args:
        user_mailbot_profile: user's mailbot profile
        db_message: new email message

    Returns:
        if any jarvis workflow was executed
    """
    jarvis_enabled = check_feature(
        user_id=user_mailbot_profile.user_id,
        feature_flag=MailBotFeatureFlag.JARVIS,
        application_tag=ApplicationTag.MailBot,
    )
    jarvis_workflows_executed = False
    user = user_mailbot_profile.user
    if jarvis_enabled:
        logger.info(f"Processing jarvis workflows.")
        jarvis_workflows_executed = queue_jarvis_workflow_for_execution(
            user=user,
            payload=db_message,  # noqa
            event_tag="new_email",
            _async=False,
        )
    return jarvis_workflows_executed
