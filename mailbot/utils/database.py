import logging
from typing import Optional

from django.conf import settings
from django.db import transaction
from django.utils import timezone

from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from applications.utils.email import get_email_domain, get_normalized_email
from execfn import ApplicationTag
from mailbot.models import SenderProfile, Message, SenderUnsubscribeDetail
from mailbot.utils.alert import check_exception_list_trained
from mailbot.utils.exceptions import SyncMessageWithDatabaseError
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


def sync_unsubscribe_detail(parsed_message: ParsedMessage, sender_profile: SenderProfile):
    """
    Sync unsubscribe details for message with sender profile. This is done only for non-general and non-sent messages.

    Args:
        parsed_message (ParsedMessage): Parsed message object.
        sender_profile (SenderProfile): Sender profile object.
    """
    if check_feature(
        user_id=parsed_message.user_id,
        feature_flag=MailBotFeatureFlag.UNSUBSCRIBE,
        application_tag=ApplicationTag.MailBot,
    ):
        try:
            sender_domain = get_email_domain(email=sender_profile.sender_email)
            if (
                not parsed_message.is_sent
                and sender_domain not in settings.GENERAL_EMAIL_DOMAINS
                and (parsed_message.unsubscribe_link or parsed_message.unsubscribe_link_mail_to)
            ):
                SenderUnsubscribeDetail.objects.create(
                    user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
                    message_id=parsed_message.message_id,
                    sender_profile=sender_profile,
                    unsubscribe_mail_to=parsed_message.unsubscribe_link_mail_to,
                    unsubscribe_link=parsed_message.unsubscribe_link,
                    unsubscribe_link_one_click=parsed_message.unsubscribe_link_one_click,
                    metadata={"unsubscribe_link_mail_to_parsed": parsed_message.unsubscribe_link_mail_to_parsed},
                )
        except Exception:
            logger.exception(
                "Failed to sync unsubscribe details for message",
                extra={"message_id": parsed_message.message_id, "sender_email": sender_profile.sender_email},
            )


def sync_single_sender_profile_for_new_message(
    sender_name,
    sender_email,
    is_sender_stats_enabled,
    parsed_message: ParsedMessage,
):
    """
    Synchronise sender profile with database for single sender.

    Args:
        sender_name (str): Sender's name
        sender_email (str): Sender's email
        is_sender_stats_enabled (bool): Whether we should update total count and read fraction.
        parsed_message (ParsedMessage): Parsed message object.
    """
    if len(sender_name) > 128:
        logger.info("Truncating sender name due to max length 128", extra={"sender_name": sender_name})
        sender_name = sender_name[:128]
    normalized_sender_email = get_normalized_email(sender_email)
    with transaction.atomic():
        sender_profile, created = SenderProfile.objects.select_for_update(of=("self",)).get_or_create(
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
            normalized_sender_email=normalized_sender_email,
            defaults={
                "sender_name": sender_name,
                "sender_email": sender_email,
                "total_count": int(is_sender_stats_enabled),
                "sent_count": int(parsed_message.is_sent),
                "read_count": int(parsed_message.is_read) if is_sender_stats_enabled else 0,
                "oldest_timestamp": parsed_message.received_at,
                "recent_timestamp": parsed_message.received_at,
                "scanned_count": 1,
            },
        )
        if not created:
            sender_profile.scanned_count += 1
            if is_sender_stats_enabled:
                sender_profile.total_count += 1
                logger.info(f"Total count increased for message {parsed_message.message_id}, sender {sender_email}")
                if sender_profile.oldest_timestamp > parsed_message.received_at:
                    sender_profile.oldest_timestamp = parsed_message.received_at
                if sender_profile.recent_timestamp < parsed_message.received_at:
                    sender_profile.recent_timestamp = parsed_message.received_at
                if parsed_message.is_read:
                    logger.info(f"Read count increased for message {parsed_message.message_id}, sender {sender_email}")
                    sender_profile.read_count += 1
                if parsed_message.is_sent:
                    logger.info(f"Sent count increased for message {parsed_message.message_id}, sender {sender_email}")
                    sender_profile.sent_count += 1
            sender_profile.save()
        transaction.on_commit(lambda: sync_unsubscribe_detail(parsed_message, sender_profile))

    return sender_profile


def sync_single_sender_profile_for_existing_message(
    sender_email,
    parsed_message: ParsedMessage,
    was_read: bool,
    was_sender_stats_enabled: bool,
    is_sender_stats_enabled: bool,
):
    """
    Synchronise sender profile with database for single sender for existing message.

    Args:
        sender_email (str): Sender's email
        parsed_message (ParsedMessage): Parsed message object.
        was_read (bool): Whether old message is in read or unread status.
        was_sender_stats_enabled (bool): Whether old label was considered for sender stats.
        is_sender_stats_enabled (bool): Whether new label is considered for sender stats.
    """
    normalized_sender_email = get_normalized_email(sender_email)
    with transaction.atomic():
        sender_profile = SenderProfile.objects.select_for_update(of=("self",)).get(
            user_mailbot_profile_id=parsed_message.user_mailbot_profile_id,
            normalized_sender_email=normalized_sender_email,
        )
        if is_sender_stats_enabled != was_sender_stats_enabled:
            if was_sender_stats_enabled:
                logger.info(f"Total count decreased for message {parsed_message.message_id}, sender {sender_email}")
                sender_profile.total_count -= 1
                if was_read:
                    logger.info(f"Read count decreased for message {parsed_message.message_id}, sender {sender_email}")
                    sender_profile.read_count -= 1
                sender_profile.save()
            else:
                logger.info(f"Total count increased for message {parsed_message.message_id}, sender {sender_email}")
                sender_profile.total_count += 1
                if parsed_message.is_read:
                    logger.info(f"Read count increased for message {parsed_message.message_id}, sender {sender_email}")
                    sender_profile.read_count += 1
                sender_profile.save()
        elif was_read != parsed_message.is_read and is_sender_stats_enabled:
            # Read change within the sender stat enabled label
            if parsed_message.is_read:
                logger.info(f"Read count increased for message {parsed_message.message_id}, sender {sender_email}")
                sender_profile.read_count += 1
            else:
                logger.info(f"Read count decreased for message {parsed_message.message_id}, sender {sender_email}")
                sender_profile.read_count -= 1
            sender_profile.save()


def sync_sender_profile_for_new_message(
    is_sender_stats_enabled, parsed_message: ParsedMessage
) -> Optional[SenderProfile]:
    """
    Synchronise sender profile with database for single message created in user's mailbox.
    Args:
        is_sender_stats_enabled (bool): Whether we should update total count and read fraction.
        parsed_message (ParsedMessage): Parsed message object.
    """
    if parsed_message.is_sent:
        for receiver in parsed_message.to_name_email + parsed_message.cc_name_email + parsed_message.bcc_name_email:
            sender_name, sender_email = receiver
            logger.info(f"{parsed_message.message_id}: Syncing sender profile for sent email, {sender_email}")
            try:
                sync_single_sender_profile_for_new_message(
                    sender_name=sender_name,
                    sender_email=sender_email,
                    is_sender_stats_enabled=is_sender_stats_enabled,
                    parsed_message=parsed_message,
                )
            except Exception:
                raise SyncMessageWithDatabaseError(
                    message_id=parsed_message.message_id,
                    reason="Sync sender profile failed for new message",
                )
    else:
        logger.info(
            f"{parsed_message.message_id}: Syncing sender profile for received email {parsed_message.from_name_email[1]}"
        )
        try:
            return sync_single_sender_profile_for_new_message(
                sender_name=parsed_message.from_name_email[0],
                sender_email=parsed_message.from_name_email[1],
                is_sender_stats_enabled=is_sender_stats_enabled,
                parsed_message=parsed_message,
            )
        except Exception:
            raise SyncMessageWithDatabaseError(
                message_id=parsed_message.message_id,
                reason="Sync sender profile failed for new message",
            )


def sync_sender_profile_for_existing_message(
    parsed_message: ParsedMessage,
    was_read: bool,
    was_sender_stats_enabled: bool,
    is_sender_stats_enabled: bool,
):
    """
    Synchronise sender profile for existing message if label or read status changed.
    Args:
        parsed_message: Parsed message object.
        was_read: Whether old message is in read or unread status.
        was_sender_stats_enabled: Whether old label was considered for sender stats.
        is_sender_stats_enabled: Whether new label is considered for sender stats.
    """
    # Update sender profile only if old and new label's stats_enabled_status does not match
    if parsed_message.is_sent:
        for receiver in parsed_message.to_name_email + parsed_message.cc_name_email + parsed_message.bcc_name_email:
            _, sender_email = receiver
            logger.info(f"{parsed_message.message_id}: Syncing sender profile for sent existing email, {sender_email}")
            try:
                sync_single_sender_profile_for_existing_message(
                    sender_email=sender_email,
                    parsed_message=parsed_message,
                    is_sender_stats_enabled=is_sender_stats_enabled,
                    was_sender_stats_enabled=was_sender_stats_enabled,
                    was_read=was_read,
                )
            except Exception:
                raise SyncMessageWithDatabaseError(
                    message_id=parsed_message.message_id,
                    reason="Sync sender profile failed for existing message",
                )
    else:
        logger.info(
            f"{parsed_message.message_id}: Syncing sender profile for received existing email, {parsed_message.from_name_email[1]}"
        )
        try:
            sync_single_sender_profile_for_existing_message(
                sender_email=parsed_message.from_name_email[1],
                parsed_message=parsed_message,
                is_sender_stats_enabled=is_sender_stats_enabled,
                was_sender_stats_enabled=was_sender_stats_enabled,
                was_read=was_read,
            )
        except:
            raise SyncMessageWithDatabaseError(
                message_id=parsed_message.message_id,
                reason="Sync sender profile failed for existing message",
            )


def update_user_training(user_mailbot_profile_id, sender_email, user_training):
    """
    Update user's training label and training time for sender's email to current time in database.
    Args:
        user_mailbot_profile_id: UserMailBotProfile ID
        sender_email: Sender's email
        user_training: Training generic label
    """
    normalized_sender_email = get_normalized_email(sender_email)
    with transaction.atomic():
        sender_profile = SenderProfile.objects.select_for_update(of=("self",)).get(
            user_mailbot_profile_id=user_mailbot_profile_id, normalized_sender_email=normalized_sender_email
        )
        if sender_profile.user_training == user_training:
            return
        sender_profile.user_training = user_training
        sender_profile.user_trained_at = timezone.now()
        sender_profile.save(update_fields=["user_training", "user_trained_at"])
        transaction.on_commit(lambda: check_exception_list_trained(sender_profile, user_training))


def update_label(user_mailbot_profile_id, message_id, generic_label, label_ids=None):
    """
    Update labels and generic label of message.
    Args:
        user_mailbot_profile_id: UserMailBotProfile ID
        message_id: ID of message
        generic_label: New generic label for current message
        label_ids: New label IDs for current message
    """
    with transaction.atomic():
        message = Message.objects.select_for_update(of=("self",)).get(
            user_mailbot_profile_id=user_mailbot_profile_id, message_id=message_id
        )
        message.generic_label = generic_label
        if label_ids:
            message.metadata["label_ids"] = label_ids
        message.save(update_fields=["generic_label", "metadata"])
