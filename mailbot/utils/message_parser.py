"""Contains functions for parser responses from GMail APIs"""

import datetime
import logging
import re
from dataclasses import dataclass
from email.header import decode_header
from email.utils import parseaddr, getaddresses
from typing import Any, Dict, List, Tuple
from urllib.parse import parse_qs as parse_url_query_string, urlparse

from O365.message import Message as OutlookMessage
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.utils import timezone

from applications.utils.email import index_headers, url_b64_decode, html_to_text, get_normalized_email
from execfn.common.utils.search import isearch_key
from mailbot.utils.defaults import (
    GmailKnownLabelName,
    OutlookKnownLabelName,
    MICROSOFT_EXTENDED_PROPERTY_ID,
    MICROSOFT_EXTENDED_PROPERTY_NAME,
)

logger = logging.getLogger(__name__)


@dataclass
class ParsedMessage:
    message_id: str
    user_mailbot_profile_id: int
    user_id: int
    service_provider: str
    user_email: str
    thread_id: str
    label_ids: List[str]
    body_preview: str
    is_read: bool
    is_sent: bool
    received_at: datetime.datetime
    message_headers: Dict[str, str]
    subject: str
    attachments: list
    from_name_email: Tuple[str, str]
    to_name_email: List[Tuple[str, str]]
    cc_name_email: List[Tuple[str, str]]
    bcc_name_email: List[Tuple[str, str]]
    html_body: str
    text_body: str
    unsubscribe_link: str
    unsubscribe_link_mail_to: str
    unsubscribe_link_one_click: bool
    unsubscribe_link_mail_to_parsed: Tuple[str, str, str]


@dataclass
class ParsedGmailMessage(ParsedMessage):
    history_id: str


@dataclass
class ParsedOutlookMessage(ParsedMessage):
    single_value_extended_property__bot_assigned_label: str


def parse_gmail_payload(headers, payload: Dict[str, Any]):
    result = {}
    if not payload:
        return result
    parts = [payload]
    first_part_processed = False

    while parts:
        part = parts.pop(0)
        if "parts" in part:
            parts.extend(part["parts"])
        if first_part_processed:
            headers = index_headers(part.get("headers"))

        if "body" not in part:
            continue

        is_html = part.get("mimeType", "").startswith("text/html")
        is_plain = part.get("mimeType", "").startswith("text/plain")
        is_attachment = "attachmentId" in part["body"] or (
            "content-disposition" in headers and headers["content-disposition"].lower().startswith("attachment")
        )
        is_inline = "content-disposition" in headers and headers["content-disposition"].lower().startswith("inline")

        if is_html and not is_attachment:
            result["body_html"] = url_b64_decode(part["body"]["data"])
        elif is_plain and not is_attachment:
            result["body_text"] = url_b64_decode(part["body"]["data"])
        elif is_attachment:
            body = part["body"]
            attachment = {
                "filename": part["filename"],
                "mimeType": part["mimeType"],
                "size": body["size"],
                "attachmentId": body.get("attachmentId"),
                "headers": index_headers(part.get("headers")),
            }
            if "attachments" not in result:
                result["attachments"] = []
            result["attachments"].append(attachment)
        elif is_inline:
            body = part["body"]
            inline = {
                "filename": part["filename"],
                "mimeType": part["mimeType"],
                "size": body["size"],
                "attachmentId": body.get("attachmentId"),
                "headers": index_headers(part.get("headers")),
            }
            if "inline" not in result:
                result["inline"] = []
            result["inline"].append(inline)

        first_part_processed = True

    return result


def validate_parsed_name_email(name_email: Tuple[str, str]) -> bool:
    """
    Filter out wrongly parsed name and email pair from message headers.

    Args:
        name_email (Tuple[str, str]): Name and email pair to filter

    Returns:
        bool: True if name_email is valid, false otherwise

    Raises:
        ValidationError: If email is not a valid email regex
    """
    if not name_email:
        return False
    if len(name_email) == 2:
        if not (email := name_email[1]) or "@" not in email:
            return False
        # Only pass on email to `normalize_email` if "@" symbol is present in email
        normalized_email = get_normalized_email(email)
        # Validation may fail on un-normalized email and pass on normalized email, but vice-versa cannot happen
        validate_email(normalized_email)
        return True
    else:
        return False


def get_recipients(user_email, message_id, message_headers: dict, recipient_type: str) -> List[Tuple[str, str]]:
    """
    Get recipients from message headers for the specified recipient type (To, CC, Bcc)
    Args:
        user_email: User's email address
        message_id: Message ID
        message_headers: Message headers
        recipient_type: Recipient type

    Returns:
        List[Tuple[str, str]]: List of valid recipients' name and email
    """
    recipients = isearch_key(message_headers, recipient_type)
    if not recipients:
        return []
    verified_recipients = []
    for parsed_recipient_name_email in getaddresses([recipients]):
        try:
            is_valid_email = validate_parsed_name_email(parsed_recipient_name_email)
        except ValidationError:
            logger.info(
                f"Invalid email format for recipient {parsed_recipient_name_email}, message {message_id}, user email {user_email}"
            )
        else:
            if is_valid_email:
                verified_recipients.append(parsed_recipient_name_email)
    return verified_recipients


MAIL_TO_PATTERN = r"(mailto:[^>]+)"
HTTPS_PATTERN = r"(https?:\/\/[^>]+)"


def decode_unsubscribe_header(encoded_header):
    """
    Decode the List-Unsubscribe header value.
    The header value is a string that MAY contain encoded words.

    Args:
        encoded_header (str): The encoded header value

    Returns:
        str: The decoded header value
    """
    # Decode the header
    decoded_parts = decode_header(encoded_header)
    # Join the decoded parts into a single string
    decoded_string = "".join(
        part.decode(encoding or "utf-8") if isinstance(part, bytes) else part for part, encoding in decoded_parts
    )
    return decoded_string


def parse_unusubscribe_mail_to(unsubscribe_mail_to: str) -> Tuple[str, str, str]:
    parsed_url = urlparse(unsubscribe_mail_to, scheme="mailto")
    parsed_query = parse_url_query_string(parsed_url.query)
    subject = isearch_key(parsed_query, "subject", ["unsubscribe"])[0]
    text_body = isearch_key(parsed_query, "body", ["unsubscribe"])[0]
    return parsed_url.path, subject, text_body


def get_unsubscribe_data(message_headers: dict) -> dict:
    """
    Extract unsubscribe related data from message headers
    """
    result = {
        "unsubscribe_link": None,
        "unsubscribe_link_mail_to": None,
        "unsubscribe_link_one_click": False,
        "unsubscribe_link_mail_to_parsed": None,
    }
    if not (list_unsubscribe_header_value := isearch_key(message_headers, "List-Unsubscribe")):
        return result
    list_unsubscribe_post_header_value = isearch_key(message_headers, "List-Unsubscribe-Post")
    list_unsubscribe_header_value = decode_unsubscribe_header(list_unsubscribe_header_value)
    https_unsubscribe_link = re.search(HTTPS_PATTERN, list_unsubscribe_header_value)
    mail_to_unsubscribe_uri = re.search(MAIL_TO_PATTERN, list_unsubscribe_header_value)
    if https_unsubscribe_link:
        result["unsubscribe_link"] = https_unsubscribe_link.group(1)
    if mail_to_unsubscribe_uri:
        result["unsubscribe_link_mail_to"] = mail_to_unsubscribe_uri.group(1)
        result["unsubscribe_link_mail_to_parsed"] = parse_unusubscribe_mail_to(result["unsubscribe_link_mail_to"])
    if list_unsubscribe_post_header_value:
        if list_unsubscribe_post_header_value == "List-Unsubscribe=One-Click":
            result["unsubscribe_link_one_click"] = True
        else:
            logger.info(f"List Unsubscribe Post Header value {list_unsubscribe_post_header_value} is not One-Click")
    return result


def parse_gmail_message(user_mailbot_profile, message: Dict[str, Any]) -> ParsedGmailMessage:
    message_id = message.get("id")
    user_email = user_mailbot_profile.user.email
    # Parse received datetime
    internal_date = message.get("internalDate")
    if internal_date:
        received_at = datetime.datetime.fromtimestamp(int(internal_date) / 1000, tz=datetime.timezone.utc)
    else:
        received_at = timezone.now()
    payload = message.get("payload", {})
    # Index message headers
    message_headers = index_headers(payload.get("headers"))

    parsed_gmail_payload = parse_gmail_payload(message_headers, payload)

    # Parse from, to, cc, bcc
    from_header = isearch_key(message_headers, "From")
    from_name_address = parseaddr(from_header)
    validation_error_message = {
        "recipient_type": "From",
        "user_email": user_email,
        "message_id": message_id,
        "recipients": from_header,
        "parsed_recipient_name_email": from_name_address,
    }
    if not validate_parsed_name_email(from_name_address):
        raise ValidationError(message=validation_error_message)
    to = get_recipients(user_email, message_id, message_headers, "To")
    cc = get_recipients(user_email, message_id, message_headers, "Cc")
    bcc = get_recipients(user_email, message_id, message_headers, "Bcc")

    # Message body
    html_body = parsed_gmail_payload.get("body_html", "")
    text_body = parsed_gmail_payload.get("body_text", "")
    if not text_body and html_body:
        text_body = html_to_text(html_body)

    # Unsubscribe data
    unsubscribe_data = get_unsubscribe_data(message_headers)

    parsed_message = ParsedGmailMessage(
        message_id=message_id,
        user_mailbot_profile_id=user_mailbot_profile.id,
        service_provider=settings.SERVICE_PROVIDER_GOOGLE,
        user_email=user_email,
        user_id=user_mailbot_profile.user_id,
        thread_id=message.get("threadId"),
        label_ids=message.get("labelIds", []),
        body_preview=message.get("snippet"),
        history_id=message.get("historyId"),
        received_at=received_at,
        is_read=GmailKnownLabelName.UNREAD.value not in message.get("labelIds", []),
        is_sent=GmailKnownLabelName.SENT.value in message.get("labelIds", []),
        subject=isearch_key(message_headers, "Subject") or "",
        message_headers=message_headers,
        attachments=parsed_gmail_payload.get("attachments", []),
        html_body=html_body,
        text_body=text_body,
        from_name_email=from_name_address,
        to_name_email=to,
        cc_name_email=cc,
        bcc_name_email=bcc,
        unsubscribe_link=unsubscribe_data["unsubscribe_link"],
        unsubscribe_link_mail_to=unsubscribe_data["unsubscribe_link_mail_to"],
        unsubscribe_link_one_click=unsubscribe_data["unsubscribe_link_one_click"],
        unsubscribe_link_mail_to_parsed=unsubscribe_data["unsubscribe_link_mail_to_parsed"],
    )
    return parsed_message


def parse_outlook_message(user_mailbot_profile, message: OutlookMessage) -> ParsedOutlookMessage:
    folder_name = user_mailbot_profile.label_mappings.get(message.folder_id)
    name_email_mapper = lambda x: (x.name, x.address)
    bot_assigned_label = None
    for single_value_extended_property in message.single_value_extended_properties:
        if (
            single_value_extended_property.get("id")
            == f"String {{{MICROSOFT_EXTENDED_PROPERTY_ID}}} Name {MICROSOFT_EXTENDED_PROPERTY_NAME}"
        ):
            bot_assigned_label = single_value_extended_property.get("value")
            break

    # Message body
    html_body_soup = message.get_body_soup()
    html_body = ""
    text_body = ""
    if html_body_soup:
        html_body = html_body_soup.decode()
    if message.body_type.upper() != "HTML":
        text_body = message.body
    elif html_body:
        text_body = html_to_text(html_body)

    # Unsubscribe data
    message_headers = index_headers(message.message_headers)
    unsubscribe_data = get_unsubscribe_data(message_headers)

    return ParsedOutlookMessage(
        message_id=message.object_id,
        user_mailbot_profile_id=user_mailbot_profile.id,
        service_provider=settings.SERVICE_PROVIDER_MICROSOFT,
        user_email=user_mailbot_profile.user.email,
        user_id=user_mailbot_profile.user_id,
        thread_id=message.conversation_id,
        label_ids=[message.folder_id],
        body_preview=message.body_preview,
        attachments=message.attachments,
        html_body=html_body,
        text_body=text_body,
        is_read=message.is_read,
        is_sent=folder_name == OutlookKnownLabelName.SENT.value,
        message_headers=message_headers,
        received_at=message.received,
        subject=message.subject,
        from_name_email=name_email_mapper(message.sender),
        to_name_email=list(map(name_email_mapper, message.to)),
        cc_name_email=list(map(name_email_mapper, message.cc)),
        bcc_name_email=list(map(name_email_mapper, message.bcc)),
        single_value_extended_property__bot_assigned_label=bot_assigned_label,
        unsubscribe_link=unsubscribe_data["unsubscribe_link"],
        unsubscribe_link_mail_to=unsubscribe_data["unsubscribe_link_mail_to"],
        unsubscribe_link_one_click=unsubscribe_data["unsubscribe_link_one_click"],
        unsubscribe_link_mail_to_parsed=unsubscribe_data["unsubscribe_link_mail_to_parsed"],
    )


def parse_message(user_mailbot_profile, message) -> ParsedMessage:
    if user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_GOOGLE:
        return parse_gmail_message(user_mailbot_profile, message)
    elif user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_MICROSOFT:
        return parse_outlook_message(user_mailbot_profile, message)
