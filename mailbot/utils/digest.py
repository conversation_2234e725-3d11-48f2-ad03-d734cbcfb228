import logging
import re
from typing import Optional, List, Dict
from constance import config as constance_config
from django.utils import timezone
from jarvis.models import Assistant
from jarvis.utils.assistants import OpenAIAssistant
from jarvis.utils.openai import get_token_count, trim_conversation

logger = logging.getLogger(__name__)


class EmailDigest:
    """
    Class to generate a digest email template using OpenAI assistants based on user instructions and email content.
    """

    default_fields_to_select = ["subject", "body"]
    PROCESS_EMAIL_BATCH_OPENAI_MODEL_NAME = "gpt-3.5-turbo"
    GENERATE_DIGEST_HTML_EMAIL_MODEL_NAME = "gpt-4-1106-preview"
    PROCESS_EMAIL_FOR_DIGEST_GENERATION_INSTRUCTION_ASSISTANT = "GENERIC_GPT_3.5"
    GENERATE_DIGEST_HTML_EMAIL_INSTRUCTION_ASSISTANT = "GENERIC_GPT_4"

    def __init__(self, messages: List[Dict], select_fields: Optional[List] = None):
        """
        Initialize the DigestEmailGenerator instance.

        Args:
            messages (list): List of messages to be included in the digest.
            select_fields (list, optional): List of fields to include in the digest. Defaults to None.
        """
        self.messages = messages
        self.assistant = OpenAIAssistant()
        self.message_max_tokens = 8000
        self.fields_to_select = self.default_fields_to_select + (select_fields or [])

    def attach_token_count(self):
        """
        Attaches token count to each message
        """
        for message in self.messages:
            token_count = get_token_count(
                self.generate_message_content(message), self.PROCESS_EMAIL_BATCH_OPENAI_MODEL_NAME
            )
            message["token_count"] = token_count

    def generate_message_content(self, message: dict):
        """
        Generate input message content based on selected fields.
        Args:
            message (dict): Dictionary representing the message.
        Returns:
            str: Formatted content of the message including selected fields.
        """
        content = "Email:\n"

        for field in self.fields_to_select:
            content += f"{field.capitalize()}:\n{message.get(field, '')}\n"

        content += "\n"
        return content

    def form_message_batches(self):
        """
        Efficiently form batches of messages based on token count.

        Returns:
            List: List of batched messages where each batch is combined within the token limit.
        """

        sorted_messages = sorted(self.messages, key=lambda x: x["token_count"])
        batches = []
        current_batch = []
        batch_size = 0
        batch_max_tokens = 9000
        for message in sorted_messages:
            token_count = message["token_count"]
            # If the message token count is greater than the limit:
            #  - Append the current batch if present to the list of batches.
            #  - Trim the message.
            #  - Create and append a new batch with the trimmed message.
            #  - Post that, create an empty batch.
            if token_count > self.message_max_tokens:
                # We are trimming the body and till 9800 max tokens as the max limit of a message can be 10000
                # Assumption : part of message apart from body will contain maximum 200 tokens
                message["body"] = trim_conversation(
                    message["body"], self.PROCESS_EMAIL_BATCH_OPENAI_MODEL_NAME, self.message_max_tokens
                )
                if current_batch:
                    batches.append(current_batch)
                current_batch = [message]
                batches.append(current_batch)
                current_batch = []
                batch_size = 0
            elif batch_size + token_count <= batch_max_tokens:
                current_batch.append(message)
                batch_size += token_count
            # Appending old batch and creating new batch since last batch has reached its limit
            else:
                batches.append(current_batch)
                current_batch = [message]
                batch_size = token_count
        if current_batch:
            batches.append(current_batch)
        return batches

    def prepare_digest_email_template(self, user_query):
        """
        Prepare a digest email template using the provided user instructions and email content.

        Returns:
            str: HTML digest email template generated by combining email content and user instructions.
                 Returns None if the assistant result is empty or an error occurs during processing.
        """
        self.attach_token_count()
        batches = self.form_message_batches()

        track_threads = {}
        assistants = Assistant.objects.filter(
            name__in=[
                self.PROCESS_EMAIL_FOR_DIGEST_GENERATION_INSTRUCTION_ASSISTANT,
                self.GENERATE_DIGEST_HTML_EMAIL_INSTRUCTION_ASSISTANT,
            ]
        ).values_list("name", "openai_id")
        assistant_details = dict(assistants)
        gpt_3_assistant_openai_id = assistant_details[self.PROCESS_EMAIL_FOR_DIGEST_GENERATION_INSTRUCTION_ASSISTANT]

        for batch in batches:
            content = ""
            for message in batch:
                content += self.generate_message_content(message)
            run_instruction = constance_config.PROCESS_EMAIL_FOR_DIGEST_GENERATION_INSTRUCTION.format(
                user_query=user_query
            )
            self.assistant.create_and_run_thread_with_message(
                run_instruction,
                gpt_3_assistant_openai_id,
                content,
            )
            track_threads[self.assistant.thread_id] = self.assistant.thread_run_id

        combined_output = self.assistant.track_threads_status_and_retrieve_combined_output(track_threads)
        # Generate the html template
        gpt_4_assistant_openai_id = assistant_details[self.GENERATE_DIGEST_HTML_EMAIL_INSTRUCTION_ASSISTANT]

        run_instruction = constance_config.GENERATE_DIGEST_HTML_EMAIL_INSTRUCTION.format(user_query=user_query)
        self.assistant.create_and_run_thread_with_message(
            run_instruction,
            gpt_4_assistant_openai_id,
            combined_output,
        )
        assistant_result, assistant_output = self.assistant.get_assistant_output()
        html_template = None
        if assistant_result:
            html_template = self.parse_html_from_assistant_output(assistant_output)

        return html_template

    def parse_html_from_assistant_output(self, assistant_output: str):
        pattern = r"```html\n(.*?)\n```"
        match = re.search(pattern, assistant_output, re.DOTALL)
        if match:
            return match.group(1)
        else:
            return None


def calculate_next_digest_hour(digest_frequency_hours: int) -> int:
    """
    Calculates the next hour (in 24-hour format) at which a digest should be sent,
    based on the current hour and a given digest frequency.

    Args:
        digest_frequency_hours (int): The number of hours between each digest.

    Returns:
        int: The hour of the next digest (0–23), relative to the current time.
    """
    now = timezone.now()
    next_hour = (now.hour + digest_frequency_hours) % 24
    return next_hour
