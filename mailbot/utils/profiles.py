import logging
from typing import Optional

from django.utils import timezone

from mailbot.models import UserMailBotProfile, SecondaryMailBotProfilesThrough
from mailbot.utils.defaults import MAILBOT_DEFAULT_PREFERENCES, MailBotProfileMetadataKey

logger = logging.getLogger(__name__)


def create_mailbot_profile(
    user, service_provider: str, authentication: dict, mailbot_scopes_information: dict, user_picture=None
):
    """Creates mailbot profile. Will revive the mailbot profile if found soft-deleted

    Args:
        user: User instance
        service_provider: Service provider
        authentication: Granted authentication dictionary
        mailbot_scopes_information: Information of whether all scopes are granted, which scopes are not granted, etc
    """
    access_token = authentication.pop("access_token")
    refresh_token = authentication.pop("refresh_token")
    try:
        user_mailbot_profile = UserMailBotProfile.objects.get(user=user, service_provider=service_provider)
        user_mailbot_profile.granted_authentication = authentication
        user_mailbot_profile.access_token = access_token
        user_mailbot_profile.refresh_token = refresh_token
        user_mailbot_profile.user_picture = user_picture
        user_mailbot_profile.save()
        mailbot_profile_created = False
    except UserMailBotProfile.DoesNotExist:
        user_mailbot_profile, mailbot_profile_created = UserMailBotProfile.all_objects.update_or_create(
            user=user,
            service_provider=service_provider,
            defaults={
                "granted_authentication": authentication,
                "deleted": None,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "user_picture": user_picture,
                "algo_version": UserMailBotProfile.get_latest_algo_version(),
            },
        )
    if mailbot_scopes_information["all_scopes_granted_for_first_time"]:
        user_mailbot_profile.preferences = MAILBOT_DEFAULT_PREFERENCES
        user_mailbot_profile.metadata[
            MailBotProfileMetadataKey.ALL_SCOPES_GRANTED_AT.value
        ] = timezone.now().isoformat()
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.ALL_SCOPES_GRANTED.value] = True
        if user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.NON_GRANTED_SCOPES.value):
            del user_mailbot_profile.metadata[MailBotProfileMetadataKey.NON_GRANTED_SCOPES.value]
        user_mailbot_profile.save(update_fields=["preferences", "metadata"])
    elif not mailbot_scopes_information["all_scopes_granted"]:
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.ALL_SCOPES_GRANTED.value] = False
        user_mailbot_profile.metadata[MailBotProfileMetadataKey.NON_GRANTED_SCOPES.value] = mailbot_scopes_information[
            "non_granted_scopes"
        ]
        user_mailbot_profile.save(update_fields=["metadata"])
    return user_mailbot_profile, mailbot_profile_created


def get_primary_user(user_mailbot_profile: UserMailBotProfile):
    """
    Fetches the primary user associated with the secondary account.
    """
    try:
        secondary_mailbot_profile = SecondaryMailBotProfilesThrough.objects.select_related(
            "primary_mailbot_profile__user"
        ).get(secondary_mailbot_profile=user_mailbot_profile)
        primary_user = secondary_mailbot_profile.primary_mailbot_profile.user
        return primary_user
    except SecondaryMailBotProfilesThrough.DoesNotExist:
        logger.info(f"Secondary mailbot profile not found for user {user_mailbot_profile.user.email}")
        return user_mailbot_profile
