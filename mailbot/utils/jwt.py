from typing import Any, Dict

import jwt
from django.conf import settings


def jwt_encode(payload: Dict[str, Any]) -> str:
    """
    Encode the `payload` as JSON Web Token.
    Args:
        payload (dict): Payload with JWT claims, e.g. dict(exp=..., aud=...) for expire and audience.

    Returns:
        str: JSON Web Token
    """
    return jwt.encode(payload=payload, key=settings.JWT_KEY, algorithm="HS256")


def jwt_decode(jwt_token: str) -> Dict[str, Any]:
    """
    Verify the `jwt` token signature and return the token claims.
    Expire claim must be present for token to be valid.
    Args:
        jwt_token (str): The token to be decoded

    Returns:
        dict: The JWT claims and payload
    """
    return jwt.decode(
        jwt=jwt_token,
        algorithms=["HS256"],
        key=settings.JWT_KEY,
        options={
            "require": ["exp"],
        },
    )
