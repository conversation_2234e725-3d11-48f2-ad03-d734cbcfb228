from datetime import timedelta
import logging
import pytz
from django.conf import settings
from django.db.models import Q
from django.utils import timezone
from execfn.common.utils.datetime import get_utc_datetime
from applications.models import EmailTemplate, SentEmails
from execfn.common.applications import ApplicationTag
from mailbot.models import Message, SenderProfile, UserMailBotProfile, MailBotGenericLabel
from mailbot.service.factory import MessageServiceFactory
from mailbot.utils.defaults import MailBotMessageHeaders, MailBotTemplateTag, MailOperationType
from mailbot.utils.email_overlay import get_inbox_overlay_instance
from mailbot.utils.exceptions import MessageIdNotFoundError
from mailbot.utils.jwt import jwt_encode

logger = logging.getLogger(__name__)


def get_digest_context_for_zapped_label(user_mailbot_profile: UserMailBotProfile):
    """
    Get context for digest mail for current user profile.
    Args:
        user_mailbot_profile: User profile for which to generate context.

    Returns:
        Dict[str, Any]: Context used to render digest mail.
    """
    tz_name = user_mailbot_profile.preferences.get("digest_timezone", "UTC")
    current_datetime_utc = timezone.now()
    current_datetime_user_tz = current_datetime_utc.astimezone(pytz.timezone(tz_name))
    past_day_datetime_utc = current_datetime_utc - timezone.timedelta(days=1)
    service = MessageServiceFactory.get_message_service(user_mailbot_profile=user_mailbot_profile)

    # Past day zapped emails for current user
    zapped_items = Message.objects.filter(
        ~Q(metadata__has_key="include_in_digest") | Q(metadata__include_in_digest=True),
        user_mailbot_profile=user_mailbot_profile,
        received_at__gt=past_day_datetime_utc,
        generic_label=MailBotGenericLabel.ZAPPED.value,
    ).only("metadata", "message_id")

    zapped_items_count = 0
    auto_responder_items_count = 0
    zapped_items_context = {}
    auto_responder_items_context = {}
    for zapped_item in zapped_items:
        try:
            message_body_text = service.get_message_body(message_id=zapped_item.message_id, html=False)
        except MessageIdNotFoundError:
            # Skip message that is already deleted by the user from digest
            continue
        else:
            if not message_body_text:
                # There is no message body in an attachment only email
                # TODO: Need to identify other cases where text body is not available due to bad parsing in `html_to_text`
                logger.info(
                    f"Message ID {zapped_item.message_id} for user {user_mailbot_profile.user.email} has no text body for rendering in digest"
                )
                message_body_text = "Attachments"
            sender_email = zapped_item.metadata["from"][1]
            zapped_item_context = {
                "message_id": zapped_item.message_id,
                "subject": zapped_item.subject,
                "body": message_body_text[:100] + ("..." if len(message_body_text) > 100 else ""),
            }
            if zapped_item.metadata.get("auto_responder_verified"):
                auto_responder_items_count += 1
                if sender_email in auto_responder_items_context:
                    auto_responder_items_context[sender_email]["messages"].append(zapped_item_context)
                else:
                    auto_responder_items_context[sender_email] = {
                        "white_list_token": jwt_encode(
                            payload={
                                "exp": timezone.now() + timezone.timedelta(days=1),
                                "operation": MailOperationType.TRAINING_THROUGH_DIGEST.value,
                                "user_mailbot_profile_id": user_mailbot_profile.id,
                                "message_id": zapped_item.message_id,
                                "sender_email": sender_email,
                                "new_label": MailBotGenericLabel.WHITE_LIST.value,
                            }
                        ),
                        "zapped_token": jwt_encode(
                            payload={
                                "exp": timezone.now() + timezone.timedelta(days=1),
                                "operation": MailOperationType.TRAINING_THROUGH_DIGEST.value,
                                "user_mailbot_profile_id": user_mailbot_profile.id,
                                "sender_email": sender_email,
                                "message_id": zapped_item.message_id,
                                "new_label": MailBotGenericLabel.ZAPPED.value,
                            }
                        ),
                        "messages": [zapped_item_context],
                    }
            else:
                zapped_items_count += 1
                if sender_email in zapped_items_context:
                    zapped_items_context[sender_email]["messages"].append(zapped_item_context)
                else:
                    zapped_items_context[sender_email] = {
                        "white_list_token": jwt_encode(
                            payload={
                                "exp": timezone.now() + timezone.timedelta(days=1),
                                "operation": MailOperationType.TRAINING_THROUGH_DIGEST.value,
                                "user_mailbot_profile_id": user_mailbot_profile.id,
                                "sender_email": sender_email,
                                "message_id": zapped_item.message_id,
                                "new_label": MailBotGenericLabel.WHITE_LIST.value,
                            }
                        ),
                        "messages": [zapped_item_context],
                    }

    # Past day user training mails for current user
    user_trainings = (
        SenderProfile.objects.filter(
            user_trained_at__gt=past_day_datetime_utc, user_mailbot_profile=user_mailbot_profile
        )
        .exclude(user_training="")
        .only("sender_email", "sender_name", "user_training")
    )
    return {
        "user_local_date": current_datetime_user_tz.strftime("%B %d, %Y"),
        "name": user_mailbot_profile.user.first_name.capitalize(),
        "frontend_base_url": settings.FRONTEND_BASE_URL,
        "backend_base_url": settings.BACKEND_BASE_URL,
        "training_items": user_trainings,
        "total_count": zapped_items_count + auto_responder_items_count,
        "zapped_items_count": zapped_items_count,
        "auto_responder_items_count": auto_responder_items_count,
        "zapped_items": zapped_items_context,
        "auto_responder_items": auto_responder_items_context,
        "archive_after": user_mailbot_profile.preferences.get("archive_mailbot_mails_after", 24),
    }


def get_digest_time_window_for_secondary_profile(secondary_user_mailbot_profile, primary_user_mailbot_profile):
    """
    Calculates the digest time window for a secondary profile digest email.

    Logic Summary:
    - If a last digest exists:
        - If seen → start = now_utc - frequency_delta
        - If unseen → start = last_digest_time - frequency
    - If no last digest → fallback to (now - frequency)
    - If forwarding policy was recently changed → all logic starts after that
    - If the gap from expected digest time to now is large enough, identify skipped period

    Returns:
        dict: {
            start_time_utc: datetime (start of digest window in UTC),
            end_time_utc: datetime (end of digest window in UTC),
            skipped_range_user_tz: Tuple(start, end) or None (in user's timezone),
            digest_frequency: int (digest frequency in hours),
            last_digest_sent_at: datetime (last digest sent at in UTC),
            current_digest_start_time_user_tz: datetime (start of current digest window in user's timezone irrespective of last digest sent at),
            current_digest_end_time_user_tz: datetime (end of current digest window in user's timezone irrespective of last digest sent at),
            prev_digest_include_emails_start_time_user_tz: datetime (last digest include emails start time in user's timezone),
            prev_digest_include_emails_end_time_user_tz: datetime (last digest include emails end time in user's timezone),
        }
    """

    # Get user's timezone and digest frequency
    tz_name = primary_user_mailbot_profile.preferences.get("digest_timezone", "UTC")
    user_tz = pytz.timezone(tz_name)
    digest_frequency = secondary_user_mailbot_profile.secondary_profile_preferences.get("digest_frequency", 1)
    frequency_delta = timedelta(hours=digest_frequency)

    now_utc = timezone.now()

    # Optional time when forwarding policy was last updated
    policy_changed_at = secondary_user_mailbot_profile.secondary_profile_preferences.get(
        "last_forwarding_policy_setting_changed_at"
    )

    # Convert policy_changed_at to datetime if it's a string
    if policy_changed_at and isinstance(policy_changed_at, str):
        try:
            policy_changed_at = get_utc_datetime(input_datetime=policy_changed_at, time_zone=pytz.timezone("UTC"))
        except (ValueError, TypeError):
            # If conversion fails, set to None to avoid comparison errors
            policy_changed_at = None

    # Fallback start time if no digest exists
    fallback_start = now_utc - frequency_delta

    # Fetch the last digest sent with this template
    email_template = EmailTemplate.objects.only("id").get(
        application__tag=ApplicationTag.MailBot.value,
        tag=MailBotTemplateTag.SECONDARY_PROFILE_DIGEST.value,
    )

    # Get latest sent digest email, filtering by policy update time if available
    query = SentEmails.objects.filter(
        user=primary_user_mailbot_profile.user,
        template=email_template,
    )
    if policy_changed_at:
        query = query.filter(created__gte=policy_changed_at)

    last_digest = query.only("created", "metadata").order_by("-created").first()
    start_utc = fallback_start
    # Determine digest start time based on last digest and whether it was seen
    if last_digest:
        last_created = last_digest.created
        seen = (last_digest.metadata or {}).get("opens", 0) > 0
        # If seen, next digest starts after the last one else include emails from an hour before last digest
        start_utc = start_utc if seen else last_created - frequency_delta

    # Determine skipped range, if applicable
    skipped_range_user_tz = None
    skipped_base = (
        last_digest.created
        if last_digest and (not policy_changed_at or last_digest.created > policy_changed_at)
        else policy_changed_at
    )
    skipped_hours = 0
    if skipped_base:
        skipped_hours = ((now_utc - frequency_delta) - skipped_base).total_seconds() // 3600
        # Only mark skipped if the gap is at least one full digest interval but less than a day
        if digest_frequency <= skipped_hours < 24:
            skipped_range_user_tz = (
                skipped_base.astimezone(user_tz),
                (now_utc - frequency_delta).astimezone(user_tz),
            )
    return {
        "start_time_utc": start_utc,
        "end_time_utc": now_utc,
        "skipped_range_user_tz": skipped_range_user_tz,
        "digest_frequency": digest_frequency,
        "last_digest_sent_at": last_digest.created if last_digest else None,
        "current_digest_start_time_user_tz": (now_utc - frequency_delta).astimezone(user_tz),
        "current_digest_end_time_user_tz": now_utc.astimezone(user_tz),
        "prev_digest_include_emails_start_time_user_tz": (last_digest.created - frequency_delta).astimezone(user_tz)
        if last_digest
        else None,
        "prev_digest_include_emails_end_time_user_tz": last_digest.created.astimezone(user_tz) if last_digest else None,
    }


def get_digest_context_for_secondary_profile(secondary_user_mailbot_profile, primary_user_mailbot_profile):
    """
    Get context for digest emails from secondary profiles.

    Args:
        secondary_user_mailbot_profile: Secondary profile for which to generate digest
        primary_user_mailbot_profile: Primary profile that will receive the digest

    Returns:
        Dict with context for digest email template
    """
    digest_time_window = get_digest_time_window_for_secondary_profile(
        secondary_user_mailbot_profile, primary_user_mailbot_profile
    )
    service = MessageServiceFactory.get_message_service(user_mailbot_profile=secondary_user_mailbot_profile)

    # Get user's timezone
    tz_name = primary_user_mailbot_profile.preferences.get("digest_timezone", "UTC")

    # TODO: Make this include_in_digest consistent it should be include in all email metadata
    inbox_items = (
        Message.objects.filter(
            ~Q(metadata__has_key="include_in_digest") | Q(metadata__include_in_digest=True),
            user_mailbot_profile=secondary_user_mailbot_profile,
            received_at__gt=digest_time_window["start_time_utc"],
            generic_label=MailBotGenericLabel.WHITE_LIST.value,
        )
        .exclude(
            Q(metadata__message_headers__has_key=MailBotMessageHeaders.MESSAGE_CATEGORY.value)
            & ~Q(metadata__message_headers__has_key=MailBotMessageHeaders.ORIGINAL_MESSAGE_ID.value)
        )
        .only("metadata", "message_id", "received_at")
        .order_by("received_at")
    )

    if inbox_items.count() == 0:
        return None

    current_digest_items_count = 0
    previous_digest_items_count = 0
    current_digest_items_context = {}
    previous_digest_items_context = {}

    for inbox_item in inbox_items:
        try:
            # If current selected message is overlay message, then get the original message and use it for digest
            if original_message_id := inbox_item.fetch_header(MailBotMessageHeaders.ORIGINAL_MESSAGE_ID.value):
                original_message = Message.objects.only("metadata", "message_id", "received_at").get(
                    message_id=original_message_id
                )
                if original_message:
                    inbox_item = original_message
            message_body_text = service.get_message_body(message_id=inbox_item.message_id, html=False)
        except MessageIdNotFoundError:
            # Skip message that is already deleted by the user from digest
            continue
        else:
            # Handle cases where message body might be missing
            if not message_body_text:
                # There is no message body in an attachment only email
                logger.info(
                    f"Message ID {inbox_item.message_id} for user {secondary_user_mailbot_profile.user.email} has no text body for rendering in digest"
                )
                message_body_text = "Attachments"

            sender_email = inbox_item.metadata["from"][1]
            inbox_item_context = {
                "message_id": inbox_item.message_id,
                "subject": inbox_item.subject,
                "received_at": inbox_item.received_at.astimezone(pytz.timezone(tz_name)).strftime("%I:%M %p"),
                "body": message_body_text[:100] + ("..." if len(message_body_text) > 100 else ""),
                "sender_name": inbox_item.metadata["from"][0],
                "view_message_url": f"https://mail.google.com/mail/u/{secondary_user_mailbot_profile.user.email}/#all/{inbox_item.message_id}",
            }

            # Group message into current or previous digest section
            last_digest_sent_at = digest_time_window["last_digest_sent_at"]
            if last_digest_sent_at and inbox_item.received_at < last_digest_sent_at:
                current_context = previous_digest_items_context
                previous_digest_items_count += 1
            else:
                current_context = current_digest_items_context
                current_digest_items_count += 1

            # Group messages by sender for better organization in digest
            if sender_email in current_context:
                current_context[sender_email]["messages"].append(inbox_item_context)
            else:
                current_context[sender_email] = {
                    "zapped_token": jwt_encode(
                        payload={
                            "exp": timezone.now() + timezone.timedelta(days=1),
                            "operation": MailOperationType.TRAINING_THROUGH_DIGEST.value,
                            "user_mailbot_profile_id": secondary_user_mailbot_profile.id,
                            "sender_email": sender_email,
                            "message_id": inbox_item.message_id,
                            "new_label": MailBotGenericLabel.ZAPPED.value,
                        }
                    ),
                    "messages": [inbox_item_context],
                }

    # Format previous digest times if present
    prev_start_time_str = (t := digest_time_window.get("prev_digest_include_emails_start_time_user_tz")) and t.strftime(
        "%I:%M %p"
    )
    prev_end_time_str = (t := digest_time_window.get("prev_digest_include_emails_end_time_user_tz")) and t.strftime(
        "%I:%M %p"
    )
    return {
        "frontend_base_url": settings.FRONTEND_BASE_URL,
        "backend_base_url": settings.BACKEND_BASE_URL,
        "secondary_account_email": secondary_user_mailbot_profile.user.email,
        "primary_account_email": primary_user_mailbot_profile.user.email,
        "total_count": current_digest_items_count,
        "current_digest_items": current_digest_items_context,
        "previous_digest_items": previous_digest_items_context,
        "digest_frequency": digest_time_window["digest_frequency"],
        "prev_digest_include_emails_start_time_user_tz": prev_start_time_str,
        "prev_digest_include_emails_end_time_user_tz": prev_end_time_str,
        "current_digest_start_time_user_tz": digest_time_window["current_digest_start_time_user_tz"].strftime(
            "%I:%M %p"
        ),
        "current_digest_end_time_user_tz": digest_time_window["current_digest_end_time_user_tz"].strftime("%I:%M %p"),
        "archive_after": primary_user_mailbot_profile.preferences.get("archive_mailbot_mails_after", 24),
        "skipped_range_text": f"⏸️ No important emails • {digest_time_window['skipped_range_user_tz'][0].strftime('%I:%M %p')} - {digest_time_window['skipped_range_user_tz'][1].strftime('%I:%M %p')}"
        if digest_time_window["skipped_range_user_tz"]
        else None,
        "preview_text": f"{current_digest_items_count} important emails received from {digest_time_window['current_digest_start_time_user_tz'].strftime('%I:%M %p')} - {digest_time_window['current_digest_end_time_user_tz'].strftime('%I:%M %p')}",
        "message_subject": f"{current_digest_items_count} important emails received between {digest_time_window['current_digest_start_time_user_tz'].strftime('%I:%M %p')} - {digest_time_window['current_digest_end_time_user_tz'].strftime('%I:%M %p')} on {secondary_user_mailbot_profile.user.email}",
    }
