from django.conf import settings


def set_cookies(response, user_mailbot_profile):
    """
    Set necessary cookies for frontend to render components.
    Args:
        response: Response object
        user_mailbot_profile: UserMailBotProfile object
    """
    cookies_to_set = {
        "service_provider": user_mailbot_profile.service_provider,
        "mailbot_profile_id": user_mailbot_profile.id,
    }
    for cookie_key, cookie_value in cookies_to_set.items():
        response.set_cookie(
            key=cookie_key,
            value=cookie_value,
            httponly=False,
            secure=True,
            samesite="None",
            domain=settings.COOKIE_DOMAIN,
            max_age=60 * 24 * 60 * settings.COOKIE_MAX_AGE_DAYS,
        )
