import logging
from typing import Dict, Any, Tuple, Optional

from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from applications.utils.email import get_normalized_email
from execfn import ApplicationTag
from mailbot.models import Message, MailBotGenericLabel, DomainTraining, SenderProfile, AnalyticsEvents
from mailbot.service.factory import MessageServiceFactory
from mailbot.utils.core import MailBotUserCore
from mailbot.utils.defaults import (
    MailBotMessageCategory,
    MailOperationStatus,
    MailOperationType,
)
from mailbot.utils.exceptions import MessageIdNotFoundError

logger = logging.getLogger(__name__)


class MailOperation:
    """
    Process the payload received when user interacts with mailbot sent emails
    """

    def __init__(self, user_mailbot_profile, payload: Dict[str, Any], undo: bool = False):
        """
        Args:
            user_mailbot_profile: UserMailBotProfile object
            payload: JWT decoded payload.
            undo: Whether to undo the provided operation.
        """
        self.payload = payload
        self.user_mailbot_profile = user_mailbot_profile
        self.service = MessageServiceFactory.get_message_service(user_mailbot_profile=self.user_mailbot_profile)
        self.message_id = payload.get("message_id")
        self.new_label = payload.get("new_label")
        self.label_name = self.service.label_name_lookup.get(self.new_label)
        self.undo = undo
        self.response: Tuple[Optional[int], Dict[str, Any]] = None, {}
        try:
            self.message = Message.objects.get(message_id=self.message_id)
            self.sender_email = self.message.metadata["from"][1]
        except Message.DoesNotExist:
            self.response = MailOperationStatus.ORIGINAL_MESSAGE_NOT_FOUND.value, {}
        else:
            self.normalized_sender_email = get_normalized_email(self.sender_email)
            self.sender_profile = SenderProfile.objects.get(
                user_mailbot_profile_id=self.message.user_mailbot_profile_id,
                normalized_sender_email=self.normalized_sender_email,
            )
            self.sender_domain = self.sender_profile.sender_domain

    def move_original_message_to_new_label(self):
        """
        Move original message to `new_label`.
        """
        # User can interact with overlay again before expire but message must not be in same label as destination
        previous_label = self.message.generic_label
        if previous_label == self.new_label:
            logger.info(f"message {self.message_id} already moved to {self.new_label} for user {self.service.email}")
            self.response = MailOperationStatus.MESSAGE_ALREADY_MOVED_TO_LABEL.value, {"label_name": self.label_name}
            return
        elif previous_label == Message.GENERIC_LABEL_TRASH:
            self.response = MailOperationStatus.ORIGINAL_MESSAGE_IN_TRASH.value, {
                "label_name": self.service.label_name_lookup.get(previous_label)
            }
        else:
            logger.info(f"Moving message to {self.label_name} for user {self.service.email}")
            # Move message to new label
            try:
                self.service.move_to_known_label(self.message_id, generic_label=self.new_label)
            except MessageIdNotFoundError:
                self.response = MailOperationStatus.ORIGINAL_MESSAGE_NOT_FOUND.value, {}

    def archive_current_message(self, category: MailBotMessageCategory):
        """
        Instant archive current mailbot sent message on which user interacted with.
        Args:
            category: Message category for internal email which is to be archived.
        """
        logger.info(f"Archiving {category.value} for user {self.service.email}")
        try:
            if overlay_message_id := self.message.metadata.get("overlay_message_id"):
                message_to_archive = Message.objects.get(message_id=overlay_message_id)
            else:
                archive_filter = {
                    "metadata__ORIGINAL_MESSAGE_ID": self.message_id,
                    "metadata__MESSAGE_CATEGORY": category.value,
                    "user_mailbot_profile": self.user_mailbot_profile,
                }
                message_to_archive = Message.objects.get(**archive_filter)
        except Message.DoesNotExist:
            logger.exception(
                "Message to archive not found in database",
                extra={
                    "sender": self.sender_profile.sender_email,
                    "user": self.service.email,
                    "original_message_id": self.message_id,
                    "category": category.value,
                },
            )
        else:
            self.service.archive_message(message_to_archive.message_id)

    def update_user_training(self):
        """
        Update the user's training on sender level
        """
        MailBotUserCore(service=self.service).update_user_training(
            sender_email=self.sender_email,
            user_training=self.new_label,
        )

    def clear_user_training(self):
        """
        Clear the user's training on sender level
        """
        MailBotUserCore(service=self.service).update_user_training(sender_email=self.sender_email, user_training="")

    def update_domain_training(self):
        """
        Update the user's training on domain level
        """
        try:
            domain_training = DomainTraining.objects.get(
                user_mailbot_profile=self.user_mailbot_profile, sender_domain=self.sender_domain
            )
        except DomainTraining.DoesNotExist:
            logger.info(f"Domain training not present for user {self.service.email}")
        else:
            MailBotUserCore(service=self.service).update_domain_training(
                domain_training=domain_training, domain_training_label=self.new_label
            )

    def clear_domain_training(self):
        """
        Clear the user's training on domain level
        """
        try:
            domain_training = DomainTraining.objects.get(
                user_mailbot_profile=self.user_mailbot_profile, sender_domain=self.sender_domain
            )
        except DomainTraining.DoesNotExist:
            logger.info(f"Domain training not present for user {self.service.email}")
        else:
            MailBotUserCore(service=self.service).update_domain_training(
                domain_training=domain_training, domain_training_label=""
            )

    def domain_training_through_first_time_sender_overlay(self):
        """
        Operation to execute when user interacts with first time sender overlay for domain training.
        """
        logger.info(
            f"User {self.service.email} interacted with first time sender overlay, sender {self.sender_profile.sender_email}, domain {self.sender_domain}"
        )
        if not self.undo:
            self.move_original_message_to_new_label()
            if self.response[0] in [
                MailOperationStatus.ORIGINAL_MESSAGE_IN_TRASH.value,
                MailOperationStatus.MESSAGE_ALREADY_MOVED_TO_LABEL.value,
                MailOperationStatus.ORIGINAL_MESSAGE_NOT_FOUND.value,
            ]:
                # response is already set by above function
                return
            self.update_domain_training()
            # Instant archive the overlay
            self.archive_current_message(MailBotMessageCategory.FIRST_TIME_SENDER_OVERLAY)
            self.response = MailOperationStatus.DOMAIN_TRAINING_SUCCESS.value, {
                "label_name": self.label_name,
                "sender_domain": self.sender_domain,
            }
        else:
            self.service.move_to_known_label(self.message_id, generic_label=MailBotGenericLabel.ZAPPED.value)
            self.clear_domain_training()
            self.response = MailOperationStatus.UNDO_DOMAIN_TRAINING_SUCCESS.value, {
                "sender_domain": self.sender_domain
            }

    def user_training_through_first_time_sender_overlay(self):
        """
        Operation to execute when user interacts with first time sender overlay for user training.
        """
        logger.info(
            f"User {self.service.email} interacted with first time sender overlay, sender {self.sender_profile.sender_email}"
        )
        if not self.undo:
            self.move_original_message_to_new_label()
            if self.response[0] in [
                MailOperationStatus.ORIGINAL_MESSAGE_IN_TRASH.value,
                MailOperationStatus.MESSAGE_ALREADY_MOVED_TO_LABEL.value,
                MailOperationStatus.ORIGINAL_MESSAGE_NOT_FOUND.value,
            ]:
                # response is already set by above function
                return
            self.update_user_training()
            # Instant archive the overlay
            self.archive_current_message(MailBotMessageCategory.FIRST_TIME_SENDER_OVERLAY)
            self.response = MailOperationStatus.USER_TRAINING_SUCCESS.value, {
                "label_name": self.label_name,
                "sender_email": self.sender_email,
            }
        else:
            self.service.move_to_known_label(self.message_id, generic_label=MailBotGenericLabel.ZAPPED.value)
            self.clear_user_training()
            self.response = MailOperationStatus.UNDO_USER_TRAINING_SUCCESS.value, {"sender_email": self.sender_email}

    def update_preference_for_first_time_sender_domain(self):
        try:
            domain_training = DomainTraining.objects.get(
                user_mailbot_profile=self.user_mailbot_profile, sender_domain=self.sender_domain
            )
        except DomainTraining.DoesNotExist:
            return
        else:
            send_alerts_for_domain = self.payload.get("send_alerts_for_domain")
            if self.undo:
                send_alerts_for_domain = not send_alerts_for_domain
            if send_alerts_for_domain == domain_training.metadata.get("send_alerts_for_domain"):
                self.response = MailOperationStatus.PREFERENCE_ALREADY_UPDATED.value, {}
                return
            domain_training.metadata["send_alerts_for_domain"] = send_alerts_for_domain
            domain_training.save()
            if not self.undo:
                self.archive_current_message(MailBotMessageCategory.FIRST_TIME_SENDER_OVERLAY)
            logger.info(
                f"Domain training preference updated for user {self.user_mailbot_profile.user.email}, domain {self.sender_domain}"
            )
            if send_alerts_for_domain:
                self.response = MailOperationStatus.DOMAIN_PREFERENCE_UPDATE_SUCCESS.value, {
                    "sender_domain": self.sender_domain
                }
            else:
                self.response = MailOperationStatus.UNDO_DOMAIN_PREFERENCE_UPDATE_SUCCESS.value, {
                    "sender_domain": self.sender_domain
                }

    def training_through_digest(self):
        logger.info(
            f"User {self.service.email} interacted with mailbot digest, sender {self.sender_profile.sender_email} for message ID {self.message_id}, new label {self.new_label}"
        )
        if not self.undo:
            self.update_user_training()
            self.response = MailOperationStatus.USER_TRAINING_SUCCESS.value, {
                "label_name": self.label_name,
                "sender_email": self.sender_email,
            }
        else:
            self.clear_user_training()

    def cancel_lucene_alert(self):
        """
        Archive lucene alert and non-archive original message.
        """
        logger.info(
            f"User {self.service.email} interacted with lucene alert, sender {self.sender_profile.sender_email}"
        )
        self.move_original_message_to_new_label()
        if self.response[0] in [
            MailOperationStatus.ORIGINAL_MESSAGE_IN_TRASH.value,
            MailOperationStatus.MESSAGE_ALREADY_MOVED_TO_LABEL.value,
            MailOperationStatus.ORIGINAL_MESSAGE_NOT_FOUND.value,
        ]:
            # response is already set by above function
            return
        self.archive_current_message(MailBotMessageCategory.LUCENE_ALERT)
        self.response = MailOperationStatus.AUTO_ARCHIVE_CANCEL_SUCCESS.value, {}

    def cancel_lucene_alert_for_sender(self):
        self.cancel_lucene_alert()
        self.sender_profile.metadata["lucene_alert_archive_cancelled"] = True
        self.sender_profile.save()
        self.response = MailOperationStatus.SENDER_AUTO_ARCHIVE_PREFERENCE_UPDATE_SUCCESS.value, {
            "sender_email": self.sender_email
        }

    def cancel_lucene_alert_for_domain(self):
        self.cancel_lucene_alert()
        domain_training, _ = DomainTraining.objects.get_or_create(
            user_mailbot_profile=self.user_mailbot_profile, sender_domain=self.sender_domain
        )
        if domain_training.metadata.get("lucene_alert_archive_cancelled"):
            self.response = MailOperationStatus.PREFERENCE_ALREADY_UPDATED.value, {}
            return
        domain_training.metadata["lucene_alert_archive_cancelled"] = True
        domain_training.save()
        self.response = MailOperationStatus.DOMAIN_AUTO_ARCHIVE_PREFERENCE_UPDATE_SUCCESS.value, {
            "sender_domain": self.sender_domain
        }

    def process_mail_operation(self) -> Dict[str, Any]:
        """
        Process the payload received when user interacts with mailbot sent emails.

        Returns:
            dict: Status code to display specific message, context to populate dynamic fields for that message
        """
        if self.response[0] == MailOperationStatus.ORIGINAL_MESSAGE_NOT_FOUND.value:
            # Return if original message is not found during initialisation,
            # as currently all the following operations require that original message must be present in database
            return {"mail_operation_status": self.response[0], "mail_operation_context": self.response[1]}
        operation = self.payload.pop("operation")
        operation_type = MailOperationType(operation)
        analytics_event_metadata = {
            "mail_operation_type": None,
            "message_id": self.message_id,
            "message_received_at": self.message.received_at.isoformat(),
        }
        if check_feature(
            user_id=self.user_mailbot_profile.user_id,
            feature_flag=MailBotFeatureFlag.DIGEST_EMAIL,
            application_tag=ApplicationTag.MailBot,
        ):
            if operation_type == MailOperationType.TRAINING_THROUGH_DIGEST:
                self.training_through_digest()
                analytics_event_metadata["mail_operation_type"] = "training_through_digest_email"
        if check_feature(
            user_id=self.user_mailbot_profile.user_id,
            feature_flag=MailBotFeatureFlag.FIRST_TIME_SENDER_OVERLAY,
            application_tag=ApplicationTag.MailBot,
        ):
            if operation_type == MailOperationType.TRAINING_THROUGH_FIRST_TIME_SENDER:
                self.user_training_through_first_time_sender_overlay()
                analytics_event_metadata["mail_operation_type"] = "sender_training_through_first_time_sender_overlay"
            elif operation_type == MailOperationType.DOMAIN_TRAINING_THROUGH_FIRST_TIME_SENDER:
                self.domain_training_through_first_time_sender_overlay()
                analytics_event_metadata["mail_operation_type"] = "domain_training_through_first_time_sender_overlay"
            elif operation_type == MailOperationType.PREFERENCES_UPDATE_FOR_FIRST_TIME_SENDER:
                self.update_preference_for_first_time_sender_domain()
                analytics_event_metadata["mail_operation_type"] = "preferences_update_for_first_time_sender"
        if check_feature(
            user_id=self.user_mailbot_profile.user_id,
            feature_flag=MailBotFeatureFlag.ARCHIVE_WITH_OVERLAY,
            application_tag=ApplicationTag.MailBot,
        ):
            if operation_type == MailOperationType.CANCEL_LUCENE_ALERT:
                self.cancel_lucene_alert()
                analytics_event_metadata["mail_operation_type"] = "cancel_lucene_alert"
            elif operation_type == MailOperationType.CANCEL_LUCENE_ALERT_FOR_SENDER:
                self.cancel_lucene_alert_for_sender()
                analytics_event_metadata["mail_operation_type"] = "cancel_lucene_alert_for_sender"
            elif operation_type == MailOperationType.CANCEL_LUCENE_ALERT_FOR_DOMAIN:
                self.cancel_lucene_alert_for_domain()
                analytics_event_metadata["mail_operation_type"] = "cancel_lucene_alert_for_domain"
        if not self.response[0]:
            self.response = MailOperationStatus.OPERATION_NOT_SUPPORTED.value, {}
        AnalyticsEvents.create_analytics_event(
            event_type=AnalyticsEvents.EVENT_TYPE_MAIL_OPERATION_EXECUTED,
            user_mailbot_profile_id=self.user_mailbot_profile.id,
            metadata=analytics_event_metadata,
        )
        return {"mail_operation_status": self.response[0], "mail_operation_context": self.response[1]}
