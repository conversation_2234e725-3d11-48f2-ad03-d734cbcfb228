from constance import config as constance_config

from mailbot.models import UserMailBotProfile
from mailbot.utils.subscription import get_active_subscription


def show_pricing_v1(user_mailbot_profile: UserMailBotProfile) -> bool:
    """
    V1 pricing are mailbot_monthly ($1 for first month, $5 afterward), mailbot_annual ($36 per year).
    """
    if user_mailbot_profile.user.date_joined.date() < constance_config.PRICES_V2_DATE:
        return True
    subscription = get_active_subscription(user_mailbot_profile, check_cancel_at_period_end=False)
    if not subscription:
        return False
    return subscription.price.nickname in ("mailbot_monthly", "mailbot_annual")


def show_pricing_v2(user_mailbot_profile: UserMailBotProfile, check_old_versions=True) -> bool:
    """
    V2 pricing are freebie (free trial for 7 days), basic ($9.99 per month) and power ($99.99 per year).
    """
    if check_old_versions and show_pricing_v1(user_mailbot_profile):
        return False
    if user_mailbot_profile.user.date_joined.date() < constance_config.PRICES_V3_DATE:
        subscription = get_active_subscription(user_mailbot_profile, check_cancel_at_period_end=False)
        if not subscription:
            return False
        # Even if user joined before V3 date, we will show them V3 or later pricing if they have not upgraded to any paid pricing
        if subscription.price.nickname in ("basic", "power") and subscription.discounts.count() == 0:
            return True
    return False


def show_pricing_v3(user_mailbot_profile: UserMailBotProfile, check_old_versions=True) -> bool:
    """
    V3 pricing are freebie (free trial for 7 days), basic (with discount of 25%, i.e., $7.49 per month) and power (with discount of 40%, i.e., $59.99 per month).
    """
    if check_old_versions and (show_pricing_v1(user_mailbot_profile) or show_pricing_v2(user_mailbot_profile)):
        return False
    if user_mailbot_profile.user.date_joined.date() < constance_config.PRICES_V4_DATE:
        return True
    subscription = get_active_subscription(user_mailbot_profile, check_cancel_at_period_end=False)
    if not subscription:
        return False
    if subscription.price.nickname == "freebie":
        return True
    discount = subscription.discounts.select_related("coupon").first()
    if not discount:
        return False
    return discount.coupon.name in ("emailzap_basic", "emailzap_power")


def show_pricing_v4(user_mailbot_profile: UserMailBotProfile, check_old_versions=True) -> bool:
    """
    V4 pricing are basic ($1 for first month, $9.99 afterward) and power_v2 ($120 per year with discount of $21, same as power but to show user that we are giving discounted price).
    """
    if check_old_versions and (
        show_pricing_v1(user_mailbot_profile)
        or show_pricing_v2(user_mailbot_profile)
        or show_pricing_v3(user_mailbot_profile)
    ):
        return False

    subscription = get_active_subscription(user_mailbot_profile, check_cancel_at_period_end=False)
    if not subscription:
        return False

    if subscription.price.nickname in ["power_v2"]:
        return True

    return user_mailbot_profile.user.date_joined.date() < constance_config.PRICES_V5_DATE


def show_pricing_v5(user_mailbot_profile: UserMailBotProfile, check_old_versions=True) -> bool:
    """
    V5 pricing are basic ($1 for first month, $9.99 afterward) and power_v3 ($120 per year with discount of $50, same as power but to show user that we are giving discounted price).
    """
    if check_old_versions and (
        show_pricing_v1(user_mailbot_profile)
        or show_pricing_v2(user_mailbot_profile)
        or show_pricing_v3(user_mailbot_profile)
        or show_pricing_v4(user_mailbot_profile)
    ):
        return False

    subscription = get_active_subscription(user_mailbot_profile, check_cancel_at_period_end=False)
    if not subscription:
        return False

    if subscription.price.nickname in ["basic", "power_v3"]:
        return True
    return user_mailbot_profile.user.date_joined.date() < constance_config.PRICES_V6_DATE


def show_pricing_v6(user_mailbot_profile: UserMailBotProfile, check_old_versions=True) -> bool:
    """
    V6 pricing are basic_v4 ($1 for first month, $7.5 afterward) and power_v4 ($90 per year with discount of $40, same as power but to show user that we are giving discounted price).
    """
    if check_old_versions and (
        show_pricing_v1(user_mailbot_profile)
        or show_pricing_v2(user_mailbot_profile)
        or show_pricing_v3(user_mailbot_profile)
        or show_pricing_v4(user_mailbot_profile)
        or show_pricing_v5(user_mailbot_profile)
    ):
        return False

    return True


def get_pricing_version(user_mailbot_profile: UserMailBotProfile) -> str:
    if show_pricing_v1(user_mailbot_profile):
        return "v1"
    elif show_pricing_v2(user_mailbot_profile, check_old_versions=False):
        return "v2"
    elif show_pricing_v3(user_mailbot_profile, check_old_versions=False):
        return "v3"
    elif show_pricing_v4(user_mailbot_profile, check_old_versions=False):
        return "v4"
    elif show_pricing_v5(user_mailbot_profile, check_old_versions=False):
        return "v5"
    elif show_pricing_v6(user_mailbot_profile, check_old_versions=False):
        return "v6"
    else:
        raise ValueError("No pricing version matches for current user")
