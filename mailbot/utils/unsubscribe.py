import logging

import requests
from django.db import transaction

from applications.exceptions import FailedToSendEmailException
from applications.models import EmailTemplate
from mailbot.models import SenderUnsubscribeDetail
from mailbot.utils.defaults import MailBotTemplateTag

logger = logging.getLogger(__name__)


def unsubscribe_email(sender_unsubscribe_detail_id):
    """
    Unsubscribe the user from the given sender_unsubscribe_detail_id

    Args:
        sender_unsubscribe_detail_id (int): The sender_unsubscribe_detail id
    """
    with transaction.atomic():
        sender_unsubscribe_detail = SenderUnsubscribeDetail.objects.select_for_update(of=("self",)).get(
            id=sender_unsubscribe_detail_id
        )
        if sender_unsubscribe_detail.unsubscribed:
            logger.info(f"Unsubscribe detail: {sender_unsubscribe_detail.id} already unsubscribed")
            return
        # TODO: Extend the support for non one-click unsubscribe links which will require user confirmation on
        #  landing pages
        if sender_unsubscribe_detail.unsubscribe_link and sender_unsubscribe_detail.unsubscribe_link_one_click:
            # https://datatracker.ietf.org/doc/html/rfc8058 If unsubscribe link is one-click then the sender must
            # process requests with single click with POST or GET request (majorly POST requests) without any
            # authorization, confirmation, or cookies required.
            try:
                response = requests.post(sender_unsubscribe_detail.unsubscribe_link, timeout=10)
                response.raise_for_status()
            except requests.RequestException:
                try:
                    # If the POST request fails, try GET request
                    response = requests.get(sender_unsubscribe_detail.unsubscribe_link, timeout=10)
                    response.raise_for_status()
                except requests.RequestException:
                    logger.info(
                        f"Failed to unsubscribe from {sender_unsubscribe_detail.unsubscribe_link} using unsubscribe link"
                    )
                else:
                    logger.info(
                        f"Unsubscribed from {sender_unsubscribe_detail.unsubscribe_link} using GET unsubscribe link"
                    )
            else:
                logger.info(
                    f"Unsubscribed from {sender_unsubscribe_detail.unsubscribe_link} using POST unsubscribe link"
                )
        if sender_unsubscribe_detail.unsubscribe_mail_to:
            template = EmailTemplate.objects.get(tag=MailBotTemplateTag.UNSUBSCRIBE_SENDER_MAIL.value)
            to, subject, text_body = sender_unsubscribe_detail.metadata.get("unsubscribe_link_mail_to_parsed")
            try:
                message_id = template.send_email(
                    user=sender_unsubscribe_detail.user_mailbot_profile.user,
                    to=to,
                    subject=subject,
                    context={
                        "text_body": text_body,
                    },
                )
            except FailedToSendEmailException:
                logger.info(
                    f"Failed to unsubscribe from {sender_unsubscribe_detail.unsubscribe_mail_to} using unsubscribe email"
                )
            else:
                logger.info(
                    f"Unsubscribed from {sender_unsubscribe_detail.unsubscribe_mail_to} using unsubscribe email"
                )
        # We will unsubscribe the sender profile in any case as we will continue to block emails from the same
        # unsubscribe link in the future through our label engine
        sender_unsubscribe_detail.unsubscribed = True
        sender_unsubscribe_detail.save(update_fields=["unsubscribed"])
