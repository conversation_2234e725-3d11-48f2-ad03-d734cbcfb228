import base64
import logging
from functools import lru_cache

from django.core.mail import EmailMultiAlternatives
from langchain.chains import LL<PERSON>hain
from langchain.chat_models import ChatOpenAI
from langchain.text_splitter import TokenTextSplitter
from langchain_core.prompts import PromptTemplate

from execfn.common.utils.search import isearch_key
from mailbot.models import Message
from mailbot.service.gmail import MailBoxPaginator, GmailService

logger = logging.getLogger(__name__)

USER_WRITING_STYLE_ANALYSIS_PROMPT = """
You are an expert email writer who writes emails on behalf of users. Before you write email, you analyse their past 
emails to understand writing style. Once you understand their writing style, you provide writing style information in 
one sentence it in this format:
Tone:
Voice:
Vocabulary and Diction:
Sentence Structure and Syntax:
Pacing:
Perspective:
Paragraph Structure and Flow:
Formality:
Use of Humor and Satire:
Clarity and Precision:
Rhythm and Cadence:

Email(s):
{emails}

Analysis:
"""

DRAFT_RESPONSE_PROMPT = """
You are an expert email writer who will draft an email for a user based on the user's command, context in the received 
email and use user's writing style while drafting the email. Do not include the subject in the draft.

User's command:
{user_query}

User's writing style:
{writing_style}

Email Received:
{thread_messages}

Drafted Response:
"""


@lru_cache()
def analyze_emails_for_writing_style(user_mailbot_profile, sender_emails=None):
    sent_emails_qs = Message.objects.filter(
        user_mailbot_profile=user_mailbot_profile, generic_label=Message.GENERIC_LABEL_SENT
    )
    # TODO : remove emails which are empty or have less than 50 characters
    if sender_emails:
        sender_emails = sender_emails.split(";")
        unique_sent_messages = (
            sent_emails_qs.filter(metadata__to__1__in=sender_emails)
            .order_by("-created")[:20]
            .values_list("message_id", flat=True)
        )
    else:
        unique_sent_messages = (
            sent_emails_qs.distinct("metadata__to")
            .order_by("metadata__to", "-created")[:20]
            .values_list("message_id", flat=True)
        )
    service = GmailService(user_mailbot_profile)
    parsed_message = service.get_messages(list(unique_sent_messages))
    sent_email_bodies = [x.text_body for x in parsed_message]
    llm = ChatOpenAI(model="gpt-4o", temperature=0.7)
    prompt = PromptTemplate(template=USER_WRITING_STYLE_ANALYSIS_PROMPT, input_variables=["emails"])
    chain = LLMChain(llm=llm, prompt=prompt)
    email_text = "\n\n".join(sent_email_bodies)
    text_splitter = TokenTextSplitter(chunk_size=120000, chunk_overlap=0)
    chunks = text_splitter.split_text(email_text)
    results = []
    for chunk in chunks:
        result = chain.run(emails=chunk)
        results.append(result)
    return "\n\n".join(results)


def draft_response(email_analysis, new_thread, user_query):
    llm = ChatOpenAI(model="gpt-4o", temperature=0.2)
    draft_prompt = PromptTemplate(
        template=DRAFT_RESPONSE_PROMPT, input_variables=["user_query", "writing_style", "thread_messages"]
    )
    # Create an LLMChain for drafting a response
    draft_chain = LLMChain(llm=llm, prompt=draft_prompt)
    text_splitter = TokenTextSplitter(chunk_size=120000, chunk_overlap=0)
    chunks = text_splitter.split_text(new_thread)
    results = []
    for chunk in chunks:
        result = draft_chain.run(writing_style=email_analysis, thread_messages=chunk, user_query=user_query)
        results.append(result)
    return "\n\n".join(results)


def draft_gmail_message(user_mailbot_profile, message_id):
    user = user_mailbot_profile.user
    gmail_service = GmailService(user_mailbot_profile=user_mailbot_profile)
    # Get the details of draft message. The draft ID is different from the message ID received in the webhook
    parsed_message = gmail_service.get_message(message_id)
    paginator = MailBoxPaginator(
        limit=500,
        mailbox_function=gmail_service.mailbox.list_draft_messages,
        message_kwargs={},
        response_data_mapper=lambda _response: list(
            map(lambda x: {x["message"]["id"]: x["id"]}, _response.pop("drafts", []))
        ),
    )
    draft_id = None
    while True:
        for draft in paginator.iterate_batch(10):
            if draft_id := draft.get(message_id):
                break
        if draft_id:
            break
    if not draft_id:
        logger.exception(f"Failed to find draft ID for message {message_id} and user {user.email}")
        return
    # Find the content of the last message in thread where this draft was created. We will pass on this message to the
    # LLM for drafting the response
    if thread_id := parsed_message.thread_id:
        thread_message_ids = gmail_service.mailbox.get_message_ids_from_thread(thread_id)
        parsed_thread_messages = gmail_service.get_messages(thread_message_ids)
        thread_messages = "\n\n".join(x.text_body for x in parsed_thread_messages)
    else:
        thread_messages = "There are no thread messages for this draft."
    sender_emails = ";".join([x[1] for x in parsed_message.to_name_email])
    writing_style = analyze_emails_for_writing_style(user_mailbot_profile, sender_emails)
    draft_message = draft_response(writing_style, thread_messages, parsed_message.text_body)
    # Prepare the updated draft email object
    email = EmailMultiAlternatives(
        subject=parsed_message.subject,
        body=draft_message,
        to=parsed_message.to_name_email,
        cc=parsed_message.cc_name_email,
        bcc=parsed_message.bcc_name_email,
    )
    if in_reply_to_message_id := isearch_key(parsed_message.message_headers, "In-Reply-To"):
        email.extra_headers["In-Reply-To"] = in_reply_to_message_id
        email.extra_headers["References"] = in_reply_to_message_id
    raw_message = {
        "id": draft_id,
        "message": {
            "raw": base64.urlsafe_b64encode(email.message().as_bytes()).decode("utf-8"),
        },
    }
    if thread_id:
        raw_message["message"]["threadId"] = thread_id
    # Update draft email
    request = gmail_service.mailbox.service.users().drafts().update(userId=user.email, id=draft_id, body=raw_message)
    executed, response = gmail_service.mailbox.http_execute_safe(request)
    if executed:
        logger.info("Successfully updated draft message.")
    else:
        logger.exception("Failed to update draft message.")
