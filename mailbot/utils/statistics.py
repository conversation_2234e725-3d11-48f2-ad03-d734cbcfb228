import logging

from django.db import transaction

from execfn.common.utils.cache import cache_lock
from execfn.common.utils.request import resolve_and_fetch_view_data
from mailbot.models import UserMailBotAnalytics, MailBotUsageLog
from mailbot.utils.subscription import get_active_subscription
from mailbot.utils.defaults import AnalyticsStatistics<PERSON>ey, MailBotProfileMetadataKey
from mailbot.utils.pusher import SenderProfileChannel, SenderProfileEvents

logger = logging.getLogger(__name__)


def update_mailbot_usage(user_mailbot_profile, key, value):
    subscription = get_active_subscription(user_mailbot_profile)
    with cache_lock(key=f"mailbot:usage:{user_mailbot_profile.id}", lock_timeout=60, max_blocking_time=10) as locked:
        if locked:
            with transaction.atomic():
                try:
                    mailbot_usage_log = MailBotUsageLog.objects.select_for_update(of=("self",)).get(
                        user_mailbot_profile=user_mailbot_profile,
                        subscription_period_start=subscription.current_period_start,
                    )
                except MailBotUsageLog.DoesNotExist:
                    MailBotUsageLog.objects.create(
                        user_mailbot_profile=user_mailbot_profile,
                        subscription_period_start=subscription.current_period_start,
                        metadata={key: value},
                    )
                else:
                    if key not in mailbot_usage_log.metadata:
                        mailbot_usage_log.metadata[key] = 0
                    mailbot_usage_log.metadata[key] += value
                    mailbot_usage_log.save(update_fields=("metadata",))
        else:
            logger.error("Lock could not be acquired for mailbot usage log")


def update_statistics_in_analytics(user_mailbot_profile, key, value):
    with transaction.atomic():
        analytics, _ = UserMailBotAnalytics.objects.select_for_update(of=("self",)).get_or_create(
            user_mailbot_profile=user_mailbot_profile
        )
        if key not in analytics.statistics:
            analytics.statistics[key] = 0
        analytics.statistics[key] += value
        analytics.save()
        if key == AnalyticsStatisticsKey.MESSAGES_SCANNED.value:
            transaction.on_commit(
                lambda: send_auto_cleaner_data(
                    user_mailbot_profile=analytics.user_mailbot_profile,
                    messages_scanned=analytics.statistics[key],
                )
            )


def send_auto_cleaner_data(user_mailbot_profile, messages_scanned):
    if (
        not user_mailbot_profile.metadata.get(MailBotProfileMetadataKey.ONBOARDING_COMPLETED.value)
        and messages_scanned <= 5000
    ):
        auto_cleaner_data = resolve_and_fetch_view_data(
            user=user_mailbot_profile.user,
            url_path="/api/v1/mailbot/auto-cleaner/",
            query_params={
                "limit": 20,
                "offset": 0,
                "ordering": "-total_count,sender_email",
            },
        )
        statistics_data = resolve_and_fetch_view_data(
            user=user_mailbot_profile.user,
            url_path="/api/v1/mailbot/statistics/all/",
        )
        SenderProfileChannel().trigger(
            user_id=user_mailbot_profile.user_id,
            event_name=SenderProfileEvents.DATA,
            data={
                "sender_profiles": auto_cleaner_data,
                "statistics_data": statistics_data,
            },
            messages_scanned=messages_scanned,
        )
