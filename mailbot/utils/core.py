from itertools import chain
import logging
from typing import Any, Dict, <PERSON><PERSON>, Optional
from django.db.models.signals import post_save
from constance import config as constance_config
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from mailbot.utils.check import should_forward_overlay
from mailbot.algorithms.utils import get_algorithm_instance
from mailbot.utils.statistics import update_statistics_in_analytics
from applications.exceptions import FailedToSendEmailException
from applications.feature_flags.check import check_feature
from applications.feature_flags.defaults import MailBotFeatureFlag
from applications.models import Application, EmailTemplate, SentEmails, ScheduledTask
from applications.utils.email import get_email_domain, get_normalized_email
from execfn import ApplicationTag
from mailbot.models import (
    Message,
    MailBotGenericLabel,
    DomainTraining,
    MessageCategoryThrough,
    SenderProfile,
    SenderUnsubscribeDetail,
    UserMailBotProfile,
)
from mailbot.service.base import BaseMessageService
from mailbot.service.gmail import GmailService
from mailbot.service.outlook import OutlookService
from mailbot import signals
from mailbot.signals import mute_signal
from mailbot.utils import database
from mailbot.utils.defaults import (
    InboxOverlayLabel,
    MailBotMessageHeaders,
    MailBotTemplateTag,
    MailBotMessageCategory,
    MailBotMessageLabelReason,
    MailBotScheduledTasks,
    GmailKnownLabelName,
    OutlookKnownLabelName,
    AnalyticsStatisticsKey,
)
from mailbot.utils.exceptions import LabelIdNotFoundError
from mailbot.utils.label_engine import MAILBOT_LABELING_RULES_REGISTRY
from mailbot.utils.label_engine.rules.rule_driver import RuleDriver
from mailbot.utils.message_parser import ParsedMessage
from mailbot.utils.statistics import update_statistics_in_analytics
from mailbot.utils.unsubscribe import unsubscribe_email
from mailbot.utils.overlays.service import OverlayService

logger = logging.getLogger(__name__)
User = get_user_model()


class MailBotUserCore:
    """
    MailBot user specific core functionalities
    """

    def __init__(self, service: BaseMessageService):
        self.service = service
        self.is_outlook_service = isinstance(self.service, OutlookService)
        self.is_gmail_service = isinstance(self.service, GmailService)
        self.application = Application.objects.get(tag=ApplicationTag.MailBot.value)

    def send_welcome_mail(self):
        """
        Send welcome mail for the current user if not already sent.
        """
        # Check if onboarding mails are sent
        is_welcome_mail_sent = SentEmails.objects.filter(
            user=self.service.user,
            template__tag=MailBotTemplateTag.WELCOME.value,
            template__application=self.application,
        ).exists()
        if is_welcome_mail_sent:
            logger.info(
                f"Skipping process of sending welcoming mail as it is already sent for user: {self.service.email}."
            )
            return
        first_name = self.service.user.first_name
        frontend_base_url = settings.FRONTEND_BASE_URL
        try:
            EmailTemplate.send_email_using_template(
                application=self.application,
                user=self.service.user,
                tag=MailBotTemplateTag.WELCOME.value,
                to=self.service.user.email,
                context={"name": first_name, "frontend_base_url": frontend_base_url},
                headers={MailBotMessageHeaders.MESSAGE_CATEGORY.value: MailBotMessageCategory.WELCOME.value},
            )
        except FailedToSendEmailException:
            logger.exception("Welcome mail delivery failed", extra={"user": self.service.email})
        else:
            logger.info(f"Welcome mail sent for user {self.service.email}")

    @mute_signal(post_save, "mailbot.receivers.increment_processed_count", sender=Message)
    def perform_onboarding_tasks(self):
        """Initial steps to run for emailzap when user first signs up."""

        logger.info(f"Performing onboarding tasks for user {self.service.email}")
        self.service.create_default_labels()
        self.service.sync_label_mappings()
        self.send_welcome_mail()
        logger.info("Scanning white_list emails")
        white_list_messages = self.service.scan_messages(
            generic_label=MailBotGenericLabel.WHITE_LIST.value,
            limit=5000,
            message_mapper=self.service.sync_with_database,
        )
        update_statistics_in_analytics(
            user_mailbot_profile=self.service.user_mailbot_profile,
            key=AnalyticsStatisticsKey.MESSAGES_SCANNED.value,
            value=len(white_list_messages),
        )

        logger.info("Scanning zapped emails")
        zapped_messages = self.service.scan_messages(
            generic_label=MailBotGenericLabel.ZAPPED.value,
            limit=5000,
            message_mapper=self.service.sync_with_database,
        )
        update_statistics_in_analytics(
            user_mailbot_profile=self.service.user_mailbot_profile,
            key=AnalyticsStatisticsKey.MESSAGES_SCANNED.value,
            value=len(zapped_messages),
        )

        logger.info("Scanning sent emails")
        sent_messages = self.service.scan_messages(
            generic_label=Message.GENERIC_LABEL_SENT, message_mapper=self.service.sync_with_database
        )
        update_statistics_in_analytics(
            user_mailbot_profile=self.service.user_mailbot_profile,
            key=AnalyticsStatisticsKey.MESSAGES_SCANNED.value,
            value=len(sent_messages),
        )

        # Analyze and categorize the message from white_list and zapped scans
        logger.info("Analyzing and categorizing the message from white_list and zapped scans")
        all_scanned_message_ids = list(
            chain((msg.message_id for msg in white_list_messages), (msg.message_id for msg in zapped_messages))
        )
        signals.scan_completed.send(
            sender=self.__class__,
            profile_id=self.service.user_mailbot_profile.id,
            message_ids=all_scanned_message_ids,
        )

        self.service.subscribe_watch_events()

    def cancel_auto_responder(self, sender_email):
        """
        If user trained the message to whitelist, then we cancel scheduled mailbot_auto_responder for that sender if present.
        Args:
            sender_email: Sender email corresponding to which cancel auto responder if scheduled.
        """
        try:
            task = ScheduledTask.objects.get(
                user=self.service.user,
                metadata__sender_email=sender_email,
                periodic_task__task=MailBotScheduledTasks.SEND_AUTO_RESPONDER_MAIL.value,
                periodic_task__enabled=True,
            )
            task.disable()
            logger.info(f"Cancelled auto responder for user {self.service.email}, sender {sender_email}")
        except ScheduledTask.DoesNotExist:
            pass
        except ScheduledTask.MultipleObjectsReturned:
            logger.exception(
                f"Multiple auto responders scheduled for user.",
                extra={"user": self.service.user.email, "sender": sender_email},
            )

    def cancel_auto_responder_for_domain(self, sender_domain):
        """
        If user trained the domain to whitelist, then we cancel scheduled mailbot_auto_responder for that domain if present.
        Args:
            sender_domain: Sender domain corresponding to which cancel auto responders if scheduled.
        """
        tasks = ScheduledTask.objects.filter(
            user=self.service.user,
            metadata__sender_email__endswith=sender_domain,
            periodic_task__task=MailBotScheduledTasks.SEND_AUTO_RESPONDER_MAIL.value,
            periodic_task__enabled=True,
        )

        for task in tasks:
            task.disable()

        logger.info(f"Cancelled auto responders for user {self.service.email}, domain {sender_domain}")

    def update_user_training(self, sender_email: str, user_training: str):
        """
        Update user training in the database and if training is for white_list then cancel scheduled auto responders.
        Args:
            sender_email: Sender's email address
            user_training: User training generic label name
        """
        database.update_user_training(self.service.user_mailbot_profile.id, sender_email, user_training)
        if user_training == MailBotGenericLabel.WHITE_LIST.value:
            self.cancel_auto_responder(sender_email=sender_email)

    def update_domain_training(self, domain_training: DomainTraining, domain_training_label: str):
        """
        Update the domain training label in database.
        Args:
            domain_training: DomainTraining object for current user and sender's domain.
            domain_training_label: Label which user want's domain messages move to.
        """
        logger.info(
            f"Domain training for user {self.service.email}, domain {domain_training.sender_domain}, "
            f"trained label {domain_training_label}"
        )
        domain_training.label_name = domain_training_label
        domain_training.trained_at = timezone.now()
        domain_training.save()
        if domain_training_label == MailBotGenericLabel.WHITE_LIST.value:
            self.cancel_auto_responder_for_domain(sender_domain=domain_training.sender_domain)


class MailBotMessageCore:
    """MailBot message specific core functionalities"""

    def __init__(self, service: BaseMessageService, parsed_message: ParsedMessage, db_message: Message = None):
        """
        Initialise mail bot message specific functionalities
        Args:
            service (BaseMessageService): Base message service to use for message processing
            parsed_message (ParsedMessage): Parsed message for processing
        """
        self.application = Application.objects.get(tag=ApplicationTag.MailBot.value)
        self.service = service
        self.is_outlook_service = isinstance(self.service, OutlookService)
        self.is_gmail_service = isinstance(self.service, GmailService)
        self.parsed_message = parsed_message
        self.user_email = self.service.email
        self.sender_name, self.sender_email = parsed_message.from_name_email
        self.normalized_sender_email = get_normalized_email(self.sender_email)
        self.sender_profile = SenderProfile.objects.get(
            normalized_sender_email=self.normalized_sender_email, user_mailbot_profile=self.service.user_mailbot_profile
        )
        self.sender_domain = get_email_domain(self.sender_email)
        self.user_mailbot_preferences = self.service.user_mailbot_profile.preferences
        self.db_message = db_message
        self.overlay_service = OverlayService(
            service=self.service,
            db_message=self.db_message,
            parsed_message=self.parsed_message,
        )
        self.algorithm = get_algorithm_instance(user_profile=self.service.user_mailbot_profile)

    def compute_should_send_fts_overlay_and_options(self) -> Tuple[bool, Optional[Dict[str, bool]]]:
        """
        Compute whether to send overlay and options to show in it based on domain generality and user's past interactions with
        first time sender overlays for non-general domains.
        Returns:
             Tuple[bool, Optional[Dict[str, bool]]]: Tuple of whether to send overlay and options to show in overlay.
        """

        # For Gmail message of category promotions then don't trigger first time sender overlay
        if (
            self.service.user_mailbot_profile.service_provider == settings.SERVICE_PROVIDER_GOOGLE
            and GmailKnownLabelName.CATEGORY_PROMOTIONS.value in self.parsed_message.label_ids
        ):
            logger.info("Skip sending first time sender overlay due to promotional email")
            return False, None
        options = {
            "move_sender_to_inbox": False,
            "move_domain_to_inbox": False,
            "send_alerts_for_domain": False,
        }

        if self.sender_domain in settings.GENERAL_EMAIL_DOMAINS:
            options["move_sender_to_inbox"] = True
            return True, options
        # For non-general domains, compute what options to show based on past overlays sent for same domain and user's interaction with them.
        try:
            domain_training = DomainTraining.objects.get(
                user_mailbot_profile=self.service.user_mailbot_profile, sender_domain=self.sender_domain
            )
        except DomainTraining.DoesNotExist:
            # First time sending first time sender alert for this domain
            options["move_sender_to_inbox"] = True
            options["move_domain_to_inbox"] = True
            # As we don't send first time sender overlay for non-general domain more than once,
            # user has the option to mark `send_alerts_for_domain`.
            options["send_alerts_for_domain"] = True
            return True, options
        else:
            if self.algorithm.should_send_fts_overlay() or domain_training.metadata.get("send_alerts_for_domain"):
                options["move_sender_to_inbox"] = True
                options["move_domain_to_inbox"] = True
                # So we toggle the preference here.
                options["send_alerts_for_domain"] = not domain_training.metadata.get("send_alerts_for_domain", True)
                return True, options
            else:
                # User has not interacted with current sender domain overlay yet or choose to disable domain alerts.
                # Therefore, we don't flood their inbox for same non-general domain with first time sender overlay.
                logger.info("Skip sending first time sender overlay due to no send_alerts_for_domain preference")
                return False, None

    def get_label_for_new_mail(
        self,
        message_categories=None,
    ) -> Dict[str, Any]:
        """
        Get a label for a new mail using various filters.

        Args:
            message_categories: Optional message categories to use for labeling rules.

        Returns:
            Dict[str, Any]: Mapping containing label name, labeling reason and related fields.
        """
        rule_driver = RuleDriver(state=self.parsed_message, rules=MAILBOT_LABELING_RULES_REGISTRY["main"])
        return rule_driver.sort_by_priorities().apply_first(
            message_categories=message_categories, algorithm=self.algorithm
        )

    def add_archiving_emailzap_label(self, remove_from_inbox_instantly: bool = False):
        """
        Add emailzap archive label to the message.

        Args:
            remove_from_inbox_instantly: Whether to remove the message from the inbox or not while adding the archive label.
        """
        if not check_feature(
            user_id=self.service.user.id,
            feature_flag=MailBotFeatureFlag.ARCHIVING_EMAILZAP,
            application_tag=ApplicationTag.MailBot,
        ):
            return
        if self.is_outlook_service:
            if not remove_from_inbox_instantly:
                # Move from inbox to archive folder after x minutes
                start_time = timezone.now() + timezone.timedelta(
                    minutes=constance_config.ARCHIVING_EMAILZAP_SETTINGS["remove_inbox_after"]
                )
                ScheduledTask.create_one_off_task(
                    user=self.service.user,
                    start_time=start_time,
                    task=MailBotScheduledTasks.UPDATE_LABELS.value,
                    task_kwargs={
                        "message_id": self.parsed_message.message_id,
                        "remove_label_name": None,
                        "add_label_name": OutlookKnownLabelName.ARCHIVE.value,
                    },
                    metadata={
                        "message_id": self.parsed_message.message_id,
                        "add_label_name": OutlookKnownLabelName.ARCHIVE.value,
                    },
                )
            else:
                logger.info(
                    f"Moving message {self.parsed_message.message_id} to archive folder for user {self.user_email}"
                )
                self.service.move_to_known_label(
                    self.parsed_message.message_id, generic_label=Message.GENERIC_LABEL_ARCHIVE
                )
        elif self.is_gmail_service:
            logger.info(f"Adding ARCHIVING_EMAILZAP label to message {self.parsed_message.message_id}")
            try:
                label_id = self.service.get_label_id(GmailService.ARCHIVING_EMAILZAP)
                if not label_id:
                    label_id = self.service.create_label(GmailService.ARCHIVING_EMAILZAP)
                    self.service.sync_label_mappings()
                self.service.mailbox.update_labels(
                    self.parsed_message.message_id,
                    remove_label_ids=[GmailKnownLabelName.INBOX.value] if remove_from_inbox_instantly else None,
                    add_label_ids=[label_id],
                )
            except LabelIdNotFoundError:
                # Label id changed or deleted from user's mailbox, clear cache and try again
                self.service.get_label_id.cache_clear()
                self.service.sync_label_mappings()
                label_id = self.service.get_label_id(GmailService.ARCHIVING_EMAILZAP)
                if not label_id:
                    label_id = self.service.create_label(GmailService.ARCHIVING_EMAILZAP)
                    self.service.sync_label_mappings()
                self.service.mailbox.update_labels(
                    self.parsed_message.message_id,
                    remove_label_ids=[GmailKnownLabelName.INBOX.value] if remove_from_inbox_instantly else None,
                    add_label_ids=[label_id],
                )
            # Remove archive label after (default 7 days)
            start_time = timezone.now() + timezone.timedelta(
                minutes=constance_config.ARCHIVING_EMAILZAP_SETTINGS["remove_archive_after"]
            )
            ScheduledTask.create_one_off_task(
                user=self.service.user,
                start_time=start_time,
                task=MailBotScheduledTasks.UPDATE_LABELS.value,
                task_kwargs={
                    "message_id": self.parsed_message.message_id,
                    "remove_label_name": GmailService.ARCHIVING_EMAILZAP,
                },
                metadata={
                    "message_id": self.parsed_message.message_id,
                    "remove_label_name": GmailService.ARCHIVING_EMAILZAP,
                },
            )
            if not remove_from_inbox_instantly:
                # Remove inbox after x minutes
                start_time = timezone.now() + timezone.timedelta(
                    minutes=constance_config.ARCHIVING_EMAILZAP_SETTINGS["remove_inbox_after"]
                )
                ScheduledTask.create_one_off_task(
                    user=self.service.user,
                    start_time=start_time,
                    task=MailBotScheduledTasks.UPDATE_LABELS.value,
                    task_kwargs={
                        "message_id": self.parsed_message.message_id,
                        "remove_label_name": GmailKnownLabelName.INBOX.value,
                    },
                    metadata={
                        "message_id": self.parsed_message.message_id,
                        "remove_label_name": GmailKnownLabelName.INBOX.value,
                    },
                )

    def take_action_post_move(self, result: Dict[str, Any]):
        """
        Take actions based on labels assigned to message like triggering overlays, calendar invites if calendar tag, etc.
        Args:
            result: Populated response from passed action and filter.
        """
        if result.get("message_labelled_due_to") == MailBotMessageLabelReason.FIRST_TIME_SENDER_FILTER.value:
            if result.get("first_time_sender_treatment") == "low_priority_with_overlay":
                if (
                    result["label_name"] == MailBotGenericLabel.WHITE_LIST.value
                    and check_feature(
                        user_id=self.service.user.id,
                        feature_flag=MailBotFeatureFlag.ARCHIVING_EMAILZAP,
                        application_tag=ApplicationTag.MailBot,
                    )
                    and result.get("has_unsubscribe_link") is True
                ):
                    # Skip first time sender overlay for unsubscribe link emails
                    pass
                else:
                    # Check and send first time sender overlay mail
                    should_send_overlay, options_for_overlay = self.compute_should_send_fts_overlay_and_options()
                    if should_send_overlay:
                        overlay_limit_preference = result.get("first_time_sender_overlay_limit", "no-limit")
                        if overlay_limit_preference != "no-limit":
                            twenty_four_hours_ago = timezone.now() - timezone.timedelta(hours=24)
                            overlays_sent_past_24_hours = SentEmails.objects.filter(
                                user=self.service.user,
                                template__tag=MailBotTemplateTag.FIRST_TIME_SENDER_OVERLAY.value,
                                created__gte=twenty_four_hours_ago,
                            ).count()
                            if overlays_sent_past_24_hours >= int(overlay_limit_preference):
                                logger.info(
                                    f"Skip sending overlay mail for user {self.user_email} due to maximum limit {overlay_limit_preference}"
                                )
                                should_send_overlay = False
                    if should_send_overlay:
                        self.overlay_service.send_overlay(
                            overlay_key=MailBotMessageCategory.FIRST_TIME_SENDER_OVERLAY.value,
                            sender_profile=self.sender_profile,
                            options_for_overlay=options_for_overlay,
                        )
                        # TODO : Common out the logic to forward emails to primary
                        if should_forward_overlay(self.service.user_mailbot_profile):
                            self.overlay_service.send_overlay(
                                overlay_key="forward_first_time_sender_overlay_to_primary",
                                sender_profile=self.sender_profile,
                                options_for_overlay=options_for_overlay,
                            )

            if result.get("first_time_sender_treatment") in ["low_priority_with_overlay", "low_priority"]:
                # Check and Send auto responder mail
                # Current mail is tagged by first time sender filter
                # Check if user preference for auto responder is enabled or not
                auto_responder_enabled = result.get("auto_responder_enabled") and check_feature(
                    user_id=self.parsed_message.user_id,
                    feature_flag=MailBotFeatureFlag.AI_AUTO_RESPONDER,
                    application_tag=ApplicationTag.MailBot,
                )
                if auto_responder_enabled:
                    # Check if we have considered this sender before for sending auto responder for current user
                    # This can happen if user deletes all sender's email and in future again gets sender's email
                    try:
                        ScheduledTask.objects.get(
                            user=self.service.user,
                            metadata__sender_email=self.sender_email,
                            periodic_task__task=MailBotScheduledTasks.SEND_AUTO_RESPONDER_MAIL.value,
                        )
                        logger.info(
                            f"Skip auto responder as we have already processed it for user {self.service.user}, sender {self.sender_email}"
                        )
                    except ScheduledTask.DoesNotExist:
                        ScheduledTask.create_one_off_task(
                            user=self.service.user,
                            task=MailBotScheduledTasks.SEND_AUTO_RESPONDER_MAIL.value,
                            start_time=timezone.now() + timezone.timedelta(hours=1),
                            task_kwargs={
                                "sender_email": self.sender_email,
                                "sender_name": self.sender_name,
                                "message_id": self.parsed_message.message_id,
                                "subject": self.parsed_message.subject,
                                "user_id": self.service.user.id,
                                "service_provider": self.service.user_mailbot_profile.service_provider,
                            },
                            metadata={"sender_email": self.sender_email},
                        )
                        logger.info(
                            f"Auto responder scheduled for user {self.service.user}, sender {self.sender_email}"
                        )
                    except ScheduledTask.MultipleObjectsReturned:
                        logger.exception(
                            f"Multiple auto responders scheduled for user.",
                            extra={"user": self.service.user.email, "sender": self.sender_email},
                        )
                else:
                    logger.info(f"Skip auto responder as it is disabled for user {self.service.user}")
        if result.get("message_labelled_due_to") == MailBotMessageLabelReason.LUCENE_FILTER.value:
            category_tags = result.get("category_tags")
            assert isinstance(category_tags, list) and len(category_tags) > 0
            if set(category_tags).intersection(set(constance_config.NON_ARCHIVE_LUCENE_TAGS)):
                logger.info(
                    f"Skip archiving lucene alert as some category tags out of {category_tags} are in non-archive mode"
                )
                return
            if check_feature(
                user_id=self.service.user.id,
                feature_flag=MailBotFeatureFlag.ARCHIVING_EMAILZAP,
                application_tag=ApplicationTag.MailBot,
            ):
                self.add_archiving_emailzap_label()
            else:
                try:
                    domain_training = DomainTraining.objects.get(
                        user_mailbot_profile=self.service.user_mailbot_profile, sender_domain=self.sender_domain
                    )
                except DomainTraining.DoesNotExist:
                    pass
                else:
                    if domain_training.metadata.get("lucene_alert_archive_cancelled"):
                        logger.info(
                            f"Skip sending lucene alert due to user preference of {self.service.email} for {self.sender_domain}"
                        )
                        return
                if self.sender_profile.metadata.get("lucene_alert_archive_cancelled"):
                    logger.info(
                        f"Skip sending lucene alert due to user preference of {self.service.email} for {self.sender_email}"
                    )
                    return
                # Send the lucene alert
                self.overlay_service.send_overlay(overlay_key=MailBotMessageCategory.LUCENE_ALERT.value)
                if should_forward_overlay(self.service.user_mailbot_profile):
                    self.overlay_service.send_overlay(
                        overlay_key="forward_lucene_alert_to_primary",
                    )
        if result.get(
            "message_labelled_due_to"
        ) == MailBotMessageLabelReason.UNSUBSCRIBED_FILTER.value and check_feature(
            user_id=self.service.user.id,
            feature_flag=MailBotFeatureFlag.UNSUBSCRIBE,
            application_tag=ApplicationTag.MailBot,
        ):
            # Unsubscribe the user from the sender's mailing list
            if self.parsed_message.unsubscribe_link or self.parsed_message.unsubscribe_link_mail_to:
                logger.info(f"Unsubscribing new email from already unsubscribed sender {self.sender_email}")
                sender_unsubscribe_detail: SenderUnsubscribeDetail = SenderUnsubscribeDetail.objects.get(
                    user_mailbot_profile=self.service.user_mailbot_profile,
                    unsubscribe_link=self.parsed_message.unsubscribe_link,
                    unsubscribe_mail_to=self.parsed_message.unsubscribe_link_mail_to,
                )
                if sender_unsubscribe_detail.unsubscribed:
                    logger.info(f"Sender {self.sender_email} didn't previously unsubscribed successfully")
                else:
                    unsubscribe_email(sender_unsubscribe_detail.id)

    def take_action_pre_move(self, result: Dict[str, Any]):
        """
        Take action on label assigned by label engine to newly created message in the user's mailbox
        before it is actually moved to newly assigned label.
        Examples:
            Move all previous thread messages to same label as newly created message's assigned label.
        Args:
            result: Mappings consisting label_name, message_labelled_due_to and other fields from passed action results.
        """
        if result.get("message_labelled_due_to") == MailBotMessageLabelReason.SAME_THREAD_FILTER.value:
            if move_thread_to := result.get("move_thread_to"):
                thread_message_ids = set(
                    self.service.get_thread_messages(thread_id=self.parsed_message.thread_id).values_list(
                        "message_id", flat=True
                    )
                )
                thread_message_ids.remove(self.parsed_message.message_id)
                for thread_message_id in thread_message_ids:
                    logger.info(f"{thread_message_id}: Thread pre moving to {move_thread_to}")
                    self.service.move_to_known_label(message_id=thread_message_id, generic_label=move_thread_to)

    def process_new_inbox_mail(self):
        """Assign label for new inbox mail and take actions based on label and reason assigned."""
        through_instances = MessageCategoryThrough.objects.filter(
            message__message_id=self.parsed_message.message_id
        ).select_related("category")
        message_categories = [instance.category for instance in through_instances]

        new_label_response = self.get_label_for_new_mail(message_categories=message_categories)
        self.take_action_pre_move(new_label_response)
        message_labelled_due_to = new_label_response.get("message_labelled_due_to")
        # label name will generic (white, grey, black) list
        generic_label = new_label_response.get("label_name")
        # Save labelling reason and other information in message metadata
        message = Message.objects.get(
            message_id=self.parsed_message.message_id, user_mailbot_profile=self.service.user_mailbot_profile
        )
        message.metadata["labelled_due_to"] = message_labelled_due_to
        if category_tags := new_label_response.get("category_tags"):
            message.metadata["category_tags"] = category_tags
        if fts_exempt := new_label_response.get("fts_exempt"):
            message.metadata["fts_exempt"] = fts_exempt
        if has_unsubscribe_link := new_label_response.get("has_unsubscribe_link"):
            message.metadata["has_unsubscribe_link"] = has_unsubscribe_link
        if generic_label == MailBotGenericLabel.ZAPPED.value and (
            include_in_digest := new_label_response.get("include_in_digest")
        ):
            message.metadata["include_in_digest"] = include_in_digest
        message.save()

        is_internal_message = bool(
            self.parsed_message.message_headers.get(MailBotMessageHeaders.MESSAGE_CATEGORY.value)
        )
        # Attach the additional label to the message e.g in case of forwarded message from secondary account we attach the secondary account label to the message
        if custom_labels := new_label_response.get("custom_labels"):
            self.service.add_custom_labels(message_id=self.parsed_message.message_id, label_names=custom_labels)
        # Move the message before taking further actions based on label
        if generic_label != MailBotGenericLabel.WHITE_LIST.value:
            logger.info(
                f"moving inbox message to {generic_label}, due to {message_labelled_due_to} for user {self.service.email}"
            )
            if generic_label == Message.GENERIC_LABEL_ARCHIVE:
                self.service.archive_message(message_id=self.parsed_message.message_id)
            else:
                self.service.move_to_known_label(self.parsed_message.message_id, generic_label=generic_label)
            if generic_label == MailBotGenericLabel.ZAPPED.value:
                update_statistics_in_analytics(
                    user_mailbot_profile=self.service.user_mailbot_profile,
                    key=AnalyticsStatisticsKey.MESSAGES_ZAPPED.value,
                    value=1,
                )
        else:
            if is_internal_message:
                logger.info(f"retaining inbox message due to internal message for user {self.service.email}")
            elif (
                self.user_mailbot_preferences.get("inbox_overlay_enabled", False)
                and message_labelled_due_to != MailBotMessageLabelReason.LUCENE_FILTER.value
            ):
                # check if the message is not a system email and send inbox overlay mail followed by archiving the original message
                self.overlay_service.send_overlay(overlay_key=MailBotMessageCategory.INBOX_OVERLAY.value)
            else:
                logger.info(f"retaining inbox message due to {message_labelled_due_to} for user {self.service.email}")

            # The email is labeled as white-listed and not internal message, meaning it should remain in the inbox.
            # If the email belongs to a secondary account, we need check and forward it to primary account.
            # TODO: Remove lucene check once we have a proper way to handle lucene alerts as right now lucene
            # filter get inbox label from rule engine for lucene alerts which is the reason we need to exlude lucene filter from below condition
            if (
                not is_internal_message
                and message_labelled_due_to != MailBotMessageLabelReason.LUCENE_FILTER.value
                and should_forward_overlay(self.service.user_mailbot_profile)
            ):
                self.overlay_service.send_overlay(overlay_key="forward_inbox_secondary_to_primary")

        if new_label_response.get("mark_as_read"):
            self.service.mark_read(message_id=self.parsed_message.message_id)
        elif new_label_response.get("mark_as_starred"):
            self.service.star_message(message_id=self.parsed_message.message_id)
        self.take_action_post_move(result=new_label_response)
