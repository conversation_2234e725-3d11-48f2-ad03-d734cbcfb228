import logging
from django.contrib.auth import get_user_model
from mailbot.service.gmail import GmailService
from mailbot.utils.check import is_associated_mailbot_profile

User = get_user_model()
logger = logging.getLogger(__name__)


class SecondaryAccountEmailHandler:
    """
    Handles email processing for secondary accounts.
    """

    @staticmethod
    def handle_label_change(forwarded_message, original_message, remove_label_name, add_label_name):
        """
        Syncs label changes from a forwarded message in the primary account
        to the corresponding original message in the associated secondary account.

        Typically triggered when a user trains (e.g., whitelist/zapped) on a forwarded message.
        This will archive the overlay message and update the label on the original message  and trigger a webhook from Gmail,
        allowing all downstream logic to run from there.

        Args:
            forwarded_message: Forwarded message object (from the primary account).
            original_message: Original message object (from the secondary account).
            remove_label_name: Label to remove from the original message.
            add_label_name: Label to add to the original message.
        """
        try:
            if not is_associated_mailbot_profile(
                forwarded_message.user_mailbot_profile.id, original_message.user_mailbot_profile.id
            ):
                logger.error(
                    f"User {forwarded_message.user_mailbot_profile.id} is not associated with message {original_message.message_id}"
                )
                return

            gmail_service = GmailService(user_mailbot_profile=original_message.user_mailbot_profile)

            # Move the message between generic labels
            gmail_service.move_between_generic_labels(
                message_id=original_message.message_id,
                remove_label_name=remove_label_name,
                add_label_name=add_label_name,
            )
        except Exception as e:
            logger.exception(f"Failed to update labels for original message {original_message.message_id}: {str(e)}")
        else:
            if overlay_message_id := original_message.metadata.get("overlay_message_id"):
                gmail_service.archive_message(overlay_message_id)
                logger.info(
                    f"Successfully archived overlay message {overlay_message_id} for original message {original_message.message_id}"
                )
            else:
                logger.info(f"No overlay message ID found for original message {original_message.message_id}")
