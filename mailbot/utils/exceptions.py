class MailBotError(Exception):
    """Base error for this module."""

    pass


class MultipleMailBotLabelError(MailBotError):
    """Multiple mailbot labels found on single message."""

    pass


class SyncMessageWithDatabaseError(MailBotError):
    def __init__(self, message_id, reason):
        self.message_id = message_id
        self.reason = reason

    def __str__(self):
        return f"{self.message_id}: {self.reason}"


class MailBotHTTPError(MailBotError):
    """Error making HTTP request."""

    def __init__(self, status_code, reason):
        self.status_code = status_code
        self.reason = reason

    def __str__(self):
        return f"{self.status_code}: Reason {self.reason}"


class MessageIdNotFoundError(MailBotHTTPError):
    """Message id not found."""

    def __init__(self, message_id, reason, status_code):
        super().__init__(status_code, reason)
        self.message_id = message_id

    def __str__(self):
        return f"{self.reason}: Message ID {self.message_id}"


class InvalidSelectFieldsError(MailBotHTTPError):
    """Invalid select field."""

    def __init__(self, select_fields, reason, status_code):
        super().__init__(status_code, reason)
        self.select_fields = select_fields

    def __str__(self):
        return f"{self.reason}: select fields {self.select_fields}"


class LabelNameAlreadyExistError(MailBotHTTPError):
    """Label name already exists in mailbox while creating it."""

    def __init__(self, label_name, reason, status_code):
        super().__init__(status_code, reason)
        self.label_name = label_name

    def __str__(self):
        return f"{self.reason}: label name {self.label_name}"


class ModifyMessageLabelsError(MailBotHTTPError):
    """Error modifying message labels using Gmail API."""

    def __init__(self, message_id, added_label_ids, removed_label_ids, reason, status_code):
        super().__init__(status_code, reason)
        self.message_id = message_id
        self.added_label_ids = added_label_ids
        self.removed_label_ids = removed_label_ids

    def __str__(self):
        return f"{self.reason}: Message ID {self.message_id}, added_labels {self.added_label_ids}, removed_labels {self.removed_label_ids}"


class ConflictingLabelIdError(ModifyMessageLabelsError):
    """Removed labels and added labels have a common label id in request body."""

    pass


class LabelIdNotFoundError(ModifyMessageLabelsError):
    """Label Id not found."""

    pass
