from mailbot.models import MailBotGeneric<PERSON>abel, SenderProfile
from mailbot.utils.label_engine.actions.first_time_sender_action import FirstTimeSenderAction
from mailbot.utils.label_engine.actions.read_fraction_action import ReadFractionAction
from mailbot.utils.label_engine.filters.domain_training_filter import DomainTrainingFilter
from mailbot.utils.label_engine.filters.exception_list_filter import ExceptionList<PERSON>ilter
from mailbot.utils.label_engine.filters.first_time_sender_filter import FirstTimeSenderFilter
from mailbot.utils.label_engine.filters.read_fraction_filter import ReadFractionFilter
from mailbot.utils.label_engine.filters.sent_message_filter import SentMessageFilter
from mailbot.utils.label_engine.filters.user_training_filter import UserTrainingFilter


def is_sender_blocked_by_emailzap(sender_profile: SenderProfile, user_email: str):
    """
    Check if sender is blocked by emailzap based on sender profile level label engine rules.

    Args:
        sender_profile (SenderProfile): SenderProfile object
        user_email (str): User email
    """
    block_labels = (MailBotGenericLabel.ZAPPED.value,)
    algo_version = sender_profile.user_mailbot_profile.algo_version
    if UserTrainingFilter.base_filter(sender_profile=sender_profile)["condition"]:
        return False
    if SentMessageFilter.base_filter(user_email=user_email, sender_profile=sender_profile)["condition"]:
        return False
    filter_result = DomainTrainingFilter.base_filter(sender_profile)
    if filter_result["condition"]:
        if filter_result["label_name"] in block_labels:
            return True
        else:
            return False
    filter_result = ExceptionListFilter.base_filter(sender_profile=sender_profile)
    if filter_result["condition"]:
        if filter_result["label_name"] in block_labels:
            return True
        else:
            return False
    filter_result = FirstTimeSenderFilter.base_filter(sender_profile=sender_profile, algo_version=algo_version)
    if filter_result["condition"]:
        action_result = FirstTimeSenderAction.base_action(
            user_mailbot_profile=sender_profile.user_mailbot_profile,
            algo_version=algo_version,
        )
        if action_result["label_name"] in block_labels:
            return True
        else:
            return False
    filter_result = ReadFractionFilter.base_filter(sender_profile=sender_profile, algo_version=algo_version)
    if filter_result["condition"]:
        action_result = ReadFractionAction.base_action(sender_profile=sender_profile, algo_version=algo_version)
        if action_result["label_name"] in block_labels:
            return True
        else:
            return False
    return False
