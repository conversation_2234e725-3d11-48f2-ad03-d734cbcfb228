import logging
from enum import Enum
from typing import Iterable

from django.conf import settings
from django.contrib.auth import get_user_model

from applications.feature_flags.base import FeatureFlagBaseApplication
from applications.feature_flags.defaults import MailBotFeatureFlag
from execfn import ApplicationTag
from mailbot.models import UserMailBotProfile
from mailbot.service.gmail import GmailService
from payments.utils.feature_flags import get_payment_properties

User = get_user_model()

logger = logging.getLogger(__name__)


class FeatureFlagMailBotApplication(FeatureFlagBaseApplication):
    application_tag = ApplicationTag.MailBot.value

    @staticmethod
    def get_properties(user, *args, **kwargs):
        """
        Retrieve user mail bot profile properties.

        Args:
            user (User): The user for whom to retrieve mail bot profile properties.

        Returns:
            Dict[str, Any]: Dictionary containing user mail bot profile properties.
        """
        # Calling super from static method https://stackoverflow.com/questions/26788214/super-and-staticmethod-interaction
        context = super(FeatureFlagMailBotApplication, FeatureFlagMailBotApplication).get_properties(
            user, *args, **kwargs
        )
        try:
            signup_date = user.user_mailbot_profile.created.isoformat()
        except UserMailBotProfile.DoesNotExist:
            pass
        else:
            context["signup_date"] = signup_date
        payment_context = get_payment_properties(user)
        context.update(payment_context)
        return context


class FlagStatus(Enum):
    TURNED_OFF = "off"
    TURNED_ON = "on"


def change_target_users_decorator(
    flag: MailBotFeatureFlag, status: FlagStatus, service_providers_allowed: Iterable[str]
):
    def inner_func(func):
        def _wrap(self, profiles):
            for profile in profiles:
                if profile.service_provider in service_providers_allowed and (
                    (status == FlagStatus.TURNED_OFF and profile.user.email in self.turned_off_for_users)
                    or (status == FlagStatus.TURNED_ON and profile.user.email in self.turned_on_for_users)
                ):
                    try:
                        logger.info(
                            f"Processing feature flag {flag.value} turned {status.value} for user {profile.user.email}"
                        )
                        func(self, profile)
                    except Exception:
                        logger.exception(
                            "Error while processing feature flag for single user",
                            extra={"user": profile.user.email, "flag": flag.value, "turned": status.value},
                        )

        return _wrap

    return inner_func


class FeatureFlagMailBotTargetChanged:
    def __init__(self, flag: str, turned_on_for_users: set, turned_off_for_users: set):
        assert turned_on_for_users.isdisjoint(
            turned_off_for_users
        ), "User cannot be simultaneously turned on and off to the flag target"
        self.flag = flag
        self.turned_on_for_users = turned_on_for_users
        self.turned_off_for_users = turned_off_for_users

    def handle(self):
        profiles = UserMailBotProfile.objects.select_related("user").filter(
            user__email__in=self.turned_on_for_users.union(self.turned_off_for_users)
        )
        if self.flag == MailBotFeatureFlag.ARCHIVING_EMAILZAP.value:
            self.handle_archiving_emailzap_turn_on(profiles)
            self.handle_archiving_emailzap_turn_off(profiles)

    @change_target_users_decorator(
        flag=MailBotFeatureFlag.ARCHIVING_EMAILZAP,
        status=FlagStatus.TURNED_ON,
        service_providers_allowed=(settings.SERVICE_PROVIDER_GOOGLE,),
    )
    def handle_archiving_emailzap_turn_on(self, profile):
        service = GmailService(user_mailbot_profile=profile)
        service.create_archiving_emailzap_label(sync_label_mappings=True)

    @change_target_users_decorator(
        flag=MailBotFeatureFlag.ARCHIVING_EMAILZAP,
        status=FlagStatus.TURNED_OFF,
        service_providers_allowed=(settings.SERVICE_PROVIDER_GOOGLE,),
    )
    def handle_archiving_emailzap_turn_off(self, profile):
        service = GmailService(user_mailbot_profile=profile)
        service.delete_label(service.ARCHIVING_EMAILZAP)
