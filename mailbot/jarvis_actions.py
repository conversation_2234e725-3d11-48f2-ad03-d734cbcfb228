import json
import logging
import uuid
from typing import Dict, List, IO

import <PERSON>yPDF2
import docx
from django.conf import settings
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db.models import QuerySet
from django.utils.text import slugify
from langchain_community.callbacks import get_openai_callback
from langchain_community.chat_models import ChatOpenAI
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate

from applications.models import EmailTemplate
from applications.utils.email import html_to_text, EmailBackend
from execfn import ApplicationTag
from execfn.common.google_sheets import GoogleSheetManager
from execfn.common.s3 import S3Manager
from execfn.common.utils.search import isearch_key
from jarvis.models import OpenAICostAnalysis
from jarvis.utils.actions import BaseAction
from jarvis.utils.openai import complete_chat, get_token_count
from mailbot.models import Message
from mailbot.service.factory import MessageServiceFactory, GmailService, OutlookService
from mailbot.utils.defaults import PDF_MIME_TYPE, DOCX_MIME_TYPE
from mailbot.utils.email import remove_quoted_text
from mailbot.utils.exceptions import MailBotError
from mailbot.utils.message_parser import ParsedMessage

logger = logging.getLogger(__name__)


class UpdateLabelAction(BaseAction):
    tag = "update_email_label_action"
    schema = {
        "type": "object",
        "properties": {"label_name": {"description": "Label name to be updated", "type": "string"}},
    }

    def execute(self, payload):
        if isinstance(payload, QuerySet) and payload.model == Message:
            messages = list(payload)
        elif isinstance(payload, Message):
            messages = [payload]
        else:
            raise NotImplementedError("Invalid payload type.")
        for message in messages:
            user_mailbot_profile = message.user_mailbot_profile
            service = MessageServiceFactory.get_message_service(user_mailbot_profile)
            label_name_to_update = self.params.get("label_name")
            try:
                service.move_to_label(message.message_id, label_name_to_update)
            except MailBotError as e:
                logger.info(
                    "Jarvis action failed to update message label.",
                    {"message_id": message.message_id, "exception": str(e)},
                )


class ArchiveAction(BaseAction):
    tag = "archive_email_action"
    schema = {"type": "object", "properties": {}}

    def execute(self, payload):
        if isinstance(payload, QuerySet) and payload.model == Message:
            messages = list(payload)
        elif isinstance(payload, Message):
            messages = [payload]
        else:
            raise NotImplementedError("Invalid payload type.")
        for message in messages:
            logger.info(f"Archiving message", {"message_id": message.message_id})
            user_mailbot_profile = message.user_mailbot_profile
            service = MessageServiceFactory.get_message_service(user_mailbot_profile)
            try:
                service.archive_message(message.message_id)
            except MailBotError as e:
                logger.info(
                    "Jarvis action failed archive message.", {"message_id": message.message_id, "exception": str(e)}
                )


class ReadAction(BaseAction):
    tag = "mark_email_as_read_action"
    schema = {"type": "object", "properties": {}}

    def execute(self, payload):
        if isinstance(payload, QuerySet) and payload.model == Message:
            messages = list(payload)
        elif isinstance(payload, Message):
            messages = [payload]
        else:
            raise NotImplementedError("Invalid payload type.")
        for message in messages:
            user_mailbot_profile = message.user_mailbot_profile
            service = MessageServiceFactory.get_message_service(user_mailbot_profile)
            try:
                service.mark_read(message.message_id)
            except MailBotError as e:
                logger.info(
                    "Jarvis action failed to mark message a read.",
                    {"message_id": message.message_id, "exception": str(e)},
                )


class CreateDigestAction(BaseAction):
    tag = "create_digest_action"
    email_template_tag = "email-digest"
    schema = {
        "type": "object",
        "properties": {
            "sender": {
                "description": "From which email account should this email go out",
                "type": "string",
                "enum": ["my account", "emailzap account"],
                "default": "emailzap account",
            },
            "recipients": {
                "description": "List to recipients that the email should go out to",
                "type": "array",
                "items": {"type": "string"},
            },
        },
    }

    def get_digest_context(self, payload):
        """
        Prepare digest context
        Args:
            payload: a single message or a list of messages

        Returns:
            parsed context for digest email
        """
        if isinstance(payload, QuerySet) and payload.model == Message:
            user_mailbot_profile = payload.first().user_mailbot_profile
            messages = list(payload)
        elif isinstance(payload, Message):
            user_mailbot_profile = payload.user_mailbot_profile
            messages = [payload]
        else:
            raise NotImplementedError("Invalid payload type.")
        message_ids = [message.message_id for message in messages]
        service = MessageServiceFactory.get_message_service(user_mailbot_profile)
        parsed_messages = service.get_messages(message_ids)
        digest_context = []
        for parsed_message in parsed_messages:
            try:
                if not (text_body := parsed_message.text_body):
                    text_body = html_to_text(parsed_message.html_body)
                message = {
                    "sender_email": parsed_message.from_name_email[1],
                    "subject": html_to_text(parsed_message.subject),
                    "body": text_body[:200],
                }
            except:
                logger.exception("Failed to parse message for Jarvis Digest Action")
            else:
                digest_context.append(message)
        return digest_context

    def execute(self, *, payload):
        sender = self.params.get("sender", "emailzap account")
        recipients = self.params.get("recipients", [self.user.email])
        send_email_kwargs = {
            "context": {"messages": self.get_digest_context(payload)},
            "user": self.user,
            "to": recipients,
        }
        # If the email is not supposed to go from mailbot, use user's tokens to send email from their mailbox.
        if sender != "emailzap account":
            service = MessageServiceFactory.get_message_service(self.user.user_mailbot_profile)
            tokens = service.get_auth_tokens()
            if isinstance(service, GmailService):
                send_email_kwargs["backend"] = EmailBackend.GMAIL
            elif isinstance(service, OutlookService):
                send_email_kwargs["backend"] = EmailBackend.OUTLOOK
            send_email_kwargs["auth_tokens"] = tokens
        digest_email_template = EmailTemplate.objects.get(
            tag=self.email_template_tag, application__tag=ApplicationTag.MailBot.value
        )
        digest_email_template.send_email(**send_email_kwargs)


class FailedToGetEmailContentException(Exception):
    """Failed to get content for sending email"""

    pass


# TODO : Convert this into a utility.
class SendEmailBaseAction:
    WRITER_ARTIFICIAL_INTELLIGENCE = "Auto generated by AI"
    WRITER_ME = "Me"

    # TODO: Enhance this template to fetch only fields that are expected to be written by llm
    PROMPT_TEMPLATE = (
        "Draft an email based on the following query: \n{query} \n"
        "Following is the content of the email : \n{email_body} \n"
        "Share your response in JSON format with keys subject and body."
        "My name is {name}. Do not include title, company name etc in the footer and keep greetings generic"
    )

    def get_email_content(self, chat_input):
        """
        Gets email contents. Also makes call to LLM for compiling subject and body if required.

        Args:
            chat_input : Input to be given to llm for chat completion
        Returns:
            tuple: A tuple containing subject and body for the email
        """
        should_ai_write_subject = (
            self.params.get("subject_written_by", self.WRITER_ARTIFICIAL_INTELLIGENCE)
            == self.WRITER_ARTIFICIAL_INTELLIGENCE
        )
        should_ai_write_body = (
            self.params.get("body_written_by", self.WRITER_ARTIFICIAL_INTELLIGENCE)
            == self.WRITER_ARTIFICIAL_INTELLIGENCE
        )
        subject = self.params.get("subject", "")
        body = self.params.get("body", "")
        # Make call to llm only if it should compose either subject or body or both
        if should_ai_write_subject or should_ai_write_body:
            chat_output = complete_chat(chat_input=chat_input)
            try:
                json_chat_output = json.loads(chat_output)
                if should_ai_write_subject:
                    subject = json_chat_output["subject"]
                if should_ai_write_body:
                    body = json_chat_output["body"]
            except json.decoder.JSONDecodeError:
                raise FailedToGetEmailContentException("Invalid output from chat completion.")
            except KeyError:
                raise FailedToGetEmailContentException("Required keys missing in chat completion output.")
        return (subject, body)


class SendEmailAction(BaseAction, SendEmailBaseAction):
    tag = "send_email_action"
    email_template_tag = "send-with-jarvis"
    schema = {
        "type": "object",
        "properties": {
            "sender": {
                "description": "From which email account should this email go out",
                "type": "string",
                "enum": ["my account", "emailzap account"],
                "default": None,
            },
            "recipients": {
                "description": "List to recipients that the email should go out to",
                "type": "array",
                "items": {"type": "string"},
            },
            "subject_written_by": {
                "description": "Who should write the subject of the email",
                "type": "string",
                "enum": ["Auto generated by AI", "Me"],
                "default": "Auto generated by AI",
            },
            "subject": {"description": "Subject of the email", "type": "string"},
            "body_written_by": {
                "description": "Who should write the body of the email",
                "type": "string",
                "enum": ["Auto generated by AI", "Me"],
                "default": "Auto generated by AI",
            },
            "body": {"description": "Body of the email", "type": "string"},
        },
    }
    ui_schema = {
        "type": "VerticalLayout",
        "elements": [
            {"type": "Control", "scope": "#/properties/sender"},
            {"type": "Control", "scope": "#/properties/recipients"},
            {
                "type": "Group",
                "elements": [
                    {"type": "Control", "scope": "#/properties/subject_written_by"},
                    {
                        "rule": {
                            "effect": "HIDE",
                            "condition": {
                                "scope": "#/properties/subject_written_by",
                                "schema": {"not": {"const": "Me"}},
                            },
                        },
                        "type": "Control",
                        "scope": "#/properties/subject",
                    },
                ],
            },
            {
                "type": "Group",
                "elements": [
                    {"type": "Control", "scope": "#/properties/body_written_by"},
                    {
                        "rule": {
                            "effect": "HIDE",
                            "condition": {"scope": "#/properties/body_written_by", "schema": {"not": {"const": "Me"}}},
                        },
                        "type": "Control",
                        "scope": "#/properties/body",
                    },
                ],
            },
        ],
    }

    def execute(self, *, payload):
        """Prepares content with the given user query as prompt and sends email

        Args:
            payload : Payload received from workflow executor
        """
        if not isinstance(payload, Message):
            raise NotImplementedError("Invalid payload type.")

        sender = self.params.get("sender", "emailzap account")
        recipients = self.params.get("recipients", [])
        if not recipients:
            logger.info("No recipients for send_email_action action.")
            return
        service = MessageServiceFactory.get_message_service(self.user.user_mailbot_profile)
        original_message_body = service.get_message_body(payload.message_id)
        subject, body = self.get_email_content(
            chat_input=self.PROMPT_TEMPLATE.format(
                query=self.user_query, name=self.user.first_name, email_body=original_message_body
            )
        )
        context = {"subject": subject, "body": body}
        email_template_kwargs = {"user": self.user, "context": context, "to": recipients}
        # If the email is not supposed to go from emailzap's account, use user's tokens to send email from their
        # mailbox.
        if sender != "emailzap account":
            tokens = service.get_auth_tokens()
            if isinstance(service, GmailService):
                email_template_kwargs["backend"] = EmailBackend.GMAIL
            elif isinstance(service, OutlookService):
                email_template_kwargs["backend"] = EmailBackend.OUTLOOK
            email_template_kwargs["auth_tokens"] = tokens

        email_template = EmailTemplate.objects.get(
            tag=self.email_template_tag, application__tag=ApplicationTag.MailBot.value
        )
        email_template.send_email(**email_template_kwargs)


class SendReplyAction(BaseAction, SendEmailBaseAction):
    tag = "send_reply_action"
    email_template_tag = "send-with-jarvis"
    schema = {
        "type": "object",
        "properties": {
            "body_written_by": {
                "description": "Who should write the body of the email",
                "type": "string",
                "enum": ["Auto generated by AI", "Me"],
                "default": "Auto generated by AI",
            },
            "body": {"description": "Body of the email", "type": "string"},
        },
    }
    ui_schema = {
        "type": "VerticalLayout",
        "elements": [
            {
                "type": "Group",
                "elements": [
                    {"type": "Control", "scope": "#/properties/body_written_by"},
                    {
                        "rule": {
                            "effect": "HIDE",
                            "condition": {"scope": "#/properties/body_written_by", "schema": {"not": {"const": "Me"}}},
                        },
                        "type": "Control",
                        "scope": "#/properties/body",
                    },
                ],
            }
        ],
    }
    max_tokens_in_input = 11000
    model_name = settings.OPENAI_MODEL_NAME

    PROMPT_TEMPLATE = (
        "Draft an email based on the following query: \n{query} \n"
        "Share your response in JSON format with keys subject and body.\n"
        "My name is {name}. Do not include title, company name etc in the footer and keep greetings generic.\n"
        "You will be given some messages that were sent by the participants in the thread. Use them as context to write a reply.\n\n"
        "This was the first message in the thread. This message may or may not be relevant to the most recent conversations in the thread."
        "\n {first_message}"
        "Here is the rest of the conversation:\n"
        "{conversation}"
    )

    MESSAGE_TEMPLATE = "Sender: {sender}\nDate: {date}\n Content: {content}\n\n"

    def get_thread_information(self, thread_id, service) -> list:
        """
        Returns information about messages in the given thread.

        Args:
            thread_id: thread ID
            service: MessageService instance

        Returns:
            list: List of dictionaries, each containing information about sender, date, content for a message in the thread
        """
        message_ids = Message.objects.filter(thread_id=thread_id).values_list("message_id", flat=True)
        messages = service.get_messages(message_ids=message_ids)
        sorted_messages = sorted(messages, key=lambda obj: obj.received_at)
        thread_information = []
        for message in sorted_messages:
            if not (text_body := message.text_body):
                text_body = html_to_text(message.html_body)
            thread_information.append(
                {
                    "sender": message.from_name_email[1],
                    "date": message.received_at,
                    "text_body": remove_quoted_text(text_body),
                }
            )
        return thread_information

    def get_chat_input(self, messages):
        """
        Returns formatted string to be used as input for chat completion.

        Args:
            messages: Messages to be used as context

        Returns:
            str: Chat input
        """
        first_message = self.MESSAGE_TEMPLATE.format(
            sender=messages[0]["sender"],
            date=messages[0]["date"],
            content=messages[0]["text_body"],
        )
        available_tokens = self.max_tokens_in_input - get_token_count(first_message, self.model_name)
        conversation = ""
        # Iterate over rest of the messages in reverse order
        for message_info in reversed(messages[1:]):
            message = self.MESSAGE_TEMPLATE.format(
                sender=message_info["sender"],
                date=message_info["date"].isoformat(),
                content=message_info["text_body"],
            )
            # If token limit is exceeded, skip this and the next messages
            if (token_count := get_token_count(message, self.model_name)) > available_tokens:
                break
            # Prepending to ensure the messages are in correct chronological order
            conversation = message + conversation
            available_tokens -= token_count
        return self.PROMPT_TEMPLATE.format(
            query=self.user_query, name=self.user.first_name, first_message=first_message, conversation=conversation
        )

    def execute(self, *, payload):
        """Prepares content with the given user query as prompt and sends reply

        Args:
            payload : Payload received from workflow executor
        """
        if not isinstance(payload, Message):
            raise NotImplementedError("Invalid payload type.")

        original_sender = payload.metadata["from"][1]
        original_message_id = isearch_key(payload.metadata["message_headers"], "Message-Id", payload.message_id)
        thread_id = payload.thread_id
        service = MessageServiceFactory.get_message_service(self.user.user_mailbot_profile)
        thread_information = self.get_thread_information(thread_id, service)
        original_message_subject = payload.subject
        _, body = self.get_email_content(chat_input=self.get_chat_input(messages=thread_information))
        context = {"body": body}
        email_template_kwargs = {
            "user": self.user,
            "context": context,
            "to": original_sender,
            "original_message_id": original_message_id,
            "thread_id": thread_id,
            "subject": original_message_subject,
        }
        tokens = service.get_auth_tokens()
        if isinstance(service, GmailService):
            email_template_kwargs["backend"] = EmailBackend.GMAIL
        elif isinstance(service, OutlookService):
            email_template_kwargs["backend"] = EmailBackend.OUTLOOK
        email_template_kwargs["auth_tokens"] = tokens

        email_template = EmailTemplate.objects.get(
            tag=self.email_template_tag, application__tag=ApplicationTag.MailBot.value
        )
        email_template.send_reply(**email_template_kwargs)


class CreateDraftAction(BaseAction, SendEmailBaseAction):
    tag = "create_draft_action"
    email_template_tag = "send-with-jarvis"
    schema = {
        "type": "object",
        "properties": {
            "subject_written_by": {
                "description": "Who should write the subject of the email",
                "type": "string",
                "enum": ["Auto generated by AI", "Me"],
                "default": "Auto generated by AI",
            },
            "subject": {"description": "Subject of the email", "type": "string"},
            "body_written_by": {
                "description": "Who should write the body of the email",
                "type": "string",
                "enum": ["Auto generated by AI", "Me"],
                "default": "Auto generated by AI",
            },
            "body": {"description": "Body of the email", "type": "string"},
        },
    }
    ui_schema = {
        "type": "VerticalLayout",
        "elements": [
            {
                "type": "Group",
                "elements": [
                    {"type": "Control", "scope": "#/properties/subject_written_by"},
                    {
                        "rule": {
                            "effect": "HIDE",
                            "condition": {
                                "scope": "#/properties/subject_written_by",
                                "schema": {"not": {"const": "Me"}},
                            },
                        },
                        "type": "Control",
                        "scope": "#/properties/subject",
                    },
                ],
            },
            {
                "type": "Group",
                "elements": [
                    {"type": "Control", "scope": "#/properties/body_written_by"},
                    {
                        "rule": {
                            "effect": "HIDE",
                            "condition": {"scope": "#/properties/body_written_by", "schema": {"not": {"const": "Me"}}},
                        },
                        "type": "Control",
                        "scope": "#/properties/body",
                    },
                ],
            },
        ],
    }

    def execute(self, *, payload):
        """Prepares content with the given user query as prompt and creates draft in user's mailbox

        Args:
            payload : Payload received from workflow executor
        """
        if not isinstance(payload, Message):
            raise NotImplementedError("Invalid payload type.")
        service = MessageServiceFactory.get_message_service(self.user.user_mailbot_profile)
        original_message_body = service.get_message_body(message_id=payload.message_id)
        original_sender = payload.metadata["from"][1]
        # THIS IS A HACK FOR THE DEMO. Need to figure out a way to pass this in context.
        if payload.generic_label == Message.GENERIC_LABEL_SENT:
            if isinstance(service, GmailService):
                response = service.mailbox.get_message_ids(
                    label_ids=None,
                    max_results=1,
                    q=f"rfc822msgid:{isearch_key(payload.metadata['message_headers'], 'In-Reply-To')}",
                )
                message_ids = response.get("messages", [])
                if len(message_ids):
                    in_reply_to_message_id = message_ids[0]["id"]
                    parsed_message = service.get_message(in_reply_to_message_id)
                    original_sender = parsed_message.from_name_email[1]

        original_message_id = isearch_key(payload.metadata["message_headers"], "Message-Id", payload.message_id)
        thread_id = payload.thread_id
        subject, body = self.get_email_content(
            chat_input=self.PROMPT_TEMPLATE.format(
                query=self.user_query, name=self.user.first_name, email_body=original_message_body
            )
        )
        context = {"subject": subject, "body": body}
        email_template_kwargs = {
            "user": self.user,
            "context": context,
            "to": original_sender,
            "original_message_id": original_message_id,
            "thread_id": thread_id,
        }
        tokens = service.get_auth_tokens()
        if isinstance(service, GmailService):
            email_template_kwargs["backend"] = EmailBackend.GMAIL
        elif isinstance(service, OutlookService):
            email_template_kwargs["backend"] = EmailBackend.OUTLOOK
        email_template_kwargs["auth_tokens"] = tokens
        email_template = EmailTemplate.objects.get(
            tag=self.email_template_tag, application__tag=ApplicationTag.MailBot.value
        )
        email_template.create_draft(**email_template_kwargs)


class TrashAction(BaseAction):
    tag = "trash_email_action"
    schema = {"type": "object", "properties": {}}

    def execute(self, payload):
        if isinstance(payload, QuerySet) and payload.model == Message:
            messages = list(payload)
        elif isinstance(payload, Message):
            messages = [payload]
        else:
            raise NotImplementedError("Invalid payload type.")
        for message in messages:
            logger.info(f"Deleting message", {"message_id": message.message_id})
            user_mailbot_profile = message.user_mailbot_profile
            service = MessageServiceFactory.get_message_service(user_mailbot_profile)
            try:
                service.trash_message(message.message_id)
            except MailBotError as e:
                logger.info(
                    "Jarvis action failed to trash message.", {"message_id": message.message_id, "exception": str(e)}
                )


class StarAction(BaseAction):
    tag = "star_email_action"
    schema = {"type": "object", "properties": {}}

    def execute(self, payload):
        if isinstance(payload, QuerySet) and payload.model == Message:
            messages = list(payload)
        elif isinstance(payload, Message):
            messages = [payload]
        else:
            raise NotImplementedError("Invalid payload type.")
        for message in messages:
            logger.info(f"Adding message to starred", {"message_id": message.message_id})
            user_mailbot_profile = message.user_mailbot_profile
            service = MessageServiceFactory.get_message_service(user_mailbot_profile)
            try:
                service.star_message(message.message_id)
            except MailBotError as e:
                logger.info(
                    "Jarvis action failed to star message.", {"message_id": message.message_id, "exception": str(e)}
                )


class MarkSpamAction(BaseAction):
    tag = "spam_email_action"
    schema = {"type": "object", "properties": {}}

    def execute(self, payload):
        if isinstance(payload, QuerySet) and payload.model == Message:
            messages = list(payload)
        elif isinstance(payload, Message):
            messages = [payload]
        else:
            raise NotImplementedError("Invalid payload type.")
        user_mailbot_profile = self.user.user_mailbot_profile
        service = MessageServiceFactory.get_message_service(user_mailbot_profile)
        for message in messages:
            logger.info(f"Adding message to spam", {"message_id": message.message_id})
            try:
                service.mark_spam(message.message_id)
            except MailBotError as e:
                logger.info(
                    "Jarvis action failed to move message to spam.",
                    {"message_id": message.message_id, "exception": str(e)},
                )


class ParseEmailContent(BaseAction):
    tag = "parse_email_content_action"
    schema = {"type": "object", "properties": {}}
    DATA_HEADERS_EXTRACTION_PROMPT = (
        "You're an expert at extracting data from emails. You are building a database where"
        "you dump relevant information extracted from a list of emails. From the given user instructions, identify the"
        "fields that we need to extract from emails. If the fields are not mentioned in the user's instructions"
        "analyse the content of the email, and identify the fields that indicate important information."
        "Return a JSON object with key fields and the value list of field names."
        "information."
        "\nUser Instructions:"
        "{user_query}"
        "\nEmail:"
        "{email}"
        "\nResponse:"
    )
    CONTEXT_EXTRACTION_PROMPT = (
        "Extract information from the given email, it's attachments and metadata as per the user's instructions. "
        "Return your answer in JSON format with the mentioned fields as keys."
        "\nUser Instructions:{user_query}"
        "\nFields : {fields}"
        "\nEmail Body: {email}"
        "\nAttachments: {attachments}"
        "\nEmail Metadata: {email_metadata}"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # TODO : Implement incremental auth flow to get spreadsheet access scopes separately
        jarvis_auth_creds = self.user.jarvis_google_auth_credentials
        access_token, refresh_token = jarvis_auth_creds.get_credentials()
        self.google_sheet_manager = GoogleSheetManager(access_token=access_token, refresh_token=refresh_token)
        self.open_ai_model = "gpt-3.5-turbo"
        self.llm = ChatOpenAI(
            model=self.open_ai_model, temperature=0.7, model_kwargs={"response_format": {"type": "json_object"}}
        )
        user_mailbot_profile = self.user.user_mailbot_profile
        self.service = MessageServiceFactory.get_message_service(user_mailbot_profile)
        self.execution_uid = uuid.uuid4()

    def execute(self, payload):
        if isinstance(payload, QuerySet) and payload.model == Message:
            messages = list(payload.order_by("-received_at"))
        elif isinstance(payload, Message):
            messages = [payload]
        else:
            raise NotImplementedError("Invalid payload type.")
        # We're limiting extraction to only 50 messages for now
        messages = messages[:50]
        message_ids = [message.message_id for message in messages]
        parsed_messages = self.service.get_messages(message_ids)
        extracted_data = self.extract_data(parsed_messages)
        self.update_google_spreadsheet(extracted_data)

    def extract_data(self, parsed_messages: List[ParsedMessage]) -> List[Dict]:
        """
        Extract the relevant information from emails
        Args:
            parsed_messages: list of parsed messages

        Returns:
            extracted information
        """
        data_headers = self.extract_data_headers(parsed_messages[0])
        output_parser = JsonOutputParser()
        content_extraction_prompt = PromptTemplate(
            template=self.CONTEXT_EXTRACTION_PROMPT, input_variables=["email", "fields"]
        )
        content_extraction_chain = content_extraction_prompt | self.llm | output_parser
        results = []
        for message in parsed_messages:
            with get_openai_callback() as cb:
                attachments_content = []
                uploaded_attachment_urls = []
                for attachment_details in message.attachments:
                    if (mime_type := attachment_details["mimeType"]) not in [PDF_MIME_TYPE, DOCX_MIME_TYPE]:
                        continue
                    attachment_file = self.service.get_attachment(
                        message.message_id, attachment_details["attachmentId"]
                    )
                    if mime_type == PDF_MIME_TYPE:
                        attachments_content.append(self.extract_text_from_pdf(attachment_file))
                    elif mime_type == DOCX_MIME_TYPE:
                        attachments_content.append(self.extract_text_from_docx(attachment_file))
                    if uploaded_file_url := self.upload_attachment_to_s3(attachment_file, attachment_details):
                        uploaded_attachment_urls.append(uploaded_file_url)
                extracted_message_data = content_extraction_chain.invoke(
                    {
                        "email": message.text_body,
                        "fields": data_headers,
                        "email_metadata": message.message_headers,
                        "user_query": self.user_query,
                        "attachments": "\n".join(attachments_content),
                    }
                )
                # Update extracted data with additional metadata
                extracted_message_data.update(
                    {
                        "from": message.from_name_email[1],
                        "date": message.received_at.isoformat(),
                        "subject": message.subject,
                    }
                )
                if uploaded_attachment_urls:
                    extracted_message_data["attachments"] = "\n".join(uploaded_attachment_urls)
                # Append the result to the list
                results.append(extracted_message_data)
                self.update_costs(
                    total_tokens=cb.total_tokens,
                    prompt_tokens=cb.prompt_tokens,
                    completion_tokens=cb.completion_tokens,
                    total_cost=cb.total_cost,
                )
        return results

    def upload_attachment_to_s3(self, file_object: IO, attachment_details: Dict) -> str:
        """
        Upload email attachment to S3

        Args:
            file_object: file object
            attachment_details: attachment details

        Returns:
            uploaded file URL
        """
        file_object.seek(0)
        filename = attachment_details["filename"]
        memory_file = InMemoryUploadedFile(
            file=file_object,
            field_name=None,
            name=filename,
            content_type=attachment_details["mimeType"],
            size=attachment_details["size"],
            charset=None,
        )
        s3_manager = S3Manager()
        base_filename, extension = filename.rsplit(".", 1)
        uploaded_filename = f"{slugify(base_filename)}.{extension}"
        uploaded_file_path = f"jarvis/{self.user.id}/{self.workflow.id}/{self.execution_uid}/{uploaded_filename}"
        return s3_manager.upload_memory_file(memory_file, settings.AWS_STORAGE_BUCKET_NAME, uploaded_file_path)

    def extract_data_headers(self, parsed_message: ParsedMessage) -> List[str]:
        """
        Extract the data headers
        Args:
            parsed_message: parsed message

        Returns:
            list of data headers
        """
        if not (data_headers := self.workflow.metadata.get("data_headers")):
            output_parser = JsonOutputParser()
            data_headers_extraction_prompt = PromptTemplate(
                template=self.DATA_HEADERS_EXTRACTION_PROMPT, input_variables=["email", "user_query"]
            )
            field_extraction_chain = data_headers_extraction_prompt | self.llm | output_parser
            with get_openai_callback() as cb:
                response = field_extraction_chain.invoke(
                    {"user_query": self.user_query, "email": parsed_message.text_body}
                )
                self.update_costs(
                    total_tokens=cb.total_tokens,
                    prompt_tokens=cb.prompt_tokens,
                    completion_tokens=cb.completion_tokens,
                    total_cost=cb.total_cost,
                )
            data_headers = response.get("fields", [])
            self.workflow.metadata["data_headers"] = data_headers
            self.workflow.save()
        return data_headers

    def update_google_spreadsheet(self, data: List[Dict]):
        """
        Update google spreadsheet with extracted information
        Args:
            data: list of data rows
        """
        google_worksheet_name = "Jarvis Export"
        google_spreadsheet_id = self.get_google_spreadsheet_id()
        self.google_sheet_manager.write_dicts_to_sheet(google_spreadsheet_id, google_worksheet_name, data)

    def get_google_spreadsheet_id(self) -> str:
        """
        Get or create google spreadsheet and return it's ID

        Returns:
            google spreadsheet ID
        """
        try:
            google_spreadsheet_id = self.workflow.metadata["google_spreadsheet_id"]
        except KeyError:
            spread_sheet_name = f"EmailZap : {self.workflow.name} - {self.workflow.id}"
            google_spreadsheet_id = self.google_sheet_manager.create_spreadsheet(spread_sheet_name)
            self.workflow.metadata["google_spreadsheet_id"] = google_spreadsheet_id
            self.workflow.save()
        return google_spreadsheet_id

    @staticmethod
    def extract_text_from_pdf(file):
        reader = PyPDF2.PdfReader(file)
        text = ""
        for page in reader.pages:
            text += page.extract_text()
        return text

    @staticmethod
    def extract_text_from_docx(file):
        doc = docx.Document(file)
        return "\n".join([para.text for para in doc.paragraphs])

    def update_costs(self, total_tokens, prompt_tokens, completion_tokens, total_cost):
        OpenAICostAnalysis.objects.create(
            user=self.user,
            workflow=self.workflow,
            llm=self.open_ai_model,
            total_tokens=total_tokens,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_cost=total_cost,
        )
