from unittest import mock
from unittest.mock import MagicMock

import factory
from django.db.models.signals import post_save
from django.test.testcases import TestCase
from django.test.utils import override_settings
from faker import Faker

from accounts.tests.factories import UserFactory
from applications.exceptions import FailedToSendEmailException
from applications.tests.factories import ApplicationFactory, EmailTemplateFactory

faker = Faker()


@override_settings(SES_FROM_NAME=faker.name(), SES_FROM_EMAIL=faker.email())
@mock.patch("applications.models.get_connection")
class SendEmailHeaderCleanupTestCase(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        with factory.django.mute_signals(post_save):
            # application's post save signal makes a call to stripe for sync
            cls.application = ApplicationFactory()
        cls.user = UserFactory()

    def setUp(self) -> None:
        self.email_template = EmailTemplateFactory(application=self.application)

    def test_send_email__SubjectHasMultiLineChar__SubjectCleanedUp(self, mock_get_connection):
        """
        test subject is stripped off of multi line characters before sending email
        """
        # Given
        mock_send_messages = MagicMock()
        mock_connection = MagicMock()
        mock_connection.send_messages = mock_send_messages
        mock_get_connection.return_value = mock_connection
        subject_part_1 = faker.word()
        subject_part_2 = faker.word()
        self.email_template.subject_template = f"{subject_part_1}\n\r{subject_part_2}"
        # When
        try:
            self.email_template.send_email(self.user, {}, [faker.email()])
        except FailedToSendEmailException:
            # We're ignoring the exception as we're not sending actual email
            pass
        # Then
        message = mock_connection.send_messages.call_args[0][0][0]
        self.assertEqual(f"{subject_part_1}{subject_part_2}", message.subject)

    def test_send_email__DefaultHeaderHasMultiLineChar__HeaderCleanedUp(self, mock_get_connection):
        """
        test default email headers are stripped off of multi line characters before sending email
        """
        # Given
        mock_send_messages = MagicMock()
        mock_connection = MagicMock()
        mock_connection.send_messages = mock_send_messages
        mock_get_connection.return_value = mock_connection
        header_value_part_1 = faker.word()
        header_value_part_2 = faker.word()
        header_key = faker.word()
        self.email_template.default_headers = {header_key: f"{header_value_part_1}\n\r{header_value_part_2}"}
        # When
        try:
            self.email_template.send_email(
                self.user,
                {},
                [faker.email()],
            )
        except FailedToSendEmailException:
            # We're ignoring the exception as we're not sending actual email
            pass
        # Then
        message = mock_connection.send_messages.call_args[0][0][0]
        self.assertEqual(f"{header_value_part_1}{header_value_part_2}", message.extra_headers[header_key])

    def test_send_email__CustomHeaderHasMultiLineChar__HeaderCleanedUp(self, mock_get_connection):
        """
        test custom email headers are stripped off of multi line characters before sending email
        """
        # Given
        mock_send_messages = MagicMock()
        mock_connection = MagicMock()
        mock_connection.send_messages = mock_send_messages
        mock_get_connection.return_value = mock_connection
        header_value_part_1 = faker.word()
        header_value_part_2 = faker.word()
        header_key = faker.word()
        # When
        try:
            self.email_template.send_email(
                self.user, {}, [faker.email()], headers={header_key: f"{header_value_part_1}\n\r{header_value_part_2}"}
            )
        except FailedToSendEmailException:
            # We're ignoring the exception as we're not sending actual email
            pass
        # Then
        message = mock_connection.send_messages.call_args[0][0][0]
        self.assertEqual(f"{header_value_part_1}{header_value_part_2}", message.extra_headers[header_key])
