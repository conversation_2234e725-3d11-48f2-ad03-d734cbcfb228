import factory
from factory import Sequence
from factory.django import DjangoModelFactory

from applications.models import EmailTemplate, Application, ExceptionList


class ApplicationFactory(DjangoModelFactory):
    name = Sequence(lambda n: f"Application : {n}")
    tag = Sequence(lambda n: f"application-tag-{n}")

    class Meta:
        model = Application


class EmailTemplateFactory(DjangoModelFactory):
    application = factory.SubFactory(ApplicationFactory)
    tag = Sequence(lambda n: f"email-template-{n}")

    class Meta:
        model = EmailTemplate


class ExceptionListFactory(DjangoModelFactory):
    class Meta:
        model = ExceptionList
