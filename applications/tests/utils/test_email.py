import base64

from django.test import SimpleTestCase
from faker import Faker

from applications.utils.email import (
    url_b64_decode,
    index_headers,
    html_to_text,
    get_email_domain,
    parse_all_emails_from_text,
    parse_all_domains_from_text,
    get_normalized_email,
)

faker = Faker()


class UrlB64DecodeTest(SimpleTestCase):
    def test_url_b64_decode__NoneInput__EmptyString(self):
        """
        test that an empty string is returned when None is provided
        """
        # Give
        encoded_string = None
        # When
        expected_result = ""
        result = url_b64_decode(encoded_string)
        # Then
        self.assertEqual(expected_result, result)

    def test_url_b64_decode__EmptyString__Success(self):
        """
        test that an empty string is returned when an empty string is provided
        """
        # Given
        encoded_string = ""
        # When
        expected_result = ""
        result = url_b64_decode(encoded_string)
        # Then
        self.assertEqual(expected_result, result)

    def test_url_b64_decode__ValidString__Success(self):
        """
        test that a valid encoded string is returned when a valid URL Safe Base64 encoded string is provided
        """
        # Given
        text = faker.text()
        encoded_string = base64.urlsafe_b64encode(text.encode()).decode()
        # When
        expected_result = text
        result = url_b64_decode(encoded_string)
        # Then
        self.assertEqual(expected_result, result)


class IndexHeadersTest(SimpleTestCase):
    def test_index_headers__NoneInput__EmptyDictionary(self):
        """
        test that an empty dictionary is returned when None is provided
        """
        # Given
        headers = None
        # When
        expected_result = {}
        result = index_headers(headers)
        # Then
        self.assertDictEqual(expected_result, result)

    def test_index_headers__EmptyList__Success(self):
        """
        test that an empty dictionary is returned when an empty list is provided
        """
        # Given
        headers = []
        # When
        expected_result = {}
        result = index_headers(headers)
        # Then
        self.assertDictEqual(expected_result, result)

    def test_index_headers__ValidList__Success(self):
        """
        test that a valid dictionary is returned when a valid list is provided
        """
        # Given
        headers = [{"name": faker.word(), "value": faker.word()} for _ in range(5)]
        # When
        result = index_headers(headers)
        expected_result = {header["name"]: header["value"] for header in headers}
        # Then
        self.assertDictEqual(expected_result, result)

    def test_index_headers__DuplicateHeaders__Success(self):
        """
        test that a dictionary is returned with the second value when a duplicate header is provided
        """
        # Given
        key = faker.word()
        value_1 = faker.word()
        value_2 = faker.word()
        headers = [{"name": key, "value": value_1}, {"name": key, "value": value_2}]
        # When
        expected_result = {headers[-1]["name"]: headers[-1]["value"]}
        result = index_headers(headers)
        # Then
        self.assertDictEqual(expected_result, result)


class HtmlToTextTest(SimpleTestCase):
    def setUp(self):
        self.heading = faker.sentence()
        self.paragraph = faker.paragraph()
        self.uri = faker.uri()
        self.uri_text = faker.sentence()

    def test_html_to_text__NoneInput__EmptyString(self):
        """
        test that an empty string is returned when None is provided
        """
        # Given
        html = None
        # When
        expected_result = ""
        result = html_to_text(html)
        # Then
        self.assertEqual(expected_result, result)

    def test_html_to_text__EmptyString__Success(self):
        """
        test that an empty string is returned when an empty string is provided
        """
        # Given
        html = ""
        # When
        expected_result = ""
        result = html_to_text(html)
        # Then
        self.assertEqual(expected_result, result)

    def test_html_to_text__PartialHtml__Success(self):
        """
        test that the content in body successfully returned when partial HTML is provided
        """
        # Given
        partial_html = f"""
            <h1>{self.heading}</h1>
            <a href="{self.uri}">{self.uri_text}</a>
            <div class="{self.heading}">{self.paragraph}</div>
            <script>
                console.log("Script tag");
            </script>
        """
        # When
        expected_result = f"{self.heading} {self.uri_text} {self.paragraph}"
        result = html_to_text(partial_html)
        # Then
        self.assertEqual(expected_result, result)

    def test_html_to_text__FullHtml__Success(self):
        """
        test that the content in body is returned successfully when full HTML is provided
        """
        # Given
        html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>{self.heading}</title>
                    <style>
                        body {{
                            font-family: Arial, sans-serif;
                            background-color: #f0f0f0;
                        }}
                        h1 {{
                            color: #333;
                        }}
                    </style>
                </head>
                <body>
                    <h1>{self.heading}</h1>
                    <p>{self.paragraph}</p>
                    <a href="{self.uri}">{self.uri_text}</a>
                    <script>
                        console.log("Script tag");
                    </script>
                </body>
                </html>
                """
        # When
        expected_result = f"{self.heading} {self.paragraph} {self.uri_text}"
        result = html_to_text(html)
        # Then
        self.assertEqual(expected_result, result)


class GetEmailDomainTest(SimpleTestCase):
    def test_get_email_domain__EmptyEmail__Success(self):
        """
        test that an empty string is returned when an empty string is provided
        """
        # Given
        email = ""
        # When
        expected_result = ""
        result = get_email_domain(email)
        # Then
        self.assertEqual(expected_result, result)

    def test_get_email_domain__ValidEmail__Success(self):
        """
        test that a correct domain list is returned when valid emails list is provided
        """
        # Given
        emails = [
            f"{faker.user_name()}@sales.acme.com",
            f"{faker.user_name()}@edu.ac.in",
            f"{faker.user_name()}@bbc.co.uk",
            f"{faker.user_name()}@finance.analytics.co.uk",
            f"{faker.user_name()}@project.management.io",
            f"{faker.user_name()}@it.support.techcorp.net",
            f"{faker.user_name()}@research.development.healthcaregroup.biz",
            f"{faker.user_name()}@gmail.com",
            f"{faker.user_name()}@yahoo.com",
            f"{faker.user_name()}@hotmail.com",
        ]
        # When
        expected_result = [
            "acme.com",
            "edu.ac.in",
            "bbc.co.uk",
            "analytics.co.uk",
            "management.io",
            "techcorp.net",
            "healthcaregroup.biz",
            "gmail.com",
            "yahoo.com",
            "hotmail.com",
        ]
        result = [get_email_domain(email) for email in emails]
        # Then
        self.assertListEqual(expected_result, result)


class ParseAllEmailsFromTextTest(SimpleTestCase):
    def test_parse_all_emails_from_text__EmptyEmail__Success(self):
        """
        test that an empty list is returned when an empty string is provided
        """
        # Given
        text = ""
        # When
        expected_result = []
        result = parse_all_emails_from_text(text)
        # Then
        self.assertListEqual(expected_result, result)

    def test_parse_all_emails_from_text__SingleEmail__Success(self):
        """
        test that a list with a single email is returned when text with a single email is provided
        """
        # Given
        email = faker.email()
        text = f"{faker.sentence()} {email}"
        # When
        expected_result = [email]
        result = parse_all_emails_from_text(text)
        # Then
        self.assertListEqual(expected_result, result)

    def test_parse_all_emails_from_text__MultipleEmail__Success(self):
        """
        test that a list with multiple emails is returned when text with multiple emails is provided
        """
        # Given
        emails = [faker.email() for _ in range(5)]
        text = f" {faker.sentence()} ".join(emails)
        # When
        expected_result = emails
        result = parse_all_emails_from_text(text)
        # Then
        self.assertListEqual(expected_result, result)


class ParseAllDomainsFromTextTest(SimpleTestCase):
    def test_parse_all_domains_from_text__EmptyEmail__Success(self):
        """
        test that an empty list is returned when an empty email is provided
        """
        # Given
        text = ""
        # When
        expected_result = []
        result = parse_all_domains_from_text(text)
        # Then
        self.assertListEqual(expected_result, result)

    def test_parse_all_domains_from_text__SingleEmail__Success(self):
        """
        test that a list with domain is returned when text with a single email is provided
        """
        # Given
        email = faker.email()
        parts = email.split("@")
        text = f"{faker.sentence()} {email}"
        # When
        expected_result = [parts[-1]]
        result = parse_all_domains_from_text(text)
        # Then
        self.assertListEqual(expected_result, result)

    def test_parse_all_domains_from_text__MultipleEmails__Success(self):
        """
        test that a list with multiple domains is returned when text with multiple emails is provided
        """
        # Given
        emails = [faker.email() for _ in range(5)]
        domains = list(map(lambda x: x.split("@")[-1], emails))
        text = f" {faker.sentence()} ".join(emails)
        # When
        expected_result = domains
        result = parse_all_domains_from_text(text)
        # Then
        self.assertListEqual(expected_result, result)


class GetNormalizedEmailTest(SimpleTestCase):
    def test_get_normalized_email__ValidEmail__Success(self):
        """
        test that a valid normalized email is returned when a valid email is provided
        """
        # Given
        email = faker.email()
        # When
        expected_result = email
        result = get_normalized_email(email)
        # Then
        self.assertEqual(expected_result, result)

    def test_get_normalized_email__CaseSensitiveEmail_Success(self):
        """
        test that a valid normalized email is returned when a case-sensitive email is provided
        """
        # Given
        user_name = faker.user_name()
        upper_case_user_name = user_name.upper()
        domain = faker.domain_name()
        email = f"{user_name}@{domain}"
        upper_case_email = f"{upper_case_user_name}@{domain}"
        # When
        expected_result = email
        result = get_normalized_email(upper_case_email)
        # Then
        self.assertEqual(expected_result, result)

    def test_get_normalized_email__SpecialCharacters__Success(self):
        """
        test that a valid normalized email is returned when a special character is provided with gmail.com as the domain
        """
        # Given
        email = [
            "<EMAIL>",
            "3-Bureau-Credit-Scores <EMAIL>",
        ]
        # When
        expected_result = [
            "<EMAIL>",
            "3-bureau-credit-scores <EMAIL>",
        ]
        result = list(get_normalized_email(email) for email in email)
        # Then
        self.assertListEqual(expected_result, result)
