import json
import logging

from django.contrib.auth import get_user_model
from django.db import transaction
from django.dispatch import receiver
from django_ses.signals import (
    click_received,
    open_received,
    bounce_received,
    complaint_received,
    send_received,
    delivery_received,
)

from applications.models import SentEmails
from applications.utils.email import (
    parse_ses_mail_obj,
    parse_ses_bounce_obj,
    parse_ses_complaint_obj,
    parse_ses_delivery_obj,
    parse_ses_click_obj,
    parse_ses_open_obj,
)

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(click_received)
@receiver(open_received)
@receiver(bounce_received)
@receiver(complaint_received)
@receiver(delivery_received)
@receiver(send_received)
def update_sent_email_status(mail_obj, raw_message, *args, **kwargs):
    """Update sent email status based on SES event

    Args:
        mail_obj (dict): A JSON object that contains information about the email that produced the event.
        raw_message (dict): SES raw message object
    """
    parsed_mail_obj = parse_ses_mail_obj(mail_obj)
    notification = json.loads(raw_message.decode("utf-8"))
    message = json.loads(notification["Message"])
    event_type = message.get("eventType", message.get("notificationType"))
    if not event_type:
        logger.exception("Event type not found in SES notification")
        return
    event_obj = kwargs.get(f"{event_type.lower()}_obj")
    logger.info(f"Received SES event for ses_message_id {parsed_mail_obj['ses_message_id']}, event_type {event_type}")
    if event_type == "Bounce":
        parsed_event_obj = parse_ses_bounce_obj(event_obj)
    elif event_type == "Complaint":
        parsed_event_obj = parse_ses_complaint_obj(event_obj)
    elif event_type == "Delivery":
        parsed_event_obj = parse_ses_delivery_obj(event_obj)
    elif event_type == "Send":
        # Send event object will always be empty
        parsed_event_obj = event_obj
    elif event_type == "Open":
        parsed_event_obj = parse_ses_open_obj(event_obj)
    elif event_type == "Click":
        parsed_event_obj = parse_ses_click_obj(event_obj)
    else:
        logger.exception("Invalid event type", extra={"event_type": event_type})
        return
    try:
        with transaction.atomic():
            sent_email = SentEmails.objects.select_for_update(of=("self",)).get(
                message_id=parsed_mail_obj["ses_message_id"]
            )
            if event_type == "Bounce":
                sent_email.status = SentEmails.STATUS_BOUNCED
                sent_email.metadata["bounce_obj"] = parsed_event_obj
                sent_email.save()
            elif event_type == "Complaint":
                sent_email.status = SentEmails.STATUS_COMPLAINED
                sent_email.metadata["complaint_obj"] = parsed_event_obj
                sent_email.save()
            elif event_type == "Delivery":
                sent_email.status = SentEmails.STATUS_DELIVERED
                sent_email.metadata["delivery_obj"] = parsed_event_obj
                sent_email.save()
            elif event_type == "Send":
                sent_email.status = SentEmails.STATUS_SENT
                sent_email.metadata["mail_obj"] = parsed_mail_obj
                sent_email.save()
            elif event_type == "Open":
                if sent_email.metadata.get("open_obj"):
                    sent_email.metadata["open_obj"].append(parsed_event_obj)
                else:
                    sent_email.metadata["open_obj"] = [parsed_event_obj]
                sent_email.metadata["opens"] = sent_email.metadata.get("opens", 0) + 1
                sent_email.save()
            elif event_type == "Click":
                if sent_email.metadata.get("click_obj"):
                    sent_email.metadata["click_obj"].append(parsed_event_obj)
                else:
                    sent_email.metadata["click_obj"] = [parsed_event_obj]
                sent_email.metadata["clicks"] = sent_email.metadata.get("clicks", 0) + 1
                sent_email.save()
    except SentEmails.DoesNotExist:
        logger.exception(
            "Sent email does not exist",
            extra={"ses_message_id": parsed_mail_obj["ses_message_id"], "event_type": event_type},
        )
    except Exception:
        logger.exception(
            "Error while updating sent email status",
            extra={"ses_message_id": parsed_mail_obj["ses_message_id"], "event_type": event_type},
        )
