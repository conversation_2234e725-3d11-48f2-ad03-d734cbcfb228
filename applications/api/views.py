import datetime
import logging

from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone
from rest_framework.authentication import SessionAuthentication
from rest_framework.decorators import action
from rest_framework.mixins import RetrieveModelMixin, ListModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet, ViewSet

from applications.api.authentication import LaunchDarklyWebhookAuthentication
from applications.api.serializers import ApplicationSerializer, CheckoutSessionSerializer
from applications.feature_flags.base import get_feature_flags
from applications.models import Application, FeatureFlagWebhookEvent
from applications.tasks import process_feature_flag_webhook_event
from execfn import ApplicationTag
from mailbot.utils.defaults import MailBotProfileMetadataKey
from mailbot.utils.email_scheduler import CreditCardMissingEmail
from mailbot.utils.pricing import get_pricing_version
from mailbot.utils.subscription import create_free_user
from mailbot.utils.check import (
    is_primary_profile,
)
from payments.models import StripeCustomer, StripeSubscription
from payments.utils.checkout_page import CheckoutFlow

logger = logging.getLogger(__name__)
User = get_user_model()


class StripeViewSet(ViewSet):
    @action(detail=False, methods=["POST"], url_path="create-checkout-session")
    def create_checkout_session(self, request):
        checkout_flow = CheckoutFlow()
        user_mailbot_profile = request.user.user_mailbot_profile
        current_pricing_version = get_pricing_version(user_mailbot_profile=user_mailbot_profile)
        serializer = CheckoutSessionSerializer(data=request.data, context={"pricing_version": current_pricing_version})
        serializer.is_valid(raise_exception=True)
        user = request.user
        # NOTE : If a free user is checking out, we need to delete the dummy subscription first
        try:
            stripe_subscription = StripeSubscription.objects.select_related("customer", "price").get(
                customer__user=user, status=StripeSubscription.STATUS_ACTIVE
            )
        except StripeSubscription.DoesNotExist:
            pass
        else:
            if stripe_subscription.price.nickname.lower() == "free":
                stripe_customer = stripe_subscription.customer
                stripe_customer.delete()
                user.refresh_from_db()

        price = serializer.validated_data["price"]
        show_onboarding_tour = (
            is_primary_profile(user_mailbot_profile) and not StripeCustomer.objects.filter(user=request.user).exists()
        )
        if price.nickname == "freebie":
            create_free_user(
                user_email=user.email,
                price_nickname=price.nickname,
                cancel_at=int((timezone.now() + timezone.timedelta(days=7)).timestamp()),
            )
            success_redirect_url = checkout_flow.get_success_redirect_url(price, show_onboarding_tour, user)
            return Response(data={"redirect_url": success_redirect_url})
        user_mailbot_profile.metadata[
            MailBotProfileMetadataKey.LAST_CHECKOUT_SESSION_CREATED_AT.value
        ] = timezone.now().isoformat()
        user_mailbot_profile.save(update_fields=("metadata",))
        CreditCardMissingEmail(user_mailbot_profile=user_mailbot_profile).schedule_email()

        redirect_url = checkout_flow.get_checkout_url(price, serializer.validated_data.get("coupon"), user, current_pricing_version, show_onboarding_tour)
        return Response(data={"redirect_url": redirect_url})


class ApplicationViewSet(RetrieveModelMixin, ListModelMixin, GenericViewSet):
    queryset = Application.objects.all()
    serializer_class = ApplicationSerializer
    lookup_field = "id"


class FeatureFlagViewSet(ViewSet):
    """
    ViewSet for handling feature flags.
    """

    @action(
        detail=False,
        url_path="",
        methods=["GET"],
        permission_classes=(IsAuthenticated,),
        authentication_classes=(SessionAuthentication,),
    )
    def get(self, request) -> Response:
        """
        Get feature flags for all apps.
        """
        user = self.request.user
        all_feature_flags = {}
        for application_tag in ApplicationTag:
            flags = get_feature_flags(user=user, application_tag=application_tag)
            all_feature_flags[application_tag.value] = flags
        return Response(data=all_feature_flags)

    @action(
        detail=False,
        url_path="webhook",
        methods=["POST"],
        permission_classes=(),
        authentication_classes=(LaunchDarklyWebhookAuthentication,),
    )
    def webhook(self, request) -> Response:
        """
        Webhook endpoint for feature flag updates.
        NOTE: Webhooks may not be delivered in chronological order, use the payload's "date" field as a timestamp to reorder webhooks as they are received.
        Retry from launch darkly is once if the webhook fails.
        Delivery is not guaranteed.
        """
        FeatureFlagWebhookEvent.objects.create(
            id=request.data["_id"],
            date=datetime.datetime.fromtimestamp(request.data["date"] / 1000, tz=datetime.timezone.utc),
            previous_version=request.data["previousVersion"],
            current_version=request.data["currentVersion"],
        )
        process_feature_flag_webhook_event.delay(request.data["_id"])
        return Response()
