import hashlib
import hmac

from django.conf import settings
from django.utils.encoding import force_bytes
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed


class LaunchDarklyWebhookAuthentication(BaseAuthentication):
    def authenticate(self, request):
        # https://docs.launchdarkly.com/home/<USER>/webhooks#sign-a-webhook
        if not (signature := request.headers.get("X-LD-Signature")):
            raise AuthenticationFailed("Missing signature header")
        computed_hash = hmac.new(
            force_bytes(settings.LAUNCH_DARKLY_WEBHOOK_SECRET), msg=request.body, digestmod=hashlib.sha256
        ).hexdigest()
        if not hmac.compare_digest(signature, computed_hash):
            raise AuthenticationFailed("Invalid signature")
        return None, None
