from django.urls import path
from django_ses.views import SESEventWebhookView
from rest_framework.routers import SimpleRouter

from applications.api.views import (
    ApplicationViewSet,
    StripeViewSet,
    FeatureFlagViewSet,
)

router = SimpleRouter()
router.register(r"feature-flags", FeatureFlagViewSet, basename="FeatureFlagViewSet")
router.register(r"stripe", StripeViewSet, basename="StripeViewSet")
router.register(r"", ApplicationViewSet)

urlpatterns = router.urls

urlpatterns.extend(
    [
        path(r"ses/event-webhook/", SESEventWebhookView.as_view(), name="ses-event-webhook"),
    ]
)
