from django.contrib.auth import get_user_model
from rest_framework import serializers

from applications.models import Application
from payments.models import StripePrice, StripeCoupon, CouponPriceThrough

User = get_user_model()


class ApplicationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Application
        fields = ("id", "name", "tag", "description", "trial_period")


class CheckoutSessionSerializer(serializers.Serializer):
    price = serializers.PrimaryKeyRelatedField(queryset=StripePrice.objects.filter(active=True))
    coupon = serializers.PrimaryKeyRelatedField(queryset=StripeCoupon.objects.all(), required=False)

    def validate(self, attrs):
        price = attrs.get("price")
        coupon = attrs.get("coupon")
        version = self.context["pricing_version"]
        if not CouponPriceThrough.objects.filter(
            coupon=coupon,
            price=price,
            version=version,
        ).exists():
            raise serializers.ValidationError(
                f"Price {price.nickname}, coupon {coupon.name} is not supported in pricing version {version}"
            )
        return attrs
