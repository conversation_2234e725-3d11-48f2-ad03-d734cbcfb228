import logging

import O365.message
from django.core.mail.backends.base import BaseEmailBackend
from django.core.mail.message import EmailMultiAlternatives

from applications.utils.outlook import OutlookService, Message

logger = logging.getLogger(__name__)


class OutlookEmailBackend(BaseEmailBackend):
    def __init__(self, email, auth_tokens, fail_silently=False, **kwargs):
        self.email = email
        self.fail_silently = fail_silently
        self.service = OutlookService(auth_tokens)

    def send_messages(self, messages: list[EmailMultiAlternatives]) -> int:
        """
        Sends one or more EmailMultiAlternatives objects and returns the number of
        email messages sent.

        Args:
            messages: Messages to be sent

        Returns:
            int: Number of messages sent successfully!
        """
        num_sent = 0
        for message in messages:
            try:
                # Create a new Outlook message
                outlook_message: Message = self.service.create_message()
                self.create_message(message, outlook_message)
                message_id = self.send_message(outlook_message)
                message.extra_headers["status"] = 200
                message.extra_headers["message_id"] = message_id
                num_sent += 1
            except Exception as err:
                error_keys = ["status", "reason"]
                for key in error_keys:
                    message.extra_headers[key] = getattr(err, key, None)
                if not self.fail_silently:
                    logger.exception(
                        "Failed to send message using outlook api",
                        extra={"email": self.email, "extra_headers": message.extra_headers},
                    )
        return num_sent

    def create_message(self, message: EmailMultiAlternatives, outlook_message: O365.message.Message):
        """Prepares outlook message to be sent

        Args:
            message: Message to be sent

        Returns:
            Outlook message object
        """
        outlook_message.to.add(message.to)
        outlook_message.subject = message.subject
        outlook_message.body = message.body
        if message.alternatives:
            text_part = message.alternatives[0]  # Assuming the first alternative is text/plain
            outlook_message.body = text_part[0]
        # TODO: Extend this to support HTML body and attachments

    def send_message(self, message: O365.message.Message) -> str:
        """
        Sends outlook message

        Args:
            message: Message to be sent

        Returns:
            str: Outlook message id
        """
        sent = message.send()
        if sent:
            return message.object_id

    def send_reply(self, message, original_message_id, **kwargs) -> str:
        """Sends outlook reply

        Args:
            message: Message to be sent
            original_message_id: ID of the original message to which the reply should be sent

        Returns:
            str: Outlook message id
        """
        try:
            outlook_message: Message = OutlookService.create_reply(original_message_id)
            self.create_message(message, outlook_message)
            outlook_message.send()
            message_id = outlook_message.object_id
            message.extra_headers["status"] = 200
            message.extra_headers["message_id"] = message_id
            return message_id
        except Exception as err:
            error_keys = ["status", "reason"]
            for key in error_keys:
                message.extra_headers[key] = getattr(err, key, None)
            if not self.fail_silently:
                logger.exception(
                    "Failed to send reply using outlook api",
                    extra={"email": self.email, "extra_headers": message.extra_headers},
                )

    def create_draft(self, message) -> str:
        """Creates draft message

        Args:
            message: Message to be sent

        Returns:
            str: Outlook message id
        """
        try:
            outlook_message: Message = self.create_message(message)
            self.create_message(message, outlook_message)
            outlook_message.save_draft()
            message_id = outlook_message.object_id
            message.extra_headers["status"] = 200
            message.extra_headers["message_id"] = message_id
            return message_id
        except Exception as err:
            error_keys = ["status", "reason"]
            for key in error_keys:
                message.extra_headers[key] = getattr(err, key, None)
            if not self.fail_silently:
                logger.exception(
                    "Failed to create draft using outlook api",
                    extra={"email": self.email, "extra_headers": message.extra_headers},
                )
