import base64
import logging

from django.core.mail import EmailMultiAlternatives
from django.core.mail.backends.base import BaseEmailBackend

from applications.utils.gmail import GmailService

logger = logging.getLogger(__name__)


class GmailEmailBackend(BaseEmailBackend):
    def __init__(self, email, auth_tokens, fail_silently=False, **kwargs):
        self.email = email
        self.fail_silently = fail_silently
        access_token = auth_tokens["access_token"]
        refresh_token = auth_tokens["refresh_token"]
        self.service = GmailService(email=email, access_token=access_token, refresh_token=refresh_token)

    def send_messages(self, messages: list[EmailMultiAlternatives]) -> int:
        """
        Sends one or more EmailMultiAlternatives objects and returns the number of
        email messages sent.

        Args:
            messages: Messages to be sent

        Returns:
            int: Number of messages sent successfully!
        """
        num_sent = 0
        for message in messages:
            try:
                raw_message = self.create_message(message)
                message_id = self.send_message(raw_message)
                message.extra_headers["status"] = 200
                message.extra_headers["message_id"] = message_id
                num_sent += 1
            except Exception as err:
                error_keys = ["status", "reason"]
                for key in error_keys:
                    message.extra_headers[key] = getattr(err, key, None)
                if not self.fail_silently:
                    logger.exception(
                        "Failed to send message using gmail api",
                        extra={"email": self.email, "extra_headers": message.extra_headers},
                    )
        return num_sent

    def create_message(self, message) -> dict:
        """
        Prepares message to be sent by encoding EmailMultiAlternatives object

        Args:
            message: Message to be sent

        Returns:
            dict: Dictionary with base64 encoded url string representing the message object
        """
        raw_message = base64.urlsafe_b64encode(message.message().as_bytes()).decode("utf-8")
        return {"raw": raw_message}

    def send_message(self, message) -> str:
        """
        Sends message using GmailService

        Args:
            message: Message to be sent

        Returns:
            str: Gmail message id
        """
        return self.service.send_message(message)

    def send_reply(self, message, original_message_id, thread_id) -> str:
        """Sends reply using GmailService

        Args:
            message: Message to be sent
            original_message_id: ID of the original message to which the reply should be sent
            thread_id: ID of the thread

        Returns:
            str: Gmail message id
        """
        try:
            message.extra_headers["In-Reply-To"] = original_message_id
            message.extra_headers["References"] = original_message_id
            raw_message = self.create_message(message)
            raw_message["threadId"] = thread_id
            message_id = self.send_message(raw_message)
            message.extra_headers["status"] = 200
            message.extra_headers["message_id"] = message_id
            return message_id
        except Exception as err:
            error_keys = ["status", "reason"]
            for key in error_keys:
                message.extra_headers[key] = getattr(err, key, None)
            if not self.fail_silently:
                logger.exception(
                    "Failed to send reply using gmail api",
                    extra={"email": self.email, "extra_headers": message.extra_headers},
                )

    def create_draft(self, message, original_message_id, thread_id) -> str:
        """Creates draft using GmailService

        Args:
            message: Message to be sent
            original_message_id: ID of the original message to which the reply should be sent
            thread_id: ID of the thread

        Returns:
            str: Gmail message id
        """
        try:
            message.extra_headers["In-Reply-To"] = original_message_id
            message.extra_headers["References"] = original_message_id
            raw_message = self.create_message(message)
            raw_message["threadId"] = thread_id
            message_id = self.service.create_draft({"message": raw_message})
            message.extra_headers["status"] = 200
            message.extra_headers["message_id"] = message_id
            return message_id
        except Exception as err:
            error_keys = ["status", "reason"]
            for key in error_keys:
                message.extra_headers[key] = getattr(err, key, None)
            if not self.fail_silently:
                logger.exception(
                    "Failed to create draft using gmail api",
                    extra={"email": self.email, "extra_headers": message.extra_headers},
                )
