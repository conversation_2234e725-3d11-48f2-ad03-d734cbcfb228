import logging

from django.conf import settings

from applications.feature_flags.defaults import FeatureFlagEnvironment
from applications.feature_flags.signals import feature_flag_target_users_changed
from applications.models import FeatureFlagWebhookEvent, ScheduledTask
from execfn.celery import app
from execfn.settings import APP_ENV_PROD, APP_ENV_DEV, APP_ENV_LOCAL

logger = logging.getLogger(__name__)


@app.task
def cleanup_expired_tasks():
    deleted = ScheduledTask.cleanup_one_off_tasks()
    logger.info(f"Deleted {deleted} expired tasks")


@app.task
def process_feature_flag_webhook_event(event_id: int):
    try:
        event = FeatureFlagWebhookEvent.objects.get(id=event_id)
    except FeatureFlagWebhookEvent.DoesNotExist:
        logger.error("Feature flag webhook event not found", extra={"event_id": event_id})
    else:
        if event.status == FeatureFlagWebhookEvent.EVENT_STATUS_SUCCEEDED:
            logger.info(f"Event {event_id} already processed")
            return
        FeatureFlagWebhookEvent.save_status(event_id=event_id, status=FeatureFlagWebhookEvent.EVENT_STATUS_PROCESSING)
        try:
            assert event.previous_version["key"] == event.current_version["key"]
            flag = event.previous_version["key"]
            prev_envs = event.previous_version["environments"]
            cur_envs = event.current_version["environments"]
            for env, prev_env_value in prev_envs.items():
                env_name = prev_env_value["_environmentName"]
                cur_env_value = cur_envs[env]
                assert env_name == cur_env_value["_environmentName"]
                if not (
                    (env_name == FeatureFlagEnvironment.PRODUCTION.value and settings.APP_ENV == APP_ENV_PROD)
                    or (env_name == FeatureFlagEnvironment.DEVELOPMENT.value and settings.APP_ENV == APP_ENV_DEV)
                    or (env_name == FeatureFlagEnvironment.OFFLINE.value and settings.APP_ENV == APP_ENV_LOCAL)
                ):
                    continue
                was_feature_flag_on = prev_env_value["on"]
                is_feature_flag_on = cur_env_value["on"]

                prev_targets = prev_env_value["targets"]
                cur_targets = cur_env_value["targets"]
                prev_target_users = set(prev_targets[0]["values"]) if prev_targets else set()
                cur_target_users = set(cur_targets[0]["values"]) if cur_targets else set()
                if (
                    was_feature_flag_on == is_feature_flag_on
                    and was_feature_flag_on == True
                    and prev_target_users != cur_target_users
                ):
                    logger.info("Feature flag target users changed")
                    # Feature flag is turned on and only target users are changed
                    feature_flag_target_users_changed.send(
                        sender=FeatureFlagWebhookEvent,
                        flag=flag,
                        turned_on_for_users=cur_target_users - prev_target_users,
                        turned_off_for_users=prev_target_users - cur_target_users,
                    )
                elif was_feature_flag_on != is_feature_flag_on and prev_target_users == cur_target_users:
                    # Feature flag is toggled but target users are not changed
                    if is_feature_flag_on:
                        logger.info(f"Feature flag {flag} turned on")
                        feature_flag_target_users_changed.send(
                            sender=FeatureFlagWebhookEvent,
                            flag=flag,
                            turned_on_for_users=prev_target_users,
                            turned_off_for_users=set(),
                        )
                    else:
                        logger.info(f"Feature flag {flag} turned off")
                        feature_flag_target_users_changed.send(
                            sender=FeatureFlagWebhookEvent,
                            flag=flag,
                            turned_on_for_users=set(),
                            turned_off_for_users=prev_target_users,
                        )
                else:
                    # TODO: Handle other cases where multiple actions are performed in a single webhook event
                    logger.error("Feature flag webhook event is not handled", extra={"event_id": event_id})
        except Exception as e:
            FeatureFlagWebhookEvent.save_status(event_id=event_id, status=FeatureFlagWebhookEvent.EVENT_STATUS_FAILED)
            raise e
        else:
            FeatureFlagWebhookEvent.save_status(
                event_id=event_id, status=FeatureFlagWebhookEvent.EVENT_STATUS_SUCCEEDED
            )
