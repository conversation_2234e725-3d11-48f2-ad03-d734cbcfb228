from enum import Enum


class MailBotFeatureFlag(Enum):
    EMAILZAP = "email_zap"
    LUCENE_CALENDAR_EVENTS = "lucene-calendar-events"
    LUCENE_ORDER_TICKET = "lucene-order-ticket"
    LUCENE_OTP = "lucene-otp"
    LUCENE_PASSWORD = "lucene-password"
    LUCENE_PAYMENT_ALERT = "lucene-payment-alert"
    LUCENE_SECURITY_ALERT = "lucene-security-alert"
    LUCENE_BANK_STATEMENTS = "lucene-bank-statements"
    ARCHIVE_WITH_OVERLAY = "archive-with-overlay"
    DIGEST_EMAIL = "digest_email"
    AUTO_ARCHIVAL = "auto-archival"
    FIRST_TIME_SENDER_OVERLAY = "first-time-sender-overlay"
    AI_AUTO_RESPONDER = "ai-auto-responder"
    JARVIS = "jarvis"
    SECONDARY_PROFILES = "secondary-profiles"
    ZERO_INBOX = "zero-inbox"
    ARCHIVING_EMAILZAP = "archiving-emailzap"
    BULK_DELETE = "bulk-delete"
    UNSUBSCRIBE = "unsubscribe"


class FeatureFlagEnvironment(Enum):
    OFFLINE = "Offline"
    DEVELOPMENT = "Development"
    PRODUCTION = "Production"
