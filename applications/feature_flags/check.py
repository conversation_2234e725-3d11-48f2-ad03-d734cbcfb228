import logging

import yaml
from django.conf import settings
from django.contrib.auth import get_user_model

from applications.feature_flags.base import get_feature_flags
from applications.feature_flags.registry import FeatureFlagHierarchy
from execfn import ApplicationTag
from execfn.settings import APP_ENV_TEST

logger = logging.getLogger(__name__)
User = get_user_model()

# Build forest for all applications
feature_flag_hierarchy_registry = {}
with open("applications/feature_flags.yaml") as stream:
    structure = yaml.safe_load(stream)
    for application_name, entry_structure in structure.items():
        ffh = FeatureFlagHierarchy(entry_structure)
        # Append feature flag tree in the forest
        feature_flag_hierarchy_registry[application_name] = ffh


def _get_or_set(user_id, application_tag: ApplicationTag):
    """
    Retrieve feature flags for a user if the request ID is same, else fetch feature flags from API.

    Args:
        user_id: User ID whose feature flags are retrieved
        application_tag: Application tag
    Returns:
         dict: Dictionary containing feature flags for a user
    """
    user = User.objects.get(id=user_id)
    return get_feature_flags(user=user, application_tag=application_tag)


def check_feature(user_id, feature_flag, application_tag: ApplicationTag) -> bool:
    """
    Check whether the feature flag is enabled for the given user.
    If any feature flag in the hierarchy above the current feature flag is disabled, we consider the current feature flag disabled.

    Args:
        user_id: User ID of user whose feature flag is to be checked
        application_tag: Application tag for which feature flag is to be checked
        feature_flag: Feature flag to be checked

    Returns:
        bool: True if the feature flag is enabled for the given user
    """
    if settings.APP_ENV == APP_ENV_TEST:
        return True
    flags = _get_or_set(user_id=user_id, application_tag=application_tag)
    feature_flag_hierarchy = feature_flag_hierarchy_registry[application_tag.value]
    is_feature_flag_enabled = feature_flag_hierarchy.check_flag(flags, feature_flag.value)
    if not is_feature_flag_enabled:
        logger.info(f"Feature flag {feature_flag} is disabled for user {user_id}")
        return False
    else:
        return True
