from contextlib import ContextDecorator
from unittest import mock


class mock_feature_flag(ContextDecorator):
    """
    A dual-use mock for `check_feature` that can be used as a context manager or a decorator.

    Args:
        feature_flag: The feature flag to mock.
        enabled (bool): Whether the feature flag should return True (enabled) or False (disabled).
    """

    def __init__(
        self,
        feature_flag=None,
        enabled=True,
        default=True,
        check_import="applications.feature_flags.check.check_feature",
    ):
        self.feature_flag_name = feature_flag
        self.check_import = check_import
        self.enabled = enabled
        self.default = default
        self.patcher = None

    def _mock_check_feature(self, user_id, feature_flag, application_tag):
        if feature_flag == self.feature_flag_name:
            return self.enabled
        return self.default

    def __enter__(self):
        self.patcher = mock.patch(
            self.check_import,
            side_effect=self._mock_check_feature,
        )
        self.patcher.start()
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        if self.patcher:
            self.patcher.stop()
            self.patcher = None
