import inspect
import logging
from importlib import import_module

from django.utils.functional import LazyObject

from applications.feature_flags.registry import FeatureFlagRegistry
from execfn import ApplicationTag

logger = logging.getLogger(__name__)


class FeatureFlagBaseApplication:
    application_tag = None

    @staticmethod
    def get_base_user_info(user):
        """
        Retrieve base user information for feature flags.

        Args:
            user : User.
        Returns:
            dict: Dictionary containing base user information
        """
        response = {"email": user.email}
        return response

    @staticmethod
    def get_properties(user, *args, **kwargs):
        response = FeatureFlagBaseApplication.get_base_user_info(user)
        return response


def scan_feature_flag_application(application_tag: ApplicationTag):
    module_path = f"{application_tag.value}.feature_flags"
    try:
        feature_flags_module = import_module(module_path)
    except ModuleNotFoundError:
        return
    else:
        module_classes = inspect.getmembers(feature_flags_module, inspect.isclass)
        feature_flag_applications = [
            cls
            for name, cls in module_classes
            if issubclass(cls, FeatureFlagBaseApplication) and cls.__module__ == module_path
        ]
        if len(feature_flag_applications) == 0:
            logger.exception("Feature flag application not found", extra={"module_path": module_path})
        elif len(feature_flag_applications) > 1:
            logger.exception("Multiple feature flag applications found", extra={"module_path": module_path})
        else:
            return feature_flag_applications[0]


class FeatureFlagApplications(LazyObject):
    def _setup(self):
        self._wrapped = {
            application_tag.value: scan_feature_flag_application(application_tag=application_tag)
            for application_tag in ApplicationTag
        }


feature_flag_applications = FeatureFlagApplications()


def get_feature_flags(user, application_tag: ApplicationTag):
    feature_flag_application = feature_flag_applications[application_tag.value]
    if feature_flag_application:
        user_application_properties = feature_flag_application.get_properties(user)
        return FeatureFlagRegistry.get_flags(application_tag.value, user_properties=user_application_properties)
