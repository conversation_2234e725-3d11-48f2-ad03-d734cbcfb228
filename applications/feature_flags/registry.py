import logging
from typing import Dict, Optional, Any

from django.conf import settings
from ldclient import Context, ContextBuilder, LDClient
from ldclient.config import Config

from execfn import ApplicationTag

logger = logging.getLogger(__name__)


class LaunchDarklyMap:
    def __init__(self):
        self._wrapped = {}
        for application_tag in ApplicationTag:
            sdk_key = getattr(settings, f"{application_tag.value.upper()}_LD_SECRET_KEY", None)
            if sdk_key:
                self._wrapped[application_tag.value] = LDClient(Config(sdk_key))
                logger.info(f"Launch Darkly Map initialized for application {application_tag.value}")

    def __getitem__(self, item):
        return self._wrapped[item]


launch_darkly_instance_map = LaunchDarklyMap()


def reset_launch_darkly_instance_map():
    global launch_darkly_instance_map
    launch_darkly_instance_map = LaunchDarklyMap()


class FeatureFlagRegistry:
    """
    Base class for handling application feature flags.
    """

    @staticmethod
    def _get_context_builder(properties: Dict) -> ContextBuilder:
        """
        Build context for evaluating feature flags.

        Args:
            properties (dict): User properties.

        Returns:
            ContextBuilder: Context builder for evaluating feature flags.
        """
        context_builder = Context.builder(properties["email"])
        context_builder.set("kind", "user")
        for key, value in properties.items():
            context_builder = context_builder.set(key, value)
        return context_builder

    @staticmethod
    def get_flags(
        application_tag_value: str, user_properties: Dict, flag_name: Optional[str] = None
    ) -> Dict[str, bool]:
        """
        Retrieve feature flags for the specified application.

        Args:
            application_tag_value: The tag of the application.
            user_properties: User properties.
            flag_name: The specific feature flag to retrieve (default: None).

        Returns:
            Dict: A dictionary containing feature flag(s) and their respective values.

        Note:
            If flag_name is not provided, all flags for the application will be retrieved.
        """
        application_ld_instance: LDClient = launch_darkly_instance_map[application_tag_value]
        context = FeatureFlagRegistry._get_context_builder(user_properties).build()
        if flag_name:
            return {flag_name: application_ld_instance.variation(key=flag_name, context=context, default=False)}
        return application_ld_instance.all_flags_state(context).to_values_map()


class FeatureFlagHierarchy:
    """
    A feature flag hierarchy is used to add functionality of checking feature flag up to the pre-requisite flags.
    Prerequisite flags are flags that must be enabled in order for other descendant flags in hierarchy to work.

    References:
        https://docs.launchdarkly.com/guides/flags/flag-hierarchy
    """

    def __init__(self, structure):
        # Auto incrementer as more nodes are added to the tree
        self._idx = 1
        # We build tree on integers, so we need to keep track of what integer is given to which feature flag name
        self._feature_flag_to_idx = {}
        # Parent array keeps track of the parent of current node, so parent of node x is parent[x]
        self._parent = [0]
        # Converting index back to feature flag, with 0 index as dummy "root" value
        self._idx_to_feature_flag = ["root"]
        self._build_prerequisite_hierarchy(structure)

    def _build_prerequisite_hierarchy(self, s, p=0):
        """
        Build prerequisite feature flag hierarchy for given application's feature flag dictionary.

        Args:
            s: A dictionary or list containing feature flag hierarchy
            p: The prerequisite feature flag from previous call to _build_prerequisite_hierarchy

        Examples:
            Initial s = {'a': ['b', 'c', {'d': {'e': ['f', 'g']}}]}

            _feature_flag_to_idx = {'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5, 'f': 6, 'g': 7}

            _parent = [0, 0, 1, 1, 3, 4, 5, 5]
        """
        if isinstance(s, list):
            for x in s:
                if isinstance(x, str):
                    self._feature_flag_to_idx[x] = self._idx
                    self._idx_to_feature_flag.append(x)
                    self._parent.append(p)
                    self._idx += 1
                elif isinstance(x, dict):
                    self._build_prerequisite_hierarchy(x, p)
                else:
                    raise TypeError(f"Unexpected type {type(x)}")
            return
        elif isinstance(s, dict):
            assert len(s.keys()) == 1
            x = list(s.keys())[0]
            self._feature_flag_to_idx[x] = self._idx
            self._idx_to_feature_flag.append(x)
            self._parent.append(p)
            self._idx += 1
            self._build_prerequisite_hierarchy(list(s.values())[0], p=self._idx - 1)
        else:
            raise TypeError(f"Unexpected type {type(s)}")

    def check_flag(self, flags: Dict[str, Any], key: str) -> bool:
        """
        Check that all feature flags are enabled for the key passed in the hierarchy.

        Args:
            flags: All flags for current context user
            key: Flag key to check for current context user

        Returns:
            bool: True if all flags are enabled for the key passed in the hierarchy, False otherwise
        """
        if key not in flags:
            # If key is deleted from launch darkly then consider it as a True value
            return True
        elif flags[key] is True:
            idx = self._feature_flag_to_idx[key]
            p = self._parent[idx]
            if p == 0:
                return True
            return self.check_flag(flags, self._idx_to_feature_flag[p])
        else:
            return False
