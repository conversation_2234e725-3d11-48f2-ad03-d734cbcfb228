# Generated by Django 4.2.5 on 2023-10-27 09:21

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("applications", "0002_application_trial_period"),
    ]

    operations = [
        migrations.AddField(
            model_name="subscription",
            name="subscription_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("active", "Active"),
                    ("cancel_at_period_end", "Cancel at period end"),
                    ("cancelled", "Cancelled"),
                ],
                max_length=32,
                null=True,
            ),
        ),
        migrations.AddConstraint(
            model_name="subscription",
            constraint=models.UniqueConstraint(
                condition=models.Q(("subscription_status", "active")),
                fields=("user", "application_id"),
                name="unique_active_status_user_subscription",
            ),
        ),
    ]
