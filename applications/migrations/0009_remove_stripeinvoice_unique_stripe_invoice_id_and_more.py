# Generated by Django 4.2.5 on 2023-11-22 10:31

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("applications", "0008_scheduledtask"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="stripeinvoice",
            name="unique_stripe_invoice_id",
        ),
        migrations.AddField(
            model_name="application",
            name="grace_period",
            field=models.IntegerField(default=7),
        ),
        migrations.AlterField(
            model_name="stripeinvoice",
            name="stripe_invoice_id",
            field=models.Char<PERSON>ield(max_length=32, unique=True),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="stripe_subscription_id",
            field=models.Char<PERSON>ield(blank=True, max_length=32, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name="subscription",
            name="subscription_status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("cancel_at_period_end", "Cancel at period end"),
                    ("cancelled", "Cancelled"),
                    ("grace", "Grace"),
                ],
                db_index=True,
                max_length=32,
            ),
        ),
    ]
