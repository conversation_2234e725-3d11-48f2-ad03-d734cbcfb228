# Generated by Django 4.2.5 on 2024-01-19 10:43

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("applications", "0012_alter_subscription_subscription_type"),
    ]

    operations = [
        migrations.AddField(
            model_name="application",
            name="linked_accounts_max_count",
            field=models.IntegerField(default=0, help_text="Maximum count of user accounts that can be linked"),
        ),
        migrations.AddField(
            model_name="subscription",
            name="secondary_users",
            field=models.ManyToManyField(related_name="primary_users", to=settings.AUTH_USER_MODEL),
        ),
    ]
