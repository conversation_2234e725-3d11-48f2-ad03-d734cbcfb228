# Generated by Django 4.2.5 on 2023-11-08 10:33

import applications.models
import applications.storages
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("applications", "0005_emailtemplate_alter_paymentplan_options_sentemails"),
    ]

    operations = [
        migrations.CreateModel(
            name="StripeInvoice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("stripe_invoice_id", models.CharField(max_length=32)),
                ("amount_due", models.IntegerField()),
                ("amount_paid", models.IntegerField()),
                ("attempt_count", models.IntegerField()),
                ("attempted", models.BooleanField()),
                (
                    "billing_reason",
                    models.CharField(
                        choices=[
                            ("manual", "Manual"),
                            ("subscription_create", "Subscription create"),
                            ("subscription_cycle", "Subcription cycle"),
                            ("subscription_threshold", "Subscription threshold"),
                            ("subscription_update", "Subscription update"),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    "collection_method",
                    models.CharField(
                        choices=[
                            ("charge_automatically", "Charge automatically"),
                            ("send_invoice", "Send Invoice"),
                        ],
                        max_length=32,
                    ),
                ),
                ("currency", models.CharField(max_length=8)),
                ("created", models.DateTimeField()),
                ("period_end", models.DateTimeField()),
                ("period_start", models.DateTimeField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("open", "Open"),
                            ("paid", "Paid"),
                            ("uncollectible", "Uncollectible"),
                            ("void", "Void"),
                        ],
                        max_length=32,
                    ),
                ),
                ("status_transitions", models.JSONField()),
                (
                    "invoice",
                    models.FileField(
                        storage=applications.storages.MediaStorage(),
                        upload_to=applications.models.invoice_upload_path,
                    ),
                ),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="applications.application",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Stripe Invoices",
            },
        ),
        migrations.AddConstraint(
            model_name="stripeinvoice",
            constraint=models.UniqueConstraint(fields=("stripe_invoice_id",), name="unique_stripe_invoice_id"),
        ),
    ]
