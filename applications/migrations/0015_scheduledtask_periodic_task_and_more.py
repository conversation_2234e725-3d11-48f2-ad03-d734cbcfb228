# Generated by Django 4.2.5 on 2024-01-23 09:53

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("django_celery_beat", "0018_improve_crontab_helptext"),
        ("applications", "0014_sentemails_metadata_alter_sentemails_status"),
    ]

    operations = [
        migrations.AddField(
            model_name="scheduledtask",
            name="periodic_task",
            field=models.OneToOneField(
                blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to="django_celery_beat.periodictask"
            ),
        ),
        migrations.AlterField(
            model_name="scheduledtask",
            name="scheduled_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="scheduledtask",
            name="status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("scheduled", "Scheduled"),
                    ("processing", "Processing"),
                    ("processed", "Processed"),
                    ("failed", "Failed"),
                    ("cancelled", "Cancelled"),
                ],
                max_length=16,
                null=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="scheduledtask",
            name="tag",
            field=models.CharField(blank=True, max_length=64, null=True),
        ),
    ]
