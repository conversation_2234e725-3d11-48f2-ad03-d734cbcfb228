# Generated by Django 4.2.5 on 2023-11-30 08:39

from django.db import migrations, models
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ("applications", "0010_stripeinvoice_receipt"),
    ]

    operations = [
        migrations.CreateModel(
            name="ExceptionList",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("type", models.CharField(choices=[("domain", "Domain"), ("email", "Email")], max_length=8)),
                ("label_name", models.CharField(max_length=128)),
                ("exception_address", models.Char<PERSON><PERSON>(max_length=254, unique=True)),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
    ]
