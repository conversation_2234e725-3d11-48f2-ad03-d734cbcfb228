# Generated by Django 4.2.5 on 2024-01-25 09:25

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("applications", "0015_scheduledtask_periodic_task_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="subscription",
            name="secondary_users",
            field=models.ManyToManyField(blank=True, related_name="primary_users", to=settings.AUTH_USER_MODEL),
        ),
    ]
