# Generated by Django 4.2.5 on 2023-10-31 07:32

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("applications", "0003_subscription_subscription_status_and_more"),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name="subscription",
            name="unique_active_user_subscription",
        ),
        migrations.RemoveField(
            model_name="subscription",
            name="is_active",
        ),
        migrations.AlterField(
            model_name="subscription",
            name="subscription_status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("cancel_at_period_end", "Cancel at period end"),
                    ("cancelled", "Cancelled"),
                ],
                db_index=True,
                max_length=32,
            ),
        ),
    ]
