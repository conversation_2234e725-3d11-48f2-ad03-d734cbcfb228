# Generated by Django 4.2.5 on 2023-11-02 11:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        (
            "applications",
            "0004_remove_subscription_unique_active_user_subscription_and_more",
        ),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("tag", models.CharField(max_length=64)),
                ("subject_template", models.Char<PERSON>ield(max_length=200)),
                ("text_template", models.TextField()),
                ("html_template", models.TextField(blank=True, null=True)),
                ("amp_template", models.TextField(blank=True, null=True)),
                (
                    "from_email",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                ("from_name", models.CharField(blank=True, max_length=128, null=True)),
                ("default_headers", models.JSONField(blank=True, null=True)),
                (
                    "application",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="applications.application",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Email Templates",
                "unique_together": {("application", "tag")},
            },
        ),
        migrations.AlterModelOptions(
            name="paymentplan",
            options={"verbose_name_plural": "Payment Plans"},
        ),
        migrations.CreateModel(
            name="SentEmails",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("ses_message_id", models.CharField(max_length=64)),
                (
                    "status",
                    models.CharField(blank=True, choices=[("sent", "Sent")], max_length=64, null=True),
                ),
                (
                    "template",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="applications.emailtemplate",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Sent Emails",
            },
        ),
    ]
