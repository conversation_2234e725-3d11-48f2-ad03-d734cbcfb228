# Generated by Django 4.2.5 on 2024-08-25 13:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("django_celery_beat", "0018_improve_crontab_helptext"),
        ("applications", "0022_remove_paymentplan_application_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="scheduledtask",
            name="scheduled_at",
        ),
        migrations.RemoveField(
            model_name="scheduledtask",
            name="status",
        ),
        migrations.RemoveField(
            model_name="scheduledtask",
            name="tag",
        ),
        migrations.AlterField(
            model_name="scheduledtask",
            name="periodic_task",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to="django_celery_beat.periodictask"
            ),
        ),
    ]
