import json
import logging
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional, Tuple

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.files.base import File
from django.core.mail import EmailMultiAlternatives, get_connection
from django.db import models, transaction
from django.template import Template, Context
from django_celery_beat.models import PeriodicTask, IntervalSchedule, ClockedSchedule
from django_extensions.db.models import TimeStampedModel

from applications.exceptions import FailedToSendEmailException, FailedToCreateDraftException
from applications.utils.base import minify_html_string
from applications.utils.email import EmailBackend
from execfn.settings import APP_ENV_TEST

User = get_user_model()
logger = logging.getLogger(__name__)


def invoice_upload_path(instance, filename):
    """Returns the upload path for invoice pdf"""
    return f"stripe/invoices/{instance.user_id}/{instance.application_id}/{filename}"


def receipt_upload_path(instance, filename):
    """Returns the upload path for receipt pdf"""
    return f"stripe/receipts/{instance.user_id}/{instance.application_id}/{filename}"


class PeriodicTaskIntervals(Enum):
    WEEKS = "weeks"
    DAYS = IntervalSchedule.DAYS
    HOURS = IntervalSchedule.HOURS
    MINUTES = IntervalSchedule.MINUTES
    SECONDS = IntervalSchedule.SECONDS
    MICROSECONDS = IntervalSchedule.MICROSECONDS


class Application(TimeStampedModel):
    name = models.CharField(max_length=128)
    tag = models.CharField(max_length=32, unique=True)
    description = models.TextField()
    stripe_product_id = models.CharField(max_length=32, null=True, blank=True)
    trial_period = models.IntegerField(default=30)
    grace_period = models.IntegerField(default=7)
    linked_accounts_max_count = models.IntegerField(
        default=0, help_text="Maximum count of user accounts that can be linked"
    )

    def __str__(self):
        return self.name


class EmailTemplate(TimeStampedModel):
    application = models.ForeignKey(Application, on_delete=models.CASCADE, related_name="email_templates")
    tag = models.CharField(max_length=64)
    subject_template = models.CharField(max_length=200)
    text_template = models.TextField()
    html_template = models.TextField(null=True, blank=True)
    amp_template = models.TextField(null=True, blank=True)
    from_email = models.EmailField(null=True, blank=True)
    from_name = models.CharField(max_length=128, null=True, blank=True)
    default_headers = models.JSONField(null=True, blank=True)

    class Meta:
        unique_together = (("application", "tag"),)
        verbose_name_plural = "Email Templates"

    def __str__(self) -> str:
        return f"{self.application.tag} - {self.tag}"

    def prepare_email_object(self, user, context, to, cc, bcc, subject, **kwargs) -> EmailMultiAlternatives:
        """
        Prepares and returns email object to be sent using Email template
        """

        if not isinstance(to, (list, tuple)):
            to = [to]
        if cc and not isinstance(cc, (list, tuple)):
            cc = [cc]
        if bcc and not isinstance(bcc, (list, tuple)):
            bcc = [bcc]
        context_obj = Context(context, autoescape=False)
        subject = subject or Template(self.subject_template).render(context_obj)
        # Multi-line headers are forbidden to prevent header injection, hence we're cleaning up subject and other
        # headers that we are adding to the email
        subject = subject.replace("\n", "").replace("\r", "")
        headers = kwargs.get("headers", {})
        if self.default_headers:
            headers.update(self.default_headers)  # noqa
        for key, value in headers.items():
            if isinstance(value, str) and ("\r" in value or "\n" in value):
                logger.exception(
                    "Email header value has newline or carriage return", extra={"key": key, "value": value}
                )
                value = value.replace("\n", "").replace("\r", "")
                headers[key] = value
        text_body = Template(self.text_template).render(context_obj)
        email = EmailMultiAlternatives(
            subject=subject, body=text_body, to=to, cc=cc or (), bcc=bcc or (), headers=headers
        )
        from_name = kwargs.get("from_name") or self.from_name or settings.SES_FROM_NAME
        from_email = kwargs.get("from_email") or self.from_email or settings.SES_FROM_EMAIL
        if from_name:
            email.from_email = f"{from_name} <{from_email}>"
        else:
            email.from_email = from_email
        if self.amp_template:
            amp_body = Template(self.amp_template).render(context_obj)
            try:
                minified_amp_body = minify_html_string(amp_body)
                email.attach_alternative(minified_amp_body, "text/x-amp-html")
            except Exception:
                logger.exception(
                    "Error while minifying HTML.",
                    extra={"email": user.email, "template": self.tag, "content-type": "amp-html"},
                )
                email.attach_alternative(amp_body, "text/x-amp-html")
        if self.html_template:
            html_body = Template(self.html_template).render(context_obj)
            try:
                minified_html_body = minify_html_string(html_body)
                email.attach_alternative(minified_html_body, "text/html")
            except Exception:
                logger.exception(
                    "Error while minifying HTML.",
                    extra={"email": user.email, "template": self.tag, "content-type": "html"},
                )
                email.attach_alternative(html_body, "text/html")
        if attachments := kwargs.get("attachments"):
            for attachment in attachments:
                if isinstance(attachment, File):
                    email.attach(attachment.name, attachment.read(), getattr(attachment, "content_type", ""))
                elif isinstance(attachment, tuple) and len(attachment) == 3:
                    filename, content, mimetype = attachment
                    email.attach(filename, content, mimetype)
                elif isinstance(attachment, dict):
                    filename = attachment.get("filename")
                    content = attachment.get("content")
                    mimetype = attachment.get("mimetype")
                    if filename and content:
                        if isinstance(content, str):
                            content = content.encode()
                        email.attach(filename, content, mimetype)
                else:
                    logger.warning(f"Invalid attachment format: {attachment}")
        return email

    def post_email_send(self, user, email, backend):
        """
        Performs actions post sending email

        Args:
            user: User instance
            email: Email object that was sent
            backend: Email backend that was used to send email

        Raises:
            FailedToSendEmailException: If email was not sent successfully, an exception is raised.
        """
        if email.extra_headers.get("status") != 200:
            raise FailedToSendEmailException(email=user.email, extra_headers=email.extra_headers, backend=backend.name)
        else:
            message_id = email.extra_headers["message_id"]
            # We are creating in memory email template for sending emails generated by llm , hence we need to handle
            # cases where we don't have an email template present
            template = self.id or None
            sent_email_data = {
                "user": user,
                "status": SentEmails.STATUS_SENT,
                "template_id": template,
                "message_id": message_id,
                "sent_using": backend.name,
            }
            SentEmails.objects.create(**sent_email_data)

    def send_email(self, user, context, to, cc=None, bcc=None, subject=None, backend=EmailBackend.SES, **kwargs):
        """
        Send email using the HTML template

        Args:
            user: User instance
            context: Context object used to render subject and body
            to: Recipients to be placed in "to" list
            cc: Recipients to be placed in "cc" list. Defaults to None.
            bcc: Recipients to be placed in "bcc" list. Defaults to None.
            subject: Subject of the email. Defaults to None.
            backend: Email backend to be used for sending email. Defaults to EmailBackend.SES.

        Returns:
            str: Message id of the sent message
        """
        if settings.APP_ENV == APP_ENV_TEST:
            backend = EmailBackend.TEST
        email = self.prepare_email_object(user, context, to, cc, bcc, subject, **kwargs)
        email.connection = get_connection(backend=backend.path, fail_silently=False, email=user.email, **kwargs)
        email.send()
        self.post_email_send(user, email, backend)
        return email.extra_headers["message_id"]

    def send_reply(
        self, user, context, original_message_id, thread_id, backend, to, cc=None, bcc=None, subject=None, **kwargs
    ):
        """
        Send email using the email template

        Args:
            user: User instance
            context: Context object used to render subject and body
            original_message_id: ID of the original message to which the reply should be sent
            thread_id: ID of the thread
            backend: Email backend to be used for sending email
            to: Recipients to be placed in "to" list
            cc: Recipients to be placed in "cc" list. Defaults to None.
            bcc: Recipients to be placed in "bcc" list. Defaults to None.
            subject: Subject of the email. Defaults to None.

        Raises:
            NotImplemented: If SES is chosen as email backend, as exception is raised.

        Returns:
            str: Message id of the sent message
        """
        if backend == EmailBackend.SES:
            raise NotImplemented("Unsupported backend to send reply.")
        email = self.prepare_email_object(user, context, to, cc, bcc, subject, **kwargs)
        kwargs = kwargs | kwargs.get("auth_tokens", {})
        connection = get_connection(backend=backend.path, fail_silently=False, email=user.email, **kwargs)
        connection.send_reply(email, original_message_id, thread_id)
        self.post_email_send(user, email, backend)
        return email.extra_headers["message_id"]

    def create_draft(
        self, user, context, backend, original_message_id, thread_id, to, cc=None, bcc=None, subject=None, **kwargs
    ):
        """
        Creates draft using the email template

        Args:
            user: User instance
            context: Context object used to render subject and body
            backend: Email backend to be used for creating draft
            to: Recipients to be placed in "to" list
            cc: Recipients to be placed in "cc" list. Defaults to None.
            bcc: Recipients to be placed in "bcc" list. Defaults to None.
            subject: Subject of the email. Defaults to None.
            original_message_id: ID of the original message to which the reply should be sent
            thread_id: ID of the thread

        Raises:
            NotImplemented: If SES is chosen as email backend, as exception is raised.
            FailedToCreateDraftException: If draft creation fails, an exception is raised.

        Returns:
            str: Message id of the sent message
        """
        if backend == EmailBackend.SES:
            raise NotImplemented("Unsupported backend to create draft.")
        email = self.prepare_email_object(user, context, to, cc, bcc, subject, **kwargs)
        kwargs = kwargs | kwargs.get("auth_tokens", {})
        connection = get_connection(backend=backend.path, fail_silently=False, email=user.email, **kwargs)
        connection.create_draft(email, original_message_id, thread_id)
        if email.extra_headers.get("status") != 200:
            raise FailedToCreateDraftException(
                email=user.email, extra_headers=email.extra_headers, backend=backend.name
            )
        return email.extra_headers["message_id"]

    @staticmethod
    def send_email_using_template(user: User, context: dict, tag: str, application: Application, to: str, **kwargs):
        """Sends email to the recipients using a template

        Args:
            user (User): User instance
            context (dict): Arguments to be used in rendering template
            tag (str): Tag of the email template
            application (Application): Application the template belongs to
        """
        try:
            template = EmailTemplate.objects.get(tag=tag, application=application)
            message_id = template.send_email(user=user, context=context, to=to, **kwargs)
        except EmailTemplate.DoesNotExist:
            logger.exception(
                "Error while sending email due to missing email template",
                extra={
                    "template": tag,
                    "application": application.name,
                    "user": user.email,
                },
            )
        else:
            logger.info(f"Email sent to user: {user.email} with template {tag} application {application.name}")
            return message_id


class SentEmails(TimeStampedModel):
    STATUS_SENT = "sent"
    STATUS_BOUNCED = "bounced"
    STATUS_DELIVERED = "delivered"
    STATUS_COMPLAINED = "complained"
    STATUS_CHOICES = (
        (STATUS_SENT, "Sent"),
        (STATUS_BOUNCED, "Bounced"),
        (STATUS_DELIVERED, "Delivered"),
        (STATUS_COMPLAINED, "Complained"),
    )

    BACKEND_CHOICES = EmailBackend.as_tuples()

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    message_id = models.CharField(max_length=64, db_index=True)
    status = models.CharField(max_length=64, null=True, blank=True, choices=STATUS_CHOICES)
    template = models.ForeignKey(EmailTemplate, on_delete=models.SET_NULL, null=True)
    metadata = models.JSONField(default=dict)
    sent_using = models.CharField(max_length=8, choices=BACKEND_CHOICES)

    class Meta:
        verbose_name_plural = "Sent Emails"


class ScheduledTaskQuerySet(models.QuerySet):
    def delete(self):
        periodic_task_ids = self.values_list("periodic_task_id", flat=True)
        PeriodicTask.objects.filter(id__in=periodic_task_ids).delete()
        return super().delete()


class ScheduledTask(TimeStampedModel):
    PERIODIC_TASK_NAME_FORMAT = f"{{user_id}}/{{task}}/{{random_uuid}}"
    objects = ScheduledTaskQuerySet.as_manager()

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    periodic_task = models.OneToOneField(PeriodicTask, on_delete=models.CASCADE)
    metadata = models.JSONField(default=dict)

    @classmethod
    def create_periodic_task(
        cls,
        user: User,
        task: str,
        start_time: datetime,
        interval_type: PeriodicTaskIntervals,
        interval_num: int,
        task_args: Optional[Tuple[Any]] = None,
        task_kwargs: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict] = None,
    ):
        """Creates period task, one that will be run at regular intervals

        Args:
            user (User): User associated with this task
            task (str): Task to be invoked
            start_time (datetime): Datetime when the schedule should begin triggering the task to run
            interval_type (str): The type of period between task runs (Example: days)
            interval_num (int): Number of interval periods to wait before running the task again
            metadata (dict): Metadata associated with this task
            args (Any) : arguments for the task to be executed

        Returns:
            ScheduledTask: An instance of ScheduledTask
        """
        if not metadata:
            metadata = {}
        if not task_args:
            task_args = ()
        if not task_kwargs:
            task_kwargs = {}
        try:
            task_args = json.dumps(task_args)
        except TypeError:
            logger.exception("Unable to convert arguments for the periodic task to a JSON string.")

        try:
            task_kwargs = json.dumps(task_kwargs)
        except TypeError:
            logger.exception("Unable to convert keyword arguments for the periodic task to a JSON string.")

        # celery's IntervalScheduler doesn't directly support "weeks" as interval type
        if interval_type == PeriodicTaskIntervals.WEEKS.value:
            interval_type = PeriodicTaskIntervals.DAYS.value
            interval_num *= 7

        task_name = cls.PERIODIC_TASK_NAME_FORMAT.format(user_id=user.id, task=task, random_uuid=uuid.uuid4())
        interval, _ = IntervalSchedule.objects.get_or_create(period=interval_type, every=interval_num)
        periodic_task = PeriodicTask.objects.create(
            name=task_name, task=task, interval=interval, start_time=start_time, args=task_args, kwargs=task_kwargs
        )
        return cls.objects.create(user=user, metadata=metadata, periodic_task=periodic_task)

    @classmethod
    def create_one_off_task(
        cls,
        user: User,
        task: str,
        start_time: datetime,
        task_args: Optional[Tuple[Any]] = None,
        metadata: Optional[Dict] = None,
        task_kwargs: Optional[Dict[str, Any]] = None,
    ):
        """Creates one-off task, one that will be run only once at the given datetime

        Args:
            user (User): User to be associated with the task
            task (str): Task to be invoked
            start_time (datetime): Datetime when the schedule should begin triggering the task to run
            metadata (dict): Metadata associated with this task
            task_args (Optional[Tuple[Any]]): arguments for the task to be executed
            task_kwargs (Optional[Dict[str, Any]]): keyword arguments for the task to be executed

        Returns:
            ScheduledTask: An instance of ScheduledTask
        """
        if metadata is None:
            metadata = {}
        if task_args is None:
            task_args = ()
        if task_kwargs is None:
            task_kwargs = {}
        try:
            task_args = json.dumps(task_args)
        except TypeError:
            logger.exception("Unable to convert arguments for the periodic task to a JSON string.")
        try:
            task_kwargs = json.dumps(task_kwargs)
        except TypeError:
            logger.exception("Unable to convert keyword arguments for the periodic task to a JSON string.")

        task_name = cls.PERIODIC_TASK_NAME_FORMAT.format(user_id=user.id, task=task, random_uuid=uuid.uuid4())
        clocked = ClockedSchedule.objects.create(clocked_time=start_time)
        periodic_task = PeriodicTask.objects.create(
            name=task_name,
            task=task,
            clocked=clocked,
            one_off=True,
            args=task_args,
            kwargs=task_kwargs,
        )
        return cls.objects.create(user=user, metadata=metadata, periodic_task=periodic_task)

    @classmethod
    def cleanup_one_off_tasks(cls):
        """Deletes all one-off tasks that have been processed"""
        return cls.objects.filter(periodic_task__one_off=True, periodic_task__total_run_count__gt=0).delete()

    def reschedule_one_off_task(self, start_time: datetime):
        """Reschedules one-off task to a provided datetime

        Args:
            start_time (datetime): Updated datetime when the schedule should begin triggering the task to run
        """
        old_clocked_id = self.periodic_task.clocked.id
        clocked = ClockedSchedule.objects.create(clocked_time=start_time)
        self.periodic_task.clocked = clocked
        self.periodic_task.save()
        ClockedSchedule.objects.filter(id=old_clocked_id).delete()

    def disable(self):
        """
        Disable periodic task associated with this scheduled task'
        """
        self.periodic_task.enabled = False
        self.periodic_task.save()

    def enable(self):
        """
        Enable periodic task associated with this scheduled task]
        """
        self.periodic_task.enabled = True
        self.periodic_task.save()

    def delete(self, *args, **kwargs):
        """Deletes a scheduled task, by first deleting the related periodic task"""
        if self.periodic_task is not None:
            self.periodic_task.delete()
        return super().delete(*args, **kwargs)


class ExceptionList(TimeStampedModel):
    TYPE_DOMAIN = "domain"
    TYPE_EMAIL = "email"
    TYPE_DOMAIN_EXTENSION = "domain_extension"
    TYPE_CHOICES = ((TYPE_DOMAIN, "Domain"), (TYPE_EMAIL, "Email"), (TYPE_DOMAIN_EXTENSION, "Domain Extension"))
    type = models.CharField(max_length=16, choices=TYPE_CHOICES)
    label_name = models.CharField(max_length=128)
    exception_address = models.CharField(max_length=254, unique=True)

    def __str__(self):
        return f"{self.exception_address}({self.type}) - {self.label_name}"


class FeatureFlagWebhookEvent(models.Model):
    EVENT_STATUS_PROCESSING = "processing"
    EVENT_STATUS_SUCCEEDED = "succeeded"
    EVENT_STATUS_FAILED = "failed"
    EVENT_STATUS_SCHEDULED = "scheduled"
    EVENT_STATUS_CHOICES = (
        (EVENT_STATUS_SCHEDULED, "Scheduled"),
        (EVENT_STATUS_PROCESSING, "Processing"),
        (EVENT_STATUS_SUCCEEDED, "Succeeded"),
        (EVENT_STATUS_FAILED, "Failed"),
    )
    id = models.CharField(primary_key=True, max_length=128)
    status = models.CharField(max_length=16, choices=EVENT_STATUS_CHOICES, default=EVENT_STATUS_SCHEDULED)
    date = models.DateTimeField()
    previous_version = models.JSONField(default=dict)
    current_version = models.JSONField(default=dict)

    @classmethod
    def save_status(cls, event_id, status):
        with transaction.atomic():
            event = FeatureFlagWebhookEvent.objects.select_for_update().get(id=event_id)
            event.status = status
            event.save(update_fields=["status"])
