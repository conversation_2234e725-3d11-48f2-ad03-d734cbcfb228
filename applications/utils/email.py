import asyncio
import base64
import hashlib
import html
import logging
import re
from enum import Enum
from typing import Any, Dict, List
from urllib.parse import unquote

import email_normalize
import tldextract
from bs4 import BeautifulSoup
from django.conf import settings
from django.core.cache import cache
from email_normalize import Normalizer, Result, providers

logger = logging.getLogger(__name__)


async def patched_normalize(self, email_address: str) -> Result:
    local_part, domain_part = email_address.lower().rsplit("@", maxsplit=1)
    mx_records = await self.mx_records(domain_part)
    provider = self._lookup_provider(mx_records)
    # https://datatracker.ietf.org/doc/html/rfc2822#section-3.2.2
    # If local part has quoted characters, treat email address as normalized address
    if local_part.startswith('"') and local_part.endswith('"'):
        return Result(email_address, email_address, mx_records, provider.__name__ if provider else None)
    if provider:
        if provider.Flags & providers.Rules.LOCAL_PART_AS_HOSTNAME:
            local_part, domain_part = self._local_part_as_hostname(local_part, domain_part)
        if provider.Flags & providers.Rules.STRIP_PERIODS:
            local_part = local_part.replace(".", "")
        if provider.Flags & providers.Rules.PLUS_ADDRESSING:
            local_part = local_part.split("+")[0]
        if provider.Flags & providers.Rules.DASH_ADDRESSING:
            local_part = local_part.split("-")[0]
    return Result(
        email_address, "@".join([local_part, domain_part]), mx_records, provider.__name__ if provider else None
    )


Normalizer.normalize = patched_normalize


def url_b64_decode(string: str) -> str:
    """Decodes a URL-safe Base64 string to its original representation.

    Args:
        string: The URL-safe Base64 string to decode.

    Returns:
        The decoded string.
    """
    if string:
        # Replace URL-safe characters with their Base64 equivalents
        string = string.replace("-", "+").replace("_", "/")
        # Decode the Base64 string
        decoded = base64.b64decode(string)
        # Unescape characters and decode percent-encoded octets
        unescaped = decoded.decode("utf-8", "replace")
        result = unquote(unescaped)
    else:
        result = ""
    return result


def index_headers(headers: List[Dict[str, Any]]) -> Dict[str, str]:
    """Takes the header array filled with objects and transforms it into a more pleasant key-value object.

    Args:
        headers: The array of header objects to transform.

    Returns:
        A dictionary of header keys and their corresponding values.
    """
    if not headers:
        return {}
    else:
        return {header["name"]: header["value"] for header in headers}


def html_to_text(html_content: str) -> str:
    """Function to convert html format to text format.

    Args:
        html_content: A string in HTML format.

    Return:
        The extracted plain text string.
    """
    if not html_content:
        return ""
    try:
        soup = BeautifulSoup(html.unescape(html_content), "html.parser")
        # Remove following tags destructively before getting text
        remove_tags = ["script", "style", "head", "title", "meta", "[document]"]
        for script in soup(remove_tags):
            script.extract()
        return soup.get_text(strip=True, separator=" ")
    except Exception:
        logger.exception("Parsing html content and converting it to text failed using bs4")
        # use non-greedy for remove scripts and styles
        text = re.sub("<script.*?</script>", "", html_content, flags=re.DOTALL)
        text = re.sub("<style.*?</style>", "", text, flags=re.DOTALL)
        # remove other tags
        text = re.sub("<[^>]+>", " ", text)
        # strip whitespace
        text = " ".join(text.split())
        # unescape html entities
        return html.unescape(text)


def get_email_domain(email: str) -> str:
    """
    Get domain from the email.

    Args:
        email (str): email address

    Returns:
        str: The domain of the email.
    """
    parts = email.split("@")
    if len(parts) == 2:
        extracted_result = tldextract.extract(email)
        return extracted_result.registered_domain
    # If the email contains more than one '@', it might have multiple domains
    # or be an invalid format, so we return an empty string to indicate that the domain extraction failed.
    return ""


def parse_all_emails_from_text(text) -> List[str]:
    """
    Parse the input text to find all email addresses
    Args:
        text: input text containing one or more email addresses

    Returns:
        list of email addresses found
    """
    pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
    emails = re.findall(pattern, text)
    return emails


def parse_all_domains_from_text(text):
    """
    Args:
        text: input text containing one or more domains

    Returns:
        list of domains found
    """
    pattern = re.compile(r"\b(?:[A-Za-z0-9-]+\.)+[A-Za-z]{2,}\b")
    domains = re.findall(pattern, text)
    return domains


def generate_sha256_hash(string: str):
    """
    Generates SHA-256 hash using the hashlib module.
    """
    hash_instance = hashlib.sha256()
    string_bytes = string.encode("utf-8")
    hash_instance.update(string_bytes)
    hashed_string = hash_instance.hexdigest()
    return hashed_string


def get_normalized_email(email):
    """
    Normalize email address to a canonical form. This is used to avoid duplicate
    sender profiles for the same email address with different capitalization or
    punctuation.
    Args:
        email: Email address to be normalized.
    Returns:
        str: Normalized email address.
    """
    cache_key = f"normalized_email:{generate_sha256_hash(email)}"
    if result := cache.get(cache_key):
        return result
    # Since email_normalize.normalize provides blocking function using asyncio,
    # we need to make sure if the event loop is set before calling it.
    try:
        asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    result = email_normalize.normalize(email)
    cache.set(cache_key, result.normalized_address)
    return result.normalized_address


def parse_ses_mail_obj(mail_obj: Dict[str, Any]):
    """
    Parse contents of event data that Amazon SES publishes to Amazon SNS.

    Amazon SES publishes email sending event records to Amazon Simple Notification Service in JSON format.

    Each email sending event record (bounce, complaint, delivery, sent, reject, open, click)
    contains information about the original email in the mail object.

    Args:
        mail_obj: Object that contains information about the email that produced the event.

    References:
        https://docs.aws.amazon.com/ses/latest/dg/event-publishing-retrieving-sns-contents.html#event-publishing-retrieving-sns-contents-mail-object

    Returns:
        dict: Parsed mail object
    """
    response = {}  # noqa
    # A unique ID that Amazon SES assigned to the message.
    # Amazon SES returned this value to us when we sent the message and stored in `ses_message_id`.
    # We can find the message ID of the original email in the headers field of the mail object.
    response["ses_message_id"] = mail_obj["messageId"]

    # The AWS account ID of the account that was used to send the email.
    response["sending_account_id"] = mail_obj["sendingAccountId"]

    # The date and time, in ISO8601 format (YYYY-MM-DDThh:mm:ss.sZ), when the message was sent.
    response["sent_at"] = mail_obj["timestamp"]

    # The email address that the message was sent from (the envelope MAIL FROM address).
    # Could be in the format of `name <email>` or just `email`.
    response["source"] = mail_obj["source"]

    # A list of email addresses that were recipients of the original mail.
    response["destination"] = mail_obj["destination"]

    # Whether the headers are truncated in the notification, which occurs if the headers are larger than 10 KB.
    headers_truncated = mail_obj["headersTruncated"]
    if isinstance(headers_truncated, str):
        headers_truncated = headers_truncated == "true"
    assert isinstance(headers_truncated, bool)
    response["headers_truncated"] = headers_truncated

    # A list of the email's original headers. Each header in the list has a name field and a value field.
    response["headers"] = index_headers(mail_obj["headers"])
    return response


def parse_ses_delivery_obj(delivery_obj: Dict[str, Any]):
    """
    Parse contents of event data that Amazon SES publishes to Amazon SNS for `Delivery` event.
    Args:
        delivery_obj: An object that contains information about a Delivery event.

    References:
        https://docs.aws.amazon.com/ses/latest/dg/event-publishing-retrieving-sns-contents.html#event-publishing-retrieving-sns-contents-delivery-object

    Returns:
        dict: Parsed delivery object
    """
    response = {}  # noqa
    # The date and time, in ISO8601 format (YYYY-MM-DDThh:mm:ss.sZ), when Amazon SES delivered the email to the recipient's mail server
    response["delivered_at"] = delivery_obj["timestamp"]

    # A list of intended recipients that the delivery event applies to.
    response["recipients"] = delivery_obj["recipients"]

    # The time in milliseconds between when Amazon SES accepted the request from the sender to when Amazon SES passed the message to the recipient's mail server.
    response["processing_time_millis"] = int(delivery_obj["processingTimeMillis"])

    # The SMTP response message of the remote ISP that accepted the email from Amazon SES.
    # This response will vary by email, by receiving mail server, and by receiving ISP.
    response["smtp_response"] = delivery_obj["smtpResponse"]

    # The host name of the Amazon SES mail server that sent the mail
    response["mail_transfer_agent"] = delivery_obj["reportingMTA"]
    return response


def parse_ses_complaint_obj(complaint_obj: Dict[str, Any]):
    """
    Parse contents of event data that Amazon SES publishes to Amazon SNS for `Complaint` event.
    Args:
        complaint_obj: An object that contains information about a Complaint event.

    References:
        https://docs.aws.amazon.com/ses/latest/dg/event-publishing-retrieving-sns-contents.html#event-publishing-retrieving-sns-contents-complaint-object

    Returns:
        dict: Parsed complaint object
    """
    response = {}  # noqa
    # The date and time, in ISO8601 format (YYYY-MM-DDThh:mm:ss.sZ), when the ISP sent the complaint notification.
    response["complained_at"] = complaint_obj["timestamp"]

    # A list that contains information about recipients that may have submitted the complaint.
    response["complained_recipients"] = [x["emailAddress"] for x in complaint_obj["complainedRecipients"]]

    # A unique ID for the complaint.
    response["feedback_id"] = complaint_obj["feedbackId"]

    # The subtype of the complaint, as determined by Amazon SES.
    # The value can either be null or OnAccountSuppressionList.
    # If the value is OnAccountSuppressionList, Amazon SES accepted the message, but didn't attempt to send it because it was on the account-level suppression list.
    response["complaint_sub_type"] = complaint_obj["complaintSubType"]

    # If a feedback report is attached to the complaint, the following fields may be present:
    if user_agent := complaint_obj.get("userAgent"):
        # This indicates the name and version of the system that generated the report
        response["user_agent"] = user_agent
    if complaint_feedback_type := complaint_obj.get("complaintFeedbackType"):
        # This contains the type of feedback report received from the ISP
        response["complaint_feedback_type"] = complaint_feedback_type
    if arrival_date := complaint_obj.get("arrivalDate"):
        # The value of the Arrival-Date or Received-Date field, in ISO8601 format (YYYY-MM-DDThh:mm:ss.sZ), from the feedback report
        response["arrival_date"] = arrival_date

    return response


def parse_ses_bounce_obj(bounce_obj: Dict[str, Any]):
    """
    Parse contents of event data that Amazon SES publishes to Amazon SNS for `Bounce` event.
    Args:
        bounce_obj: An object that contains information about a Bounce event.

    References:
        https://docs.aws.amazon.com/ses/latest/dg/event-publishing-retrieving-sns-contents.html#event-publishing-retrieving-sns-contents-bounce-object

    Returns:
        dict: Parsed bounced object
    """
    response = {}  # noqa
    # The date and time, in ISO8601 format (YYYY-MM-DDThh:mm:ss.sZ), when the ISP sent the bounce notification.
    response["bounced_at"] = bounce_obj["timestamp"]

    # A list that contains information about the recipients of the original mail that bounced.
    response["bounced_recipients"] = [x["emailAddress"] for x in bounce_obj["bouncedRecipients"]]

    # A unique ID for the bounce.
    response["feedback_id"] = bounce_obj["feedbackId"]

    # The type of bounce, as determined by Amazon SES.
    response["bounce_type"] = bounce_obj["bounceType"]

    # The subtype of the bounce, as determined by Amazon SES.
    response["bounce_sub_type"] = bounce_obj["bounceSubType"]

    # This is the value of the Message Transfer Authority (MTA) that attempted to perform the delivery, relay, or gateway operation described in the DSN.
    if reporting_mta := bounce_obj.get("reportingMTA"):
        response["reporting_mta"] = reporting_mta

    # If a DSN is attached to the bounce, the following fields may also be present.
    if action := bounce_obj.get("action"):
        # This indicates the action performed by the reporting MTA as a result of its attempt to deliver the message to this recipient.
        response["action"] = action
    if status := bounce_obj.get("status"):
        # This is the per-recipient transport-independent status code that indicates the delivery status of the message.
        response["status"] = status
    if diagnostic_code := bounce_obj.get("diagnosticCode"):
        # The status code issued by the reporting MTA in the SMTP DSN (Delivery Status Notification).
        response["diagnostic_code"] = diagnostic_code

    return response


def parse_ses_open_obj(open_obj: Dict[str, Any]):
    """
    Parse contents of event data that Amazon SES publishes to Amazon SNS for `Open` event.
    Args:
        open_obj: An object that contains information about an Open event.

    References:
        https://docs.aws.amazon.com/ses/latest/dg/event-publishing-retrieving-sns-contents.html#event-publishing-retrieving-sns-contents-open-object

    Returns:
        dict: Parsed open object
    """
    response = {}  # noqa
    # The date and time, in ISO8601 format (YYYY-MM-DDThh:mm:ss.sZ), when the ISP sent the open notification.
    response["opened_at"] = open_obj["timestamp"]

    # The IP address of the computer that the recipient used to open the email message.
    response["ip_address"] = open_obj["ipAddress"]

    # The user agent of the recipient's email client or device used to open the email message.
    response["user_agent"] = open_obj["userAgent"]

    return response


def parse_ses_click_obj(click_obj: Dict[str, Any]):
    """
    Parse contents of event data that Amazon SES publishes to Amazon SNS for `Click` event.
    Args:
        click_obj: An object that contains information about a Click event.

    References:
        https://docs.aws.amazon.com/ses/latest/dg/event-publishing-retrieving-sns-contents.html#event-publishing-retrieving-sns-contents-click-object

    Returns:
        dict: Parsed click object
    """
    response = {}  # noqa
    # The date and time, in ISO8601 format (YYYY-MM-DDThh:mm:ss.sZ), when the ISP sent the click notification.
    response["clicked_at"] = click_obj["timestamp"]

    # The IP address of the computer that the recipient used to open the email message.
    response["ip_address"] = click_obj["ipAddress"]

    # The user agent of the recipient's email client or device used to open the email message.
    response["user_agent"] = click_obj["userAgent"]

    # The link that was clicked, as contained in the message body.
    response["link"] = click_obj["link"]
    return response


class EmailBackend(Enum):
    """
    Enumeration representing email backends with associated names, and paths.

    Attributes:
        SES: An enumeration member representing the SES email backend.
        GMAIL: An enumeration member representing the Gmail email backend.
        OUTLOOK: An enumeration member representing the Outlook email backend.
    """

    SES = ("ses", "django_ses.SESBackend")
    GMAIL = ("gmail", "applications.mail.backends.gmail.GmailEmailBackend")
    OUTLOOK = ("outlook", "applications.mail.backends.outlook.OutlookEmailBackend")
    TEST = ("test", "applications.mail.backends.testing.TestingEmailBackend")

    def __init__(self, name, path) -> None:
        """
        Initializes a EmailBackend enum member with the given name and path.

        Args:
            name (str): The name of the backend.
            path (str): The path of the backend.

        Note:
            This constructor should not be called directly by users.
            Enum members are created automatically with the class definition.
        """
        self._name = name
        self._path = path

    @property
    def name(self):
        """
        Get the name of the backend.
        """
        return self._name

    @property
    def display_name(self):
        """
        Get the display name of the backend.
        """
        return self._name.capitalize()

    @property
    def path(self):
        """
        Get the path of the backend.
        """
        return self._path

    @classmethod
    def as_tuples(cls):
        """
        Get all backends as a tuple of tuples.

        Returns:
            tuple: A tuple of tuples representing all backends.
        """
        return tuple((member.name, member.display_name) for member in cls)

    @classmethod
    def from_service_provider(cls, service_provider: str):
        """
        Get the email backend for the given service provider.

        Args:
            service_provider (str): The service provider.

        Returns:
            EmailBackend: The email backend for the given service provider.
        """
        if service_provider == settings.SERVICE_PROVIDER_GOOGLE:
            return cls.GMAIL
        elif service_provider == settings.SERVICE_PROVIDER_MICROSOFT:
            return cls.OUTLOOK
        else:
            raise ValueError(f"Unsupported service provider: {service_provider}")
