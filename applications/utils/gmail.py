import logging
from typing import Any, Dict, Tuple

from django.conf import settings
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import HttpRequest

logger = logging.getLogger(__name__)


class GmailServiceHTTPError(Exception):
    """Error making HTTP request."""

    def __init__(self, status_code, reason):
        self.status_code = status_code
        self.reason = reason

    def __str__(self):
        return f"{self.status_code}: Reason {self.reason}"


class GmailService:
    MAX_NUM_TRIES = 6

    def __init__(self, email, access_token, refresh_token) -> None:
        self.email = email
        credentials = Credentials(
            token=access_token,
            refresh_token=refresh_token,
            client_id=settings.GOOGLE.get("client_id"),
            client_secret=settings.GOOGLE.get("client_secret"),
            token_uri=settings.GOOGLE.get("token_uri"),
        )
        self.service = build(serviceName="gmail", version="v1", credentials=credentials, cache_discovery=False)

    def http_execute_safe(self, request: HttpRequest) -> Tuple[bool, Dict[str, Any]]:
        """
        Execute HTTP request and return response if successful else return False and empty dict.

        Args:
            request (HttpRequest): HTTP request to be executed

        Returns:
            Tuple[bool, Dict[str, Any]]: Tuple of execution status and response
        """
        try:
            response = request.execute(num_retries=self.MAX_NUM_TRIES)
        except HttpError as http_error:
            # Network request connected with Gmail API, got error in response
            return False, {"reason": http_error.reason, "status_code": http_error.status_code, "uri": http_error.uri}
        except Exception as exc:
            # Network request fails
            logger.exception(
                "Error while making HTTP connection with Gmail API",
                extra={"user": self.email, "exception_details": exc},
            )
            return False, {}
        else:
            return True, response

    def send_message(self, message):
        """
        Sends an email message.
        Args:
            message: Message body to be sent
        Raises:
            MailBotHTTPError: If the message sending fails
        Returns:
            str: Message id assigned by Gmail
        """
        request: HttpRequest = self.service.users().messages().send(userId=self.email, body=message)
        executed, response = self.http_execute_safe(request)
        if executed:
            return response["id"]
        else:
            raise GmailServiceHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))

    def create_draft(self, message):
        """
        Creates a draft message.
        Args:
            message: Message body to be sent
        Raises:
            MailBotHTTPError: If the draft creation fails
        Returns:
            str: Message id assigned by Gmail
        """
        request: HttpRequest = self.service.users().drafts().create(userId=self.email, body=message)
        executed, response = self.http_execute_safe(request)
        if executed:
            return response["id"]
        else:
            raise GmailServiceHTTPError(reason=response.get("reason", ""), status_code=response.get("status_code", ""))
