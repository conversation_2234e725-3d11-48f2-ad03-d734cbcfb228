from typing import Dict

TRACKING_PARAMS_PREFIX = (
    "utm_",
    "hsa_",
    "gclid",
)


def get_tracking_params(query_params: Dict) -> Dict:
    """
    Get tracking params from a set of query params
    Args:
        query_params: query params

    Returns:
        tracking parameters

    """
    tracking_params = {}
    for key, value in query_params.items():
        if key.startswith(tuple(TRACKING_PARAMS_PREFIX)):
            if isinstance(value, list) and len(value) == 1:
                tracking_params[key] = value[0]
            else:
                tracking_params[key] = value
    return tracking_params


def prepare_tracking_parameters(old_parameters: Dict, new_parameters: Dict) -> Dict:
    """
    Prepare tracking parameters object to stored in the database based on the provided 'old_parameters' and 'new_parameters'.

    Notes:
        This utility function checks if a new key is being added and meets the criteria of
        starting with "utm_", "hsa_", or being equal to "gclid". If so, it sets the new key.

    Args:
        old_parameters (dict): The old parameter dictionary.
        new_parameters (dict): The new parameters dictionary.

    Returns:
        dict: The updated 'old_parameters' dictionary with added or updated keys.

    Example:
        old_parameters = {'utm_source': 'google'}
        new_parameters = {'utm_medium': 'cpc', 'hsa_campaign': 'spring_sale'}
        result = prepare_tracking_parameters(old_parameters, new_parameters)
        # Result: {'utm_source': 'google', 'utm_medium': 'cpc', 'hsa_campaign': 'spring_sale'}
    """
    for key, value in new_parameters.items():
        if key.startswith(TRACKING_PARAMS_PREFIX) and key not in old_parameters:
            old_parameters[key] = value
    return old_parameters
