import datetime

from O365 import Account, Message
from O365.utils.token import BaseT<PERSON>Backend
from django.conf import settings

MICROSOFT_MAILBOT_REQUIRED_SCOPES = ["Mail.Send"]


class DatabaseTokenBackend(BaseTokenBackend):
    """Mock Database based backend"""

    def __init__(self, token):
        super().__init__()
        self.token = token

    def load_token(self):
        return self.token_constructor(self.token)

    def save_token(self):
        pass


class OutlookService:
    def __init__(self, auth_tokens):
        self.token_backend = DatabaseTokenBackend(token=auth_tokens)
        client_id = settings.MICROSOFT["app_id"]
        client_secret = settings.MICROSOFT["app_secret"]
        self.account = Account(
            credentials=(client_id, client_secret),
            token_backend=self.token_backend,
            default_headers={"Prefer": "IdType='ImmutableId'"},
            scopes=MICROSOFT_MAILBOT_REQUIRED_SCOPES,
            timezone=datetime.timezone.utc,
            raise_http_errors=False,
        )

    def create_message(self) -> Message:
        """Creates message

        Returns:
            New empty message object
        """
        return self.account.mailbox().new_message()

    def create_reply(self, original_message_id) -> Message:
        """Creates a reply to an existing message

        Args:
            message_id: Message id of the original message

        Returns:
            Message object
        """
        return self.account.mailbox().get_message(original_message_id).reply()
