import logging
from typing import Tuple

import math
from urllib.parse import urlparse, urlunparse

import boto3
import requests
from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from minify_html import minify

from execfn.settings import APP_ENV_LOCAL

logger = logging.getLogger(__name__)


def get_filename(default_filename: str, response: requests.Response) -> str:
    """Get the filename from a downloaded PDF file response.

    Args:
        default_filename (str): The default filename to be used if extraction fails.
        response (requests.Response): The response object representing the downloaded PDF file.

    Returns:
        str: The extracted filename from the response headers or the default filename.
    """
    filename = f"{default_filename}.pdf"
    if "Content-Disposition" in response.headers:
        content_disposition = response.headers["Content-Disposition"]

        # Split the header by semicolon to get individual components
        components = content_disposition.split(";")

        # Iterate through the components to find the one that represents the filename
        for component in components:
            key_value = component.strip().split("=", 1)
            if len(key_value) == 2:
                key, value = key_value
                if key.strip().lower() == "filename":
                    filename = value.strip(' "')
    return filename


def download_file(url, default_file_name="file"):
    """
    Download a file from a given URL and create a SimpleUploadedFile instance.

    Args:
        url (str): The URL of the file to be downloaded.
        default_file_name (str, optional): The name of the field for the SimpleUploadedFile instance. Default is 'file'.

    Returns:
        SimpleUploadedFile: An instance of SimpleUploadedFile representing the downloaded file, or None if download fails.
    """
    try:
        response = requests.get(url)

        if response.status_code == 200:
            filename = get_filename(default_file_name, response)

            uploaded_file = SimpleUploadedFile(
                filename,
                response.content,
                content_type=response.headers["content-type"],
            )

            return uploaded_file
        else:
            logger.exception("Failed to download file.", extra={"url": url, "status_code": response.status_code})
            return None
    except Exception as e:
        logger.exception("Error downloading file.", extra={"url": url, "details": str(e)})
        return None


def add_subroute_to_url(url, subroute):
    """
    Adds a sub-route to the given URL.

    Args:
        url (str): The original URL.
        subroute (str): The sub-route to be added.

    Returns:
        str: The modified URL with the sub-route.

    Note:
        This function handles cases where the original path may or may not end with a "/"
        and where the sub-route may or may not start with a "/".
    """
    # Parse the URL
    parsed_url = urlparse(url)

    # Modify the path by adding the sub-route
    modified_path = parsed_url.path.rstrip("/") + "/" + subroute.lstrip("/")

    # Reconstruct the modified URL
    modified_url = urlunparse(
        (parsed_url.scheme, parsed_url.netloc, modified_path, parsed_url.params, parsed_url.query, parsed_url.fragment)
    )

    return modified_url


def batch_enumerate(sequence_length: int, batch_size: int) -> Tuple[int, slice]:
    """
    Enumerator over sequence yielding batch_num and range representing current batch.

    Args:
        sequence_length(int): length of elements
        batch_size(int): batch size for single batch

    Returns:
        [int, slice]: 0 based batch number, range for current batch
    """
    num_batches = math.ceil(sequence_length / batch_size)
    for i in range(num_batches):
        current_batch_range = slice(
            batch_size * i,
            min((i + 1) * batch_size, sequence_length),
        )
        yield i, current_batch_range


def minify_html_string(html_string):
    """
    Minifies an HTML string using the minify_html library.

    Args:
        html_string (str): The input HTML string to be minified.

    Returns:
        str: The minified HTML string

    Raises:
        Exception: If an error occurs during the minification process, an exception is raised.
                   The exception message provides details about the error.
    """
    minified_html = minify(html_string, minify_js=True, keep_closing_tags=True, keep_html_and_head_opening_tags=True)
    return minified_html
