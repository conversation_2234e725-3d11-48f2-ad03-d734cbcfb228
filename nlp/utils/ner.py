import logging

import tiktoken as tiktoken
from django.conf import settings
from langchain import PromptTemplate
from langchain_community.chat_models import ChatOpenAI

logger = logging.getLogger(__name__)


def get_chat_model():
    """
    Get chat model to be used for NER
    Returns:
        Instance of LLM Model
    """
    return ChatOpenAI(model_name=settings.OPENAI_MODEL_NAME, temperature=0)


def trim_conversation_for_llm_input(prompt: str, conversation: str) -> str:
    """
    The LLM we're using as of today cannot take more than 4097 tokens, which is why we're forced to trim the message
    Args:
        prompt : prompt for LLM
        conversation: user conversation for LLM

    Returns:
        trimmed message
    """
    max_token_length = 4000
    encoding = tiktoken.encoding_for_model(settings.OPENAI_MODEL_NAME)
    prompt_tokens = encoding.encode(prompt)
    conversation_tokens = encoding.encode(conversation)
    if len(conversation_tokens) + len(prompt_tokens) <= max_token_length:
        trimmed_conversation = conversation
    else:
        available_tokens = max_token_length - len(prompt_tokens)
        conversation_tokens = conversation_tokens[len(conversation_tokens) - available_tokens :]
        trimmed_conversation_tokens = encoding.decode(conversation_tokens)
        trimmed_conversation = "".join(trimmed_conversation_tokens)
    return trimmed_conversation


def binary_classification(conversation: str, prompt: str) -> bool:
    """
    Perform Binary classỉfication based on the provided conversation and prompt.
    Args:
        conversation (str): The conversation to be analyzed.
        prompt (str): The prompt to be passed to the Language Model.

    Returns:
        bool: True if the binary classification is successful, False otherwise.
    """
    llm = get_chat_model()
    trimmed_conversation = trim_conversation_for_llm_input(prompt, conversation)

    binary_classification_message = PromptTemplate.from_template(prompt).format(input=trimmed_conversation)
    prediction = llm.predict(binary_classification_message)
    prediction = prediction.strip(". \t\n\r").lower()
    return prediction == "yes"
