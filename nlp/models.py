from django.db import models
from django_jsonform.models.fields import ArrayField


class Entity(models.Model):
    name = models.CharField(max_length=125)
    key = models.CharField(max_length=125, unique=True)
    description = models.TextField()
    examples = ArrayField(models.TextField(), null=True, blank=True)
    data_type = models.CharField(max_length=55, null=True, blank=True)
    default = models.CharField(max_length=55, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Entities"

    def __str__(self):
        return self.name


class Intent(models.Model):
    CALENDARZAP = "calendarzap"
    REMINDERZAP = "reminderzap"
    INTRODUCTIONSZAP = "introductionszap"

    USE_CASE_CHOICES = (
        (CALENDARZAP, "Calender Zap"),
        (REMINDERZAP, "Reminder Zap"),
        (INTRODUCTIONSZAP, "Introductions Zap"),
    )

    use_case = models.Char<PERSON>ield(max_length=55, choices=USE_CASE_CHOICES)
    name = models.CharField(max_length=125)
    key = models.CharField(max_length=125, unique=True)
    description = models.TextField()
    examples = ArrayField(models.TextField(), null=True, blank=True)
    entities = models.ManyToManyField(Entity)

    def __str__(self):
        return f"{self.use_case} - {self.name}"


class LuceneFilter(models.Model):
    OTP = "OTP"
    BANK_STATEMENT = "BankStatement"
    PAYMENT_ALERT = "PaymentAlert"
    ORDER_TICKET = "OrderTicket"
    SECURITY = "Security"
    PASSWORD = "Password"

    TAG_CHOICES = (
        (OTP, "Otp/Verification"),
        (BANK_STATEMENT, "BankStatement"),
        (PAYMENT_ALERT, "Payment"),
        (ORDER_TICKET, "Order Ticket"),
        (SECURITY, "Security"),
        (PASSWORD, "Password"),
    )
    tag = models.CharField(max_length=125, unique=True, choices=TAG_CHOICES)
    prompt_template = models.TextField(null=True, blank=True)
    phrases = ArrayField(models.TextField(), null=True, blank=True)

    class Meta:
        verbose_name_plural = "Lucene Filters"

    def __str__(self):
        return self.tag
