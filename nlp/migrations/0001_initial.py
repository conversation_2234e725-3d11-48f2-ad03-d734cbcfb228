# Generated by Django 4.2.5 on 2023-11-03 08:33

from django.db import migrations, models
import django_jsonform.models.fields


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Entity",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("name", models.CharField(max_length=125)),
                ("key", models.CharField(max_length=125, unique=True)),
                ("description", models.TextField()),
                (
                    "examples",
                    django_jsonform.models.fields.ArrayField(
                        base_field=models.TextField(), blank=True, null=True, size=None
                    ),
                ),
                ("data_type", models.CharField(blank=True, max_length=55, null=True)),
                ("default", models.CharField(blank=True, max_length=55, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="LuceneFilter",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "tag",
                    models.CharField(
                        choices=[
                            ("OTP", "Otp/Verification"),
                            ("BankStatement", "BankStatement"),
                            ("PaymentAlert", "Payment"),
                            ("OrderTicket", "Order Ticket"),
                            ("Security", "Security"),
                            ("Password", "Password"),
                        ],
                        max_length=125,
                        unique=True,
                    ),
                ),
                ("prompt_template", models.TextField(blank=True, null=True)),
                (
                    "subject_phrases",
                    django_jsonform.models.fields.ArrayField(
                        base_field=models.TextField(), blank=True, null=True, size=None
                    ),
                ),
                (
                    "body_phrases",
                    django_jsonform.models.fields.ArrayField(
                        base_field=models.TextField(), blank=True, null=True, size=None
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Intent",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "use_case",
                    models.CharField(
                        choices=[
                            ("calendarzap", "Calender Zap"),
                            ("reminderzap", "Reminder Zap"),
                            ("introductionszap", "Introductions Zap"),
                        ],
                        max_length=55,
                    ),
                ),
                ("name", models.CharField(max_length=125)),
                ("key", models.CharField(max_length=125, unique=True)),
                ("description", models.TextField()),
                (
                    "examples",
                    django_jsonform.models.fields.ArrayField(
                        base_field=models.TextField(), blank=True, null=True, size=None
                    ),
                ),
                ("entities", models.ManyToManyField(to="nlp.entity")),
            ],
        ),
    ]
