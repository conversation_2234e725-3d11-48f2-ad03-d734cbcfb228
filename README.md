# Execfn Backend

- [Setting up the execfn backend](#setting-up-the-execfn-backend)
  - [Clone the GitHub repository](#clone-the-github-repository)
  - [Setup pyenv and pyenv-virtualenv](#setup-pyenv-and-pyenv-virtualenv)
  - [Install dependencies](#install-dependencies)
  - [Setup Database](#setup-database)
  - [Create a superuser account](#create-a-superuser-account)
  - [Run the server](#run-the-server)
- [Contribution Guide](#contribution-guide)
  - [Introduction](#introduction)
  - [Git Message Conventions](#git-message-conventions)
  - [The Seven Rules of a Great Git Commit Message](#the-seven-rules-of-a-great-git-commit-message)
  - [Example Git Commit Message](#example-git-commit-message)
  - [Pull Request Conventions](#pull-request-conventions)
  - [PR Contract](#pr-contract)
  - [Conclusion](#conclusion)
  - [Bibliography](#bibliography)

## Setting up the execfn backend

### Clone the GitHub repository

Make sure git is [setup](https://www.theodinproject.com/lessons/foundations-setting-up-git) properly on your system.

```bash
git clone <repository_url>
cd your-repository
```

### Setup pyenv and pyenv-virtualenv

- Install [pyenv](https://github.com/pyenv/pyenv#installation)
- Install [pyenv-virtualenv](https://github.com/pyenv/pyenv-virtualenv)

Install python version 3.9.16 using:

```bash
pyenv install 3.9.16
```

Create a virtual environment:

```bash
pyenv virtualenv 3.9.16 <virtual_env_name>
```

Automatically activate and deactivate the virtual environment:

- Create a .python-version file in the backend directory.

- Add <your_virtual_env_name>

### Install dependencies

Navigate to the backend directory and run the following commands:

```bash
pip install poetry
poetry install
```

### Setup Database

Install PostgreSQL:

<https://www.postgresql.org/download/>

Create a _.env_ file in the _backend_ directory:

```bash
touch .env
```

```python
# Add to the .env file
DJANGO_DB_PASSWORD='super_secret_password'
```

Create a database and user:

```bash
psql postgres
```

```sql
createdb execfn;
CREATE USER execfn_user WITH ENCRYPTED PASSWORD 'super_secret_password';
ALTER ROLE execfn_user WITH LOGIN;
GRANT ALL ON DATABASE execfn TO USER execfn_user;
GRANT ALL ON SCHEMA PUBLIC TO execfn_user;
```

Apply changes to the database schema:

```bash
# In the backend directory:
python manage.py migrate
```

### Create a superuser account

In the _backend_ directory:

```bash
python manage.py createsuperuser
```

### Run the server

In the _backend_ directory:

```bash
python manage.py runserver
```

## Contribution Guide

### Introduction

Consistent and well-structured Git commit messages and pull requests (PRs) are essential for maintaining an organized and efficient development process. This document outlines the conventions and guidelines that the Executive Function engineering team should follow when making commits and creating PRs. Adhering to these conventions will improve code readability, collaboration, and traceability.

### Git Message Conventions

A well-structured commit message should convey the purpose of the commit clearly and succinctly. To achieve this, we follow the following conventions:

### The Seven Rules of a Great Git Commit Message

1. **Separate subject from body with a blank line:** Start your commit message with a concise subject line, followed by a blank line, and then provide a detailed body (if necessary).
2. **Limit the subject line to 50 characters:** Keep the subject line short and to the point, summarizing the essence of the commit.
3. **Capitalize the subject line:** Always capitalize the first letter of the subject line.
4. **Do not end the subject line with a period:** Omit periods at the end of the subject line.
5. **Use the imperative mood in the subject line:** Start the subject line with a verb in the present tense that describes the action. **For example:**
   - _Fix some error for some scenario_
   - _Create API for a particular use case_
6. **Wrap the body at 72 characters:** Keep the body of the commit message within 72 characters per line for readability.
7. **Use the body to explain what and why vs. how:** Explain the problem the commit is solving, focusing on the 'what' and 'why.' Avoid delving into implementation details, which are better explained in the code itself.

### Example Git Commit Message

     Fix issue with user authentication

     The previous authentication method was not handling edge cases correctly,
     resulting in occasional login failures. This commit resolves the issue by
     implementing a more robust authentication mechanism.

     Resolves: EFN-123
     See also: EFN-456, EFN-789

### Pull Request Conventions

**When creating PRs, it's essential to maintain consistency and provide clear information to reviewers. Follow these conventions:**

- **PR Title:** Start the PR title with the associated issue or task number in the format EFN-XXXX, followed by a concise title that describes the purpose of the PR.
- **Commit Squashing:** Always squash and merge your commits when merging into the development branch to maintain a clean and linear commit history.
- **Description:** Keep the PR description short and crisp. It should provide context, explain the changes made, and include any relevant information for reviewers.
- **Delete Remote Branch:** After merging the PR, delete the remote branch to keep the repository clean and prevent branch clutter.

### PR Contract

**Before submitting a PR, make sure to complete the PR contract and follow the guidelines mentioned below:**

- The PR title must start with `<PROJECT>-<TICKET ID>`
- The PR titles should be in assertive tone like
  - _Fix bug in …_
  - _Write/Create API for …_
  - _Enhance/Add existing/new feature …_
  - _Example_ `PLAT-12141 Build API to...`
- Start a thread in `#code-reviews` channel on slack with the title
  - `EFN-<TICKET ID> PR TITLE <PR No.>`
  - _Attach links to ticket id and PR in the thread title itself_

```text
[EFN-dx4SWZMd](https://trello.com/c/dx4SWZMd/523-stripe-integration) Strip Integration [#209]
(https://github.com/execfnbot/frontend/pull/209)
```

- **ChangeList:**
  - The changelist should contain precisely what changes you’ve made overall.
  - It should rarely, if ever, contain why the change was made (That can be elaborated in the description section, if required)
  - **For eg:** _“Added a util`foobar` to return the workflows with kind tag `<tag>`” or “Refactored the`foobar` function to accept a new string arg”_

### Conclusion

Adhering to these Git commit and PR conventions will streamline our development process, enhance code quality, and make collaboration within the Executive Function engineering team more efficient. By following these guidelines consistently, we can maintain a clean and well-documented codebase.

### Bibliography

- [Chris Beams - How to Write a Git Commit Message](https://chris.beams.io/posts/git-commit/)
- [A Git Commit Message Style Guide](https://gist.github.com/robertpainsi/b632364184e70900af4ab688decf6f53)

**Please make sure to refer to these resources for additional guidance on writing effective commit messages.**
