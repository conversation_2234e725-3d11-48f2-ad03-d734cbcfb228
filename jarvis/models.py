import logging

from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import models
from django.utils import timezone
from django_extensions.db.models import TimeStampedModel
from django_jsonform.models.fields import Array<PERSON>ield
from google.auth.exceptions import RefreshError
from google.oauth2.credentials import Credentials
from googleapiclient._auth import refresh_credentials
from jsonschema import Draft7Validator
from jsonschema.exceptions import SchemaError

from applications.models import ScheduledTask
from execfn.common.utils.cryptography import decrypt_secret, encrypt_secret
from execfn.settings import APP_ENV_LOCAL

User = get_user_model()
logger = logging.getLogger(__name__)


class Assistant(models.Model):
    openai_id = models.CharField(max_length=255, unique=True)
    name = models.CharField(max_length=55, unique=True)
    openai_file_ids = ArrayField(models.TextField(), null=True, blank=True)
    instructions = models.TextField()
    user_prompt = models.TextField(null=True, blank=True)
    run_instructions = models.TextField(null=True, blank=True)

    def __str__(self):
        return self.name


class AssistantLogs(models.Model):
    assistant = models.ForeignKey(Assistant, on_delete=models.SET_NULL, null=True)
    user_query = models.TextField()
    output = models.TextField()


class Workflow(TimeStampedModel):
    STATE_INITIATED = "initiated"
    STATE_PROCESSING = "processing"
    STATE_CONFIGURED = "configured"
    STATE_FAILED = "failed"

    STATE_CHOICES = (
        (STATE_FAILED, "Failed"),
        (STATE_CONFIGURED, "Configured"),
        (STATE_PROCESSING, "Processing"),
        (STATE_INITIATED, "Initiated"),
    )

    TYPE_EVENT_DRIVEN = "event_driven"
    TYPE_INSTANTANEOUS = "instantaneous"
    TYPE_PERIODIC = "periodic"
    TYPE_SCHEDULED = "scheduled"

    WORKFLOW_TYPE_CHOICES = (
        (TYPE_EVENT_DRIVEN, "Event Driven"),
        (TYPE_INSTANTANEOUS, "Instantaneous"),
        (TYPE_PERIODIC, "Periodic"),
        (TYPE_SCHEDULED, "Scheduled"),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    user_query = models.TextField()
    assistant = models.ForeignKey(Assistant, on_delete=models.SET_NULL, null=True)
    json_workflow = models.JSONField(null=True)
    state = models.CharField(max_length=55, default=STATE_INITIATED, choices=STATE_CHOICES)
    # TODO : Rename this to workflow trigger type
    type = models.CharField(max_length=55, choices=WORKFLOW_TYPE_CHOICES, null=True)
    is_active = models.BooleanField(default=False)
    scheduled_task = models.ForeignKey(ScheduledTask, on_delete=models.SET_NULL, null=True, blank=True)
    metadata = models.JSONField(default={})

    def __str__(self):
        return f"{self.user_query} : {self.state} | Active {self.is_active}"


# TODO : Get rid of this model or refactor
class WorkflowEvent(models.Model):
    tag = models.CharField(max_length=125)
    workflow = models.OneToOneField(
        Workflow, on_delete=models.CASCADE, related_name="workflow_events", related_query_name="workflow_events"
    )

    class Meta:
        unique_together = ("tag", "workflow")


class ScheduledWorkflowTask(models.Model):
    workflow = models.ForeignKey(Workflow, on_delete=models.CASCADE)
    scheduled_task = models.ForeignKey(ScheduledTask, on_delete=models.CASCADE)

    class Meta:
        unique_together = ("workflow", "scheduled_task")


class WorkflowExecutionLog(TimeStampedModel):
    EXECUTION_SUCCESS = "success"
    EXECUTION_FAILURE = "failure"
    EXECUTION_STATUS_CHOICES = ((EXECUTION_SUCCESS, "Execution success"), (EXECUTION_FAILURE, "Execution failure"))
    workflow = models.ForeignKey(Workflow, on_delete=models.SET_NULL, null=True)
    metadata = models.JSONField(null=True)
    status = models.CharField(max_length=32, choices=EXECUTION_STATUS_CHOICES)

    def __str__(self):
        return f"{self.workflow.user_query} : {self.status} | {self.created}"


class WorkflowComponentDefinition(TimeStampedModel):
    TYPE_TRIGGER = "trigger"
    TYPE_CONSTRAINT = "constraint"
    TYPE_ACTION = "action"

    TYPE_CHOICES = (
        (TYPE_TRIGGER, "Trigger"),
        (TYPE_CONSTRAINT, "Constraint"),
        (TYPE_ACTION, "Action"),
    )

    name = models.CharField(max_length=128)
    tag = models.CharField(max_length=64, unique=True)
    description = models.TextField()
    input = models.JSONField(default=dict)
    ui_schema = models.JSONField(null=True, blank=True)
    type = models.CharField(max_length=16, choices=TYPE_CHOICES, db_index=True)

    class Meta:
        verbose_name = "Workflow Component Definition"
        verbose_name_plural = "Workflow Component Definitions"

    def __str__(self):
        return f"{self.name} | {self.tag} | {self.type}"

    def clean(self) -> None:
        """Validate input as per schema and raise Exceptions if any"""
        try:
            Draft7Validator.check_schema(self.input)
        except SchemaError as e:
            raise ValidationError(f"Invalid JSON schema: {e.message}")
        return super().clean()

    def save(self, **kwargs):
        """Invoke full_clean by overriding default save method
        This is make sure clean is invoked on model save
        """
        super().full_clean()
        return super().save(**kwargs)


# TODO : This is a temp model to store incremental JARVIS scopes. Once we figure out the UX of how it will function, we
#  will remodel whatever is required
class JarvisGoogleAuthCredentials(TimeStampedModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="jarvis_google_auth_credentials")
    encrypted_access_token = models.BinaryField(null=True, blank=True, editable=False)
    encrypted_refresh_token = models.BinaryField(null=True, blank=True, editable=False)
    expires_at = models.DateTimeField(null=True, blank=True)
    granted_scopes = models.JSONField(default=list)

    @property
    def access_token(self) -> str:
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        return decrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=self.encrypted_access_token,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @access_token.setter
    def access_token(self, value: str):
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        self.encrypted_access_token = encrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=value,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @property
    def refresh_token(self) -> str:
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        return decrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=self.encrypted_refresh_token,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @refresh_token.setter
    def refresh_token(self, value: str):
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        self.encrypted_refresh_token = encrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=value,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    def get_credentials(self):
        access_token, refresh_token = None, None
        if timezone.now() >= self.expires_at - timezone.timedelta(minutes=5):
            try:
                credential = Credentials(
                    token=self.access_token,
                    refresh_token=self.refresh_token,
                    client_id=settings.JARVIS_GOOGLE_AUTH.get("client_id"),
                    client_secret=settings.JARVIS_GOOGLE_AUTH.get("client_secret"),
                    token_uri=settings.JARVIS_GOOGLE_AUTH.get("token_uri"),
                )
                refresh_credentials(credential)
            except RefreshError as refresh_error:
                logger.info(f"Refreshing Jarvis auth creds for user {self.user.email} failed due to {refresh_error}")
            else:
                self.expires_at = credential.expiry
                self.access_token = credential.token
                self.refresh_token = credential.refresh_token
                self.save()
                access_token, refresh_token = self.access_token, self.refresh_token
        else:
            access_token, refresh_token = self.access_token, self.refresh_token
        return access_token, refresh_token


class OpenAICostAnalysis(TimeStampedModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="openai_cost_analysis")
    workflow = models.ForeignKey(Workflow, on_delete=models.SET_NULL, null=True)
    llm = models.CharField()
    total_tokens = models.PositiveIntegerField()
    prompt_tokens = models.PositiveIntegerField()
    completion_tokens = models.PositiveIntegerField()
    total_cost = models.FloatField()


class JarvisUserLimits(TimeStampedModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="jarvis_user_limits")
    workflow_limit = models.PositiveIntegerField()
    message_limit = models.PositiveIntegerField()
