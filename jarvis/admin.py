from django.contrib import admin

from jarvis.models import Assistant, Workflow, WorkflowExecutionLog, WorkflowComponentDefinition


@admin.register(Assistant)
class AssistantAdmin(admin.ModelAdmin):
    pass


@admin.register(Workflow)
class WorkflowAdmin(admin.ModelAdmin):
    pass


@admin.register(WorkflowExecutionLog)
class WorkflowExecutionLogAdmin(admin.ModelAdmin):
    pass


@admin.register(WorkflowComponentDefinition)
class WorkflowComponentDefinitionAdmin(admin.ModelAdmin):
    readonly_fields = ["input", "ui_schema"]
    pass
