from django import dispatch

"""
Jarvis workflows have three different triggers
1. Event Driven : Whenever an application receives an event, it can call for the execution of <PERSON> workflow using the 
    event's unique tag.
2. Instantaneous : Workflows that need to be executed as soon as they're configured and activated.
3. Periodic : Recurring or on-off workflows that are executed in a specific interval at a specific time.

For the latter two, since Jarvis does not have the payload for execution of workflows it fires a signal with the 
instance of the workflow to be executed. All receiving applications check if the instance belongs to their use case and 
then proceeds to queue Jarvis workflow for execution with the relevant payload.    
"""
execute_jarvis_workflow_signal = dispatch.Signal(["workflow_id"])
