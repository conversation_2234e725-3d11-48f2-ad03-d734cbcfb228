import logging

from django.contrib.auth import get_user_model
from django.db import transaction, DatabaseError

from execfn.celery import app
from jarvis.models import Workflow, WorkflowEvent, WorkflowComponentDefinition
from jarvis.signals import execute_jarvis_workflow_signal
from jarvis.utils.assistants import OpenAIAssistant
from jarvis.utils.parser import WorklowJsonParser
from jarvis.utils.typing import JarvisPayload
from jarvis.utils.workflow import WorkflowExecutor, get_jarvis_payload_from_cache, set_jarvis_payload_cache
from jarvis.utils.workflow import is_workflow_definition_valid

User = get_user_model()
logger = logging.getLogger(__name__)


@app.task
def configure_workflow(workflow_id):
    """
    Configures the workflow by creating a workflow definition based on user query
    Args:
        workflow_id : id of the workflow which needs to be configured
    """
    with transaction.atomic():
        try:
            workflow = (
                Workflow.objects.select_related("user", "assistant")
                .select_for_update(nowait=True, of=("self",))
                .get(id=workflow_id)
            )
            assert workflow.state == Workflow.STATE_INITIATED
        except (DatabaseError, Workflow.DoesNotExist, AssertionError):
            raise
            # return
        else:
            workflow.state = Workflow.STATE_PROCESSING
            workflow.save()

    logger.info("Initialising JARVIS assistant for workflow extraction.")
    assistant = OpenAIAssistant()
    workflow_state = Workflow.STATE_FAILED
    workflow_type = None
    if workflow_definition_json := assistant.get_workflow(workflow.assistant, workflow.user_query):
        is_valid, error = is_workflow_definition_valid(workflow_definition_json)
        if is_valid:
            workflow_state = Workflow.STATE_CONFIGURED
            workflow_type = workflow_definition_json["trigger"]["tag"]
            schema_definitions = {
                item["tag"]: item["input"] for item in WorkflowComponentDefinition.objects.values("input", "tag")
            }
            metadata = {"timezone": workflow.user.user_mailbot_profile.preferences.get("digest_timezone")}
            parser = WorklowJsonParser(workflow_definition_json, schema_definitions, metadata)
            updated_json_workflow = parser.parse()
            workflow.json_workflow = updated_json_workflow
    workflow.state = workflow_state
    workflow.type = workflow_type
    # TODO: Further optimize this to only fetch definitions that are tagged in workflow json
    workflow.save()


@app.task
def instantaneous_execution_of_jarvis_workflow(workflow_id: int):
    """
    Task for the instantaneous execution of Jarvis workflows

    Args:
        workflow_id : ID of the workflow to be executed
    """
    workflow = Workflow.objects.prefetch_related("workflow_events").get(id=workflow_id)
    if workflow.is_active:
        logger.info(f"Initiating execution of instantaneous Jarvis workflow. WorkflowId {workflow_id}")
        execute_jarvis_workflow_signal.send(sender=instantaneous_execution_of_jarvis_workflow, workflow=workflow)
    else:
        logger.info("Skipped execution of instantaneous Jarvis workflow as the workflow is inactive.")


@app.task
def periodic_execution_of_jarvis_workflow(workflow_id: int):
    """
    Task for the periodic execution of Jarvis workflows

    Args:
        workflow_id : ID of the workflow to be executed
    """
    workflow = Workflow.objects.prefetch_related("workflow_events").get(id=workflow_id)
    if workflow.is_active:
        logger.info(f"Initiating execution of periodic Jarvis workflow. WorkflowId {workflow_id}")
        execute_jarvis_workflow_signal.send(sender=periodic_execution_of_jarvis_workflow, workflow=workflow)
    else:
        logger.info("Skipped execution of periodic Jarvis workflow as the workflow is inactive.")


def queue_jarvis_workflow_for_execution(
    user: User, payload: JarvisPayload, _async: bool = True, workflow_id: int = None, event_tag: str = None
):
    """
    Fetch all workflows (if event tag is provided) or a single workflow (if workflow_id is provided) and execute for
    the input payload

    Args:
        workflow_id : workflow to be executed
        event_tag : event tag for which we need to trigger all workflows e.g. new_email
        user : user for which we need to trigger Jarvis workflows
        payload : payload for workflow execution
        _async : if jarvis workflows should be executed async

    Returns:
        boolean result if the constraints for this workflow were satisfied
    """
    assert any([event_tag, workflow_id]), (
        "You must provide either workflow_id or event_tag to identify Jarvis " "workflows to be executed"
    )
    if event_tag:
        workflow_ids = WorkflowEvent.objects.filter(
            tag=event_tag, workflow__user=user, workflow__is_active=True, workflow__type=Workflow.TYPE_EVENT_DRIVEN
        ).values_list("workflow_id", flat=True)
    else:
        workflow_ids = [workflow_id]
    jarvis_workflows_executed = False
    for workflow_id in workflow_ids:
        payload_cache_key = set_jarvis_payload_cache(payload)
        if _async:
            workflow_execution_result = execute_workflow.delay(workflow_id, payload_cache_key)
            jarvis_workflows_executed |= workflow_execution_result.get()
        else:
            jarvis_workflows_executed |= execute_workflow.run(workflow_id, payload_cache_key)
    return jarvis_workflows_executed


@app.task
def execute_workflow(workflow_id, payload_cache_key):
    """
    Fetch and execute single jarvis workflow with the input payload
    Args:
        workflow_id: workflow
        payload_cache_key: key to fetch payload

    Returns:
        boolean result if the constraints for this workflow were satisfied
    """
    payload: JarvisPayload = get_jarvis_payload_from_cache(payload_cache_key)
    workflow = Workflow.objects.select_related("user").get(id=workflow_id, is_active=True)
    logger.info(f"Executing JARVIS workflow for user. WorkflowId {workflow_id} | User {workflow.user_id}")
    executor = WorkflowExecutor(workflow=workflow, user=workflow.user, payload=payload)
    return executor.run()
