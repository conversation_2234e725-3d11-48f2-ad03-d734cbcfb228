from django.conf import settings
from openai import OpenAI
import tiktoken


def get_token_count(conversation: str, model_name: str):
    """
    Args:
        conversation: The input text for which we need to get the token count
        model_name: The model name ot get the encoding used by the model

    Returns:
        Count of tokens in the input conversation
    """

    encoding = tiktoken.encoding_for_model(model_name)
    token_count = len(encoding.encode(conversation))
    return token_count


def trim_conversation(conversation: str, model_name: str, max_tokens: int):
    """
    Trim the conversation to max tokens based on the model
    Args:
        conversation: The input text which we need to trim
        model_name: The model name ot get the encoding used by the model
        max_tokens: The max length till which we need to trim

    Returns:
        Trimmed conversation
    """

    encoding = tiktoken.encoding_for_model(model_name)
    conversation_tokens = encoding.encode(conversation)
    if len(conversation_tokens) <= max_tokens:
        trimmed_conversation = conversation
    else:
        conversation_tokens = conversation_tokens[:max_tokens]
        trimmed_conversation_tokens = encoding.decode(conversation_tokens)
        trimmed_conversation = "".join(trimmed_conversation_tokens)
    return trimmed_conversation


def complete_chat(chat_input: str):
    """
    Complete a chat interaction using the OpenAI API.

    Args:
        chat_input (str): The input for the chat conversation.

    Returns:
        str: The model's response to the input query.
    """
    client = OpenAI(api_key=settings.OPENAI_API_KEY)
    response = client.chat.completions.create(
        model=settings.OPENAI_MODEL_NAME,
        response_format={"type": "json_object"},
        messages=[
            {"role": "user", "content": chat_input},
        ],
    )
    return response.choices[0].message.content
