from abc import ABC, abstractmethod

from jarvis.utils.typing import JarvisPayload


class BaseAction(ABC):
    def __init__(self, *, user, params, user_query, workflow):
        """Initializes action object

        Args:
            user : User for which the action is to be executed
            params : Parameters to be used when executing action
            user_query : User query for the workflow
        """
        self.user = user
        self.params = params
        self.user_query = user_query
        self.workflow = workflow

    @abstractmethod
    def execute(self, *, payload: JarvisPayload):
        pass
