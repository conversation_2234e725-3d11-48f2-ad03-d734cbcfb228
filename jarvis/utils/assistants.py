import json
import logging
import re
import time
from json import JSO<PERSON>ecode<PERSON><PERSON><PERSON>
from typing import Tuple, Union

from constance import config as constance_config
from django.conf import settings
from django.utils import timezone
from openai import OpenAI

logger = logging.getLogger(__name__)


class OpenAIAssistant:
    """
    Class defining all the functions to interact with an OpenAI assistant
    """

    def __init__(self):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
        self.thread_id = None
        self.thread_run_id = None

    def create_thread(self):
        """
        Create a thread for assistant
        """
        thread = self.client.beta.threads.create()
        self.thread_id = thread.id

    def add_message_to_thread(self, content: str):
        """
        Add message to the thread
        """
        message = self.client.beta.threads.messages.create(
            thread_id=self.thread_id,
            role="user",
            content=content,
        )

    def run_thread(self, instructions: str, assistant_id: str):
        """
        Run the assistant
        """
        run = self.client.beta.threads.runs.create(
            thread_id=self.thread_id,
            assistant_id=assistant_id,
            instructions=instructions,
            tools=[{"type": "retrieval"}],
        )
        self.thread_run_id = run.id

    def create_and_run_thread_with_message(self, run_instruction: str, assistant_id: str, message: str):
        """
        Create a thread , Add message to the thread and run the thread
        Args:
            run_instruction: Assistant instruction to run the thread
            assistant_id: assistant id of the assistant to attach the thread to
            message: message to add to the thread

        Returns:

        """
        self.create_thread()
        self.add_message_to_thread(message)
        self.run_thread(run_instruction, assistant_id)

    def get_assistant_output(self) -> Tuple[bool, Union[str, None]]:
        """
        Check for run status if completed before timeout retrieve the output
        Returns:
        Tuple[bool, Union[str, None]]: A tuple containing a boolean (True if completed, False otherwise)
                                   and a string representing the output or an error message.
        """

        logger.info(f"Get assistant output for thread id {self.thread_id} and run id :{self.thread_run_id}")

        time_elapsed = 0
        timeout = constance_config.ASSISTANT_TIMEOUT_CUTOFF
        retry_time = constance_config.ASSISTANT_POLLING_RETRY_INTERVAL
        while time_elapsed < timeout:
            run = self.client.beta.threads.runs.retrieve(thread_id=self.thread_id, run_id=self.thread_run_id)
            if run.status == "completed":
                messages = self.client.beta.threads.messages.list(thread_id=self.thread_id)
                for each in messages:
                    if each.role == "assistant":
                        return True, each.content[0].text.value

            time.sleep(retry_time)
            time_elapsed = time_elapsed + retry_time

        return False, "Max wait time elapsed , could not retrieve the workflow"

    def get_workflow(self, assistant_details, user_query) -> Union[dict, None]:
        assistant_id = assistant_details.openai_id
        user_prompt = assistant_details.user_prompt
        assistant_instructions = assistant_details.instructions

        self.create_thread()
        content = user_query + "\n" + user_prompt
        self.add_message_to_thread(content)
        prefix = f"Today's date is {timezone.now().isoformat()}"
        self.run_thread(prefix + "\n" + assistant_instructions, assistant_id)
        assistant_result, assistant_output = self.get_assistant_output()
        logger.info(f"Assistant raw output is {assistant_output}")
        assistant_json_workflow = None
        if assistant_result:
            assistant_json_workflow = self.get_json_workflow_from_assistant_output(assistant_output)
        else:
            logger.info(f"Assistant output when assistant result is false is {assistant_output}")
        return assistant_json_workflow

    def track_threads_status_and_retrieve_combined_output(self, track_threads: dict):
        """
        Track the status of threads and retrieve the combined output when completed.

        Args:
            track_threads (dict): Dictionary containing thread IDs and corresponding run IDs.

        Returns:
            str: Combined output from assistant messages.
        """
        combined_output = ""
        threads_to_remove = []
        time_elapsed = 0
        timeout = constance_config.ASSISTANT_TIMEOUT_CUTOFF
        retry_time = constance_config.ASSISTANT_POLLING_RETRY_INTERVAL
        while track_threads and time_elapsed < timeout:
            for thread_id, run_id in track_threads.items():
                run_steps = self.client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run_id)
                if run_steps.status == "completed":
                    threads_to_remove.append(thread_id)
                    messages = self.client.beta.threads.messages.list(thread_id=thread_id)
                    for each in messages:
                        if each.role == "assistant":
                            combined_output = combined_output + "\n" + each.content[0].text.value + "\n"
                if run_steps.status == "failed":
                    threads_to_remove.append(thread_id)

            for thread in threads_to_remove:
                track_threads.pop(thread)
            threads_to_remove.clear()
            if not track_threads:
                break
            time.sleep(retry_time)
            time_elapsed = time_elapsed + retry_time
        return combined_output

    @staticmethod
    def get_json_workflow_from_assistant_output(assistant_output: str) -> Union[dict, None]:
        """
        search and return json workflow from the assistant output
        """
        try:
            pattern = r"```json\n(.*?)\n```"
            match = re.search(pattern, assistant_output, re.DOTALL)
            assistant_json_workflow = None
            if match:
                assistant_json_workflow = json.loads(match.group(1))
            return assistant_json_workflow
        except JSONDecodeError as e:
            logger.exception(
                "Error while decoding json workflow from assistant output",
                extra={"exception_details": str(e)},
            )
            return None

    @staticmethod
    def get_python_code_from_assistant_output(assistant_output: str):
        """
        search and return Python code from the assistant output
        """
        pattern = r"```python\n(.*?)\n```"
        match = re.search(pattern, assistant_output, re.DOTALL)
        if match:
            return match.group(1)
        else:
            return None
