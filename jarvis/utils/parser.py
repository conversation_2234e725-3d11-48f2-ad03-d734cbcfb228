import pytz
import logging
from copy import deepcopy

from execfn.common.utils.datetime import get_utc_datetime

logger = logging.getLogger(__name__)


class DateTimeParser:
    """
    A parser for handling datetime strings and converting them to a specified timezone.

    Attributes:
        user_timezone (timezone): The timezone to which the datetime strings will be converted.
    """

    def __init__(self, user_timezone):
        """
        Initialize the DateTimeParser with the user's timezone.

        Args:
            user_timezone (str): A string representing the user's timezone, e.g., "America/New_York".
        """
        self.user_timezone = pytz.timezone(user_timezone)

    def parse(self, value):
        """
        Parse a datetime string and convert it to the user's timezone.

        Args:
            value (str): The datetime string to be parsed.

        Returns:
            str: A datetime string in isoformat representing the parsed value in the user's timezone.
        """
        return get_utc_datetime(input_datetime=value, time_zone=self.user_timezone).isoformat()


class WorklowJsonParser:
    """
    A utility class for parsing nested JSON data with a specified schema.

    Attributes:
        json_data (dict): The nested JSON data to be parsed.
        json_schemas (dict): A dictionary of JSON schemas.
        metadata (dict): Additional metadata, e.g., user's timezone.
        parsers (dict): A dictionary of parsers for different data types.
    """

    def __init__(self, json_data, json_schemas, metadata):
        """
        Initialize the DataParser with nested JSON data, schemas, and metadata.

        Args:
            json_data (dict): The nested JSON data to be parsed.
            json_schemas (dict): A dictionary of JSON schemas.
            metadata (dict): Additional metadata, e.g., user's timezone.
        """
        self.json_data = json_data
        self.json_schemas = json_schemas
        self.metadata = metadata
        self.parsers = {
            "date-time": DateTimeParser(metadata.get("timezone") or "UTC"),
            # Add more parsers for other data types as needed
        }

    def parse(self):
        """
        Parse workflow json object and returns parsed workflow.

        Returns:
            dict: The updated workflow object with parsed parameter values.
        """
        updated_json_data = {}

        # Iterate through all keys in the nested JSON data
        for key in self.json_data:
            data = self.json_data[key]
            if key == "actions":
                updated_data = self._parse_actions(data)
            elif key == "constraints":
                updated_data = self._parse_constraints(data)
            else:
                updated_data = self._parse_trigger(data)
            updated_json_data[key] = updated_data
        return updated_json_data

    def _parse_actions(self, data):
        """
        Parses actions of a workflow

        Args:
            data (list): a list of actions

        Returns:
            list: List of parsed actions
        """
        try:
            parsed_data = []
            for component in data:
                tag = component.get("tag")
                if schema := self.json_schemas.get(tag):
                    parsed_component = self.__parse_item(component, schema)
                    parsed_data.append(parsed_component)
            return parsed_data
        except Exception as e:
            logger.exception("Failed to parse workflow actions", extra={"actions": data, "details": e})
            return data

    def _parse_constraints(self, data):
        """
        Parses constraints of a workflow

        Args:
            data (list): a list of constraints groups

        Returns:
            list: List of parsed constraint groups
        """
        try:
            parsed_data = []
            for group in data:
                parsed_group = []
                for component in group:
                    tag = component.get("tag")
                    if schema := self.json_schemas.get(tag):
                        parsed_component = self.__parse_item(component, schema)
                        parsed_group.append(parsed_component)
                parsed_data.append(parsed_group)
            return parsed_data
        except Exception as e:
            logger.exception("Failed to parse workflow constraints", extra={"constraints": data, "details": e})
            return data

    def _parse_trigger(self, component):
        """
        Parses trigger of a workflow

        Args:
            component (dict): a dictionary representating trigger

        Returns:
            dict: Parsed trigger
        """
        try:
            tag = component.get("tag")
            if schema := self.json_schemas.get(tag):
                parsed_component = self.__parse_item(component, schema)
                return parsed_component
            else:
                return component
        except Exception as e:
            logger.exception("Failed to parse workflow trigger", extra={"trigger": component, "details": e})
            return component

    def __parse_item(self, item, schema):
        """
        Parse parameter in an item and update the original object in place.

        Args:
            item (dict): The item to be parsed and updated.
            schema (dict): The schema the item is supposed to be following.

        Returns:
            dict: The updated item with parsed paramater values.
        """
        try:
            updated_item = deepcopy(item)
            properties = schema["properties"]
            for field, value in item.get("params", {}).items():
                format = properties[field].get("format")
                if format in self.parsers:
                    parser = self.parsers[format]
                    parsed_value = parser.parse(value)
                    if parsed_value is not None:
                        updated_item["params"][field] = parsed_value
            return updated_item
        except Exception as e:
            logger.exception("Failed to parse workflow component", extra={"component": item, "details": e})
            return item
