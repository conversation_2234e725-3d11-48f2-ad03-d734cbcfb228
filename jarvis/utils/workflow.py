import importlib
import inspect
import logging
import uuid
from copy import deepcopy
from typing import <PERSON><PERSON>, Dict

from django.apps import apps
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db.models import QuerySet, <PERSON>, Sum, IntegerField
from django.db.models.fields.json import KT
from django.db.models.functions import Cast
from jsonschema import validate
from jsonschema.exceptions import ValidationError

from jarvis.models import WorkflowExecutionLog, JarvisUserLimits
from jarvis.utils.actions import BaseAction
from jarvis.utils.constraints import BaseConstraint
from jarvis.utils.typing import JarvisPayload, JarvisPayloadType

logger = logging.getLogger(__name__)

User = get_user_model()

# NOTE : We've kept the additional fields `description` in the schema as this might be used in future where we build a
# pipeline that can directly ingest this schema definition in the assistant

JARVIS_WORKFLOW_SCHEMA = {
    "type": "object",
    "properties": {
        "trigger": {
            "type": "object",
            "properties": {
                "tag": {
                    "type": "string",
                    "description": "Type of the workflow triggering conditions.",
                    "enum": ["event_driven", "scheduled", "instantaneous", "periodic"],
                },
                "params": {
                    "type": "object",
                    "description": "Parameters corresponding to the workflow type as described in the retrieval file. These parameters define the execution details of a specific workflow type.",
                },
            },
        },
        "constraints": {
            "type": "array",
            "description": "List of list of constraint evaluated whose results will be evaluated with an OR condition.",
            "items": {
                "type": "array",
                "description": "List of constraints to be evaluated with an AND condition.",
                "items": {
                    "type": "object",
                    "description": "Constraint object defining the condition details.",
                    "properties": {
                        "tag": {
                            "description": "Tag of the constraint as described in the retrieval file.",
                            "type": "string",
                        },
                        "params": {
                            "description": "Parameters related to the specific constraint as defined in the retrieval file.",
                            "type": "object",
                        },
                    },
                },
            },
        },
        "actions": {
            "type": "array",
            "description": "List of actions to be performed if any constraint set is satisfied.",
            "items": {
                "type": "object",
                "description": "Action defining the task to be performed.",
                "properties": {
                    "tag": {"description": "Tag of the action as defined in the retrieval file.", "type": "string"},
                    "params": {
                        "description": "Parameters related to the specific action as defined in the retrieval file.",
                        "type": "object",
                    },
                },
            },
        },
    },
}


def get_jarvis_eca_classes(module_name, base_class):
    """
    Get all classes for jarvis ECA module
    """
    installed_apps = [app for app in apps.get_app_configs()]
    all_classes = []
    for app in installed_apps:
        try:
            module_path = f"{app.name}.{module_name}"
            jarvis_module = importlib.import_module(module_path)
            module_classes = inspect.getmembers(jarvis_module, inspect.isclass)
            jarvis_classes = [
                cls for name, cls in module_classes if issubclass(cls, base_class) and cls.__module__ == module_path
            ]
            all_classes.extend(jarvis_classes)
        except ImportError:
            pass
    return all_classes


JARVIS_ACTIONS = {action_cls.tag: action_cls for action_cls in get_jarvis_eca_classes("jarvis_actions", BaseAction)}
JARVIS_CONSTRAINTS = {
    constraint_cls.tag: constraint_cls
    for constraint_cls in get_jarvis_eca_classes("jarvis_constraints", BaseConstraint)
}


class WorkflowExecutor:
    def __init__(self, *, workflow, user, payload):
        self.payload = payload
        self.filtered_payload = None
        self.user = user
        self.workflow = workflow

    def get_constraints(self):
        """
        Read the workflow definition and initialise constraint classes for evaluation
        """
        raw_constraint_group_list = self.workflow.json_workflow.get("constraints", [])
        if not raw_constraint_group_list:
            logger.info(f"No constraints found for workflow def {self.workflow.id}")
            return []
        constraint_group_sets = []
        constraint_names = []
        for constraint_group in raw_constraint_group_list:
            constraint_group_classes = []
            for constraint in constraint_group:
                tag = constraint.get("tag")
                params = constraint.get("params")
                constraint_cls = JARVIS_CONSTRAINTS.get(tag)
                if not constraint_cls:
                    logger.info(f"Invalid constraint tag {tag} for workflow def ID {self.workflow.id}")
                    continue
                constraint_group_classes.append(constraint_cls(user=self.user, params=params))
                constraint_names.append(tag)
            if constraint_group_classes:
                constraint_group_sets.append(constraint_group_classes)
        logger.info(f"Fetched constraints. ConstraintNames : {constraint_names}")
        return constraint_group_sets

    def get_actions(self):
        """
        Read the workflow definition and initialise action classes for evaluation
        """
        raw_actions_list = self.workflow.json_workflow.get("actions", [])
        if not raw_actions_list:
            logger.info(f"No actions found for workflow def {self.workflow.id}")
            return []
        action_classes_list = []
        action_names = []
        for action in raw_actions_list:
            tag = action.get("tag")
            params = action.get("params")
            user_query = self.workflow.user_query
            action_cls = JARVIS_ACTIONS.get(tag)
            if not action_cls:
                logger.info(f"Invalid action tag {tag} for workflow def ID {self.workflow.id}")
                continue
            action_classes_list.append(
                action_cls(user=self.user, params=params, user_query=user_query, workflow=self.workflow)
            )
            action_names.append(tag)
        logger.info(f"Fetched actions. ActionNames : {action_names}")
        return action_classes_list

    def evaluate_constraints(self, constraint_groups):  # noqa (method can be static)
        """
        Evaluate constraints for the workflow def
        Args:
            constraint_groups: list of constraint groups

        Returns:
            boolean if the constraints are true
        """
        any_constraint_group_satisfied = False
        for subarray in constraint_groups:
            # If the subarray is empty, the final result is False. Else we initialise the subarray result with True,
            # after which we perform an AND operation between the all the constraints of the subarray
            constraint_group_satisfied = len(subarray) != 0
            # here we're creating a copy of the payload so that each constraint group can be evaluated with the original
            # set of data
            if isinstance(self.payload, (list, dict)):
                payload = deepcopy(self.payload)
            else:
                payload = self.payload
            for constraint in subarray:
                constraint_satisfied, filtered_payload = constraint.evaluate(payload=payload)
                payload = filtered_payload
                logger.info(f"{constraint.tag} : {constraint_satisfied}")
                if not (constraint_group_satisfied := constraint_group_satisfied and constraint_satisfied):
                    break
            if constraint_group_satisfied:
                # assign the filtered payload for execution of the action
                if self.filtered_payload is None:
                    self.filtered_payload = payload
                else:
                    if isinstance(self.payload, QuerySet):
                        self.filtered_payload = self.filtered_payload | payload
                    elif isinstance(payload, list):
                        self.filtered_payload.extend(payload)
                    elif isinstance(payload, dict):
                        self.filtered_payload.update(payload)
                    else:
                        self.filtered_payload = payload
            any_constraint_group_satisfied |= constraint_group_satisfied
        logger.info(f"Evaluated constraints. Result {any_constraint_group_satisfied}")
        return any_constraint_group_satisfied

    def execute_actions(self, actions_list):
        """
        Execute all actions for workflow def
        Args:
            actions_list: list of action class instances to be executed

        Returns:
            None
        """
        for action in actions_list:
            try:
                action.execute(payload=self.filtered_payload)
                logger.info(f"Executed action. ActionName {action.tag}")
            except:
                logger.exception("Failed to execute JARVIS action")

    def run(self):
        """
        Run the workflow by executing all associated actions if the constraints are True

        Returns:
            None
        """
        constraints_satisfied = False
        # if there are no constraint groups to evaluate, the result should be False
        if self.can_execute_workflow():
            if (constraint_groups := self.get_constraints()) and (
                constraints_satisfied := self.evaluate_constraints(constraint_groups)
            ):
                if actions_list := self.get_actions():
                    self.execute_actions(actions_list)
                    self.log_workflow_execution()
                else:
                    # if there are no actions to execute, the result should be False even though constraints were
                    # satisfied
                    constraints_satisfied = False
        return constraints_satisfied

    def can_execute_workflow(self):
        can_execute_workflow = True
        try:
            user_limits = JarvisUserLimits.objects.get(user=self.user)
            total_messages_processed = (
                WorkflowExecutionLog.objects.filter(workflow__user=self.user)
                .annotate(messages_processed=Cast(KT("metadata__messages_processed"), IntegerField()))
                .aggregate(Sum("messages_processed"))["messages_processed__sum"]
            )
            if total_messages_processed and total_messages_processed >= user_limits.message_limit:
                can_execute_workflow = False
        except JarvisUserLimits.DoesNotExist:
            pass
        return can_execute_workflow

    def log_workflow_execution(self):
        if isinstance(self.filtered_payload, (list, tuple, set)):
            messages_processed = len(self.filtered_payload)
        elif isinstance(self.filtered_payload, QuerySet):
            messages_processed = self.filtered_payload.count()
        else:
            messages_processed = 1
        WorkflowExecutionLog.objects.create(
            workflow=self.workflow,
            status=WorkflowExecutionLog.EXECUTION_SUCCESS,
            metadata={"messages_processed": messages_processed},
        )


def set_jarvis_payload_cache(payload: JarvisPayload) -> str:
    """
    Serialize and set jarvis payload cache
    Args:
        payload: jarvis workflow payload

    Returns:
        cache key
    """
    if isinstance(payload, QuerySet):
        qs_model_name = payload.model._meta.model_name  # noqa (access to protected member)
        qs_app_label = payload.model._meta.app_label  # noqa (access to protected member)
        jarvis_payload = {
            "type": JarvisPayloadType.QUERYSET.value,
            "payload": {
                "model_name": qs_model_name,
                "app_label": qs_app_label,
                "ids": list(payload.values_list("id", flat=True)),
            },
        }
    elif isinstance(payload, Model):
        obj_model_name = payload._meta.model_name  # noqa (access to protected member)
        obj_app_label = payload._meta.app_label  # noqa (access to protected member)
        jarvis_payload = {
            "type": JarvisPayloadType.MODEL_OBJECT.value,
            "payload": {"model_name": obj_model_name, "app_label": obj_app_label, "id": payload.id},
        }
    elif isinstance(payload, (list, dict)):
        jarvis_payload = {"type": JarvisPayloadType.RAW.value, "payload": payload}
    else:
        raise NotImplementedError("Invalid payload value.")
    cache_key = str(uuid.uuid4())
    cache.set(cache_key, jarvis_payload, 300)
    return cache_key


def get_jarvis_payload_from_cache(cache_key: str) -> JarvisPayload:
    """
    Fetch and deserialize jarvis payload
    Args:
        cache_key: cache key for payload

    Returns:
        jarvis payload
    """
    jarvis_payload = cache.get(cache_key)
    if jarvis_payload["type"] == JarvisPayloadType.QUERYSET.value:
        qs_app_label = jarvis_payload["payload"]["app_label"]
        qs_model_name = jarvis_payload["payload"]["model_name"]
        Model = apps.get_model(f"{qs_app_label}.{qs_model_name}")
        payload = Model.objects.filter(id__in=jarvis_payload["payload"]["ids"])
    elif jarvis_payload["type"] == JarvisPayloadType.MODEL_OBJECT.value:
        obj_app_label = jarvis_payload["payload"]["app_label"]
        obj_model_name = jarvis_payload["payload"]["model_name"]
        Model = apps.get_model(f"{obj_app_label}.{obj_model_name}")
        payload = Model.objects.get(id=jarvis_payload["payload"]["id"])
    elif jarvis_payload["type"] == JarvisPayloadType.RAW.value:
        payload = jarvis_payload["payload"]
    else:
        raise NotImplementedError("Invalid payload type.")
    return payload


def is_workflow_definition_valid(jarvis_workflow: Dict) -> Tuple[bool, str]:
    """
    Validate JARVIS Workflow Definition using the defined JSON Schema
    Args:
        jarvis_workflow : JSON workflow generated by LLM

    Returns:
        if the workflow is valid, and error if any
    """
    is_valid, error = True, None
    try:
        validate(jarvis_workflow, JARVIS_WORKFLOW_SCHEMA)
    except ValidationError as e:
        is_valid = False
        error = str(e)
    return is_valid, error
