from typing import Dict

from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from jsonschema import Draft7Validator
from jsonschema.exceptions import SchemaError
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from applications.models import ScheduledTask
from execfn.common.utils.datetime import STANDARD_DATETIME_FORMAT
from jarvis.models import Assistant, Workflow, WorkflowEvent, WorkflowComponentDefinition, JarvisUserLimits
from jarvis.tasks import instantaneous_execution_of_jarvis_workflow
from jarvis.utils.workflow import is_workflow_definition_valid

User = get_user_model()


class WorkflowSerializer(serializers.ModelSerializer):
    assistant = serializers.SlugRelatedField(slug_field="name", queryset=Assistant.objects.all())

    class Meta:
        model = Workflow
        fields = ("assistant", "user_query", "id", "state", "json_workflow", "is_active", "name")
        read_only_fields = ("id",)

    def validate(self, data):
        if "is_active" in data and self.instance.state != Workflow.STATE_CONFIGURED:
            raise serializers.ValidationError(
                {"error": f"Unable to activate workflow that hasn't been successfully configured."}
            )
        return data

    def create(self, validated_data):
        request = self.context["request"]
        user = request.user
        validated_data["user"] = user
        try:
            user_limits = JarvisUserLimits.objects.get(user=user)
            existing_workflows_count = Workflow.objects.filter(user=user).count()
            if existing_workflows_count >= user_limits.workflow_limit:
                raise ValidationError("You've reached the maximum limit for workflows")
        except JarvisUserLimits.DoesNotExist:
            pass
        return super().create(validated_data)

    def create_scheduled_task(self, workflow):
        """
        Create a new scheduled task for Jarvis workflow
        Args:
            workflow: workflow instance
        """
        periodic_task_params = workflow.json_workflow["trigger"]["params"]
        start_date_time = timezone.datetime.strptime(periodic_task_params["start_date_time"], STANDARD_DATETIME_FORMAT)
        task_name = "jarvis.tasks.periodic_execution_of_jarvis_workflow"
        if (period_type := periodic_task_params.get("period_type")) and (
            period_interval := periodic_task_params.get("period_interval")
        ):
            scheduled_task = ScheduledTask.create_periodic_task(
                workflow.user, task_name, start_date_time, period_type, period_interval, task_args=(workflow.id,)
            )
        else:
            scheduled_task = ScheduledTask.create_one_off_task(
                workflow.user, task_name, start_date_time, task_args=(workflow.id,)
            )
        workflow.scheduled_task = scheduled_task
        workflow.save()

    def update_or_create_workflow_trigger(self, workflow: Workflow, old_json_workflow: Dict):
        """
        Update workflow trigger
        1. For event driven workflow : Update the event type
        2. For periodic or scheduled workflow : Update the scheduled task

        Args:
            workflow: update workflow instance
            old_json_workflow: old workflow json
        """
        old_trigger_type = old_json_workflow["trigger"]["tag"]
        new_trigger_type = workflow.json_workflow["trigger"]["tag"]
        # NOTE : This could also mean that the workflow has been configured and is being activated for the first time
        if old_trigger_type == new_trigger_type:
            # if the trigger type hasn't changed, the only thing that can change for an event driven workflow is the tag
            if new_trigger_type == Workflow.TYPE_EVENT_DRIVEN:
                event_tag = workflow.json_workflow["trigger"]["params"]["event_tag"]
                # TODO : Validate only allowed event tags can be configured
                WorkflowEvent.objects.update_or_create(workflow=workflow, defaults={"tag": event_tag})
            # if the trigger type hasn't changed, we simply delete the existing scheduled task and create a new one for
            # scheduled and periodic workflows
            elif new_trigger_type in [Workflow.TYPE_PERIODIC, Workflow.TYPE_SCHEDULED]:
                if workflow.scheduled_task:
                    workflow.scheduled_task.delete()
                self.create_scheduled_task(workflow)
        else:
            # if the trigger type as changed, delete the old workflow event or scheduled task
            if old_trigger_type in {Workflow.TYPE_PERIODIC, Workflow.TYPE_SCHEDULED} and workflow.scheduled_task:
                workflow.scheduled_task.delete()
            elif old_trigger_type == Workflow.TYPE_EVENT_DRIVEN:
                WorkflowEvent.objects.filter(workflow=workflow).delete()
            # create new workflow event or scheduled task as applicable
            if new_trigger_type == Workflow.TYPE_EVENT_DRIVEN:
                event_tag = workflow.json_workflow["trigger"]["params"]["event_tag"]
                WorkflowEvent.objects.create(workflow=workflow, tag=event_tag)
            elif new_trigger_type in [Workflow.TYPE_PERIODIC, Workflow.TYPE_SCHEDULED]:
                self.create_scheduled_task(workflow)

    def update(self, instance, validated_data):
        """
        Update the state of a Jarvis Workflow.
        NOTE : At the time of implementation we're only allowed to activate/deactivate the workflow, and update its
        definition.

        Args:
            instance: workflow instance to be updated
            validated_data: validated workflow data

        Returns:
            updated workflow instance
        """
        # Validate that only activation/deactivation and workflow definition updates are allowed
        allowed_fields = {"json_workflow", "is_active"}
        if disallowed_fields := validated_data.keys() - allowed_fields:
            raise serializers.ValidationError(
                {"error": f"Updating fields {', '.join(disallowed_fields)} is not allowed."}
            )
        if updated_json_workflow := validated_data.get("json_workflow"):
            is_json_workflow_valid, errors = is_workflow_definition_valid(updated_json_workflow)
            if not is_json_workflow_valid:
                raise serializers.ValidationError(errors)

        with transaction.atomic():
            # Capture old state of the workflow before update
            old_json_workflow = instance.json_workflow
            was_workflow_active = instance.is_active
            # Set the workflow type as per new workflow trigger if we're updating the workflow json
            if json_workflow := validated_data.get("json_workflow"):
                validated_data["type"] = json_workflow["trigger"]["tag"]
            # Update the workflow
            updated_instance = super().update(instance, validated_data)
            # Check if the workflow trigger has changed, if yes then update the related event information or scheduled
            # tasks as applicable
            old_workflow_trigger = old_json_workflow["trigger"]
            new_workflow_trigger = updated_instance.json_workflow["trigger"]
            new_trigger_type = updated_instance.json_workflow["trigger"]["tag"]
            workflow_event_exists = WorkflowEvent.objects.filter(workflow=updated_instance).exists()
            # Trigger update or create  of workflow trigger if
            # 1. workflow trigger has changed
            # 2. workflow trigger is of type event driven and there is no existing workflow event
            # 3. workflow trigger is of type scheduled or periodic but there is no scheduled task
            if (
                old_workflow_trigger != new_workflow_trigger
                or (new_trigger_type == Workflow.TYPE_EVENT_DRIVEN and not workflow_event_exists)
                or (
                    new_trigger_type in {Workflow.TYPE_PERIODIC, Workflow.TYPE_SCHEDULED}
                    and not updated_instance.scheduled_task
                )
            ):
                self.update_or_create_workflow_trigger(updated_instance, old_json_workflow)
                updated_instance.refresh_from_db()

            if (
                new_trigger_type in {Workflow.TYPE_PERIODIC, Workflow.TYPE_SCHEDULED}
                and updated_instance.scheduled_task
            ):
                # Irrespective of the fact if the workflow trigger was updated or not, if the workflow was activated
                # then enable the scheduled tasks, else if the workflow was deactivated then disable the scheduled tasks
                if not was_workflow_active and instance.is_active:
                    updated_instance.scheduled_task.enable()
                elif was_workflow_active and not instance.is_active:
                    updated_instance.scheduled_task.disable()
            elif new_trigger_type == Workflow.TYPE_INSTANTANEOUS and not was_workflow_active and instance.is_active:
                # If an instantaneous workflow is activated, trigger the execution
                transaction.on_commit(lambda: instantaneous_execution_of_jarvis_workflow.delay(instance.id))
        return updated_instance


class WorkflowListSerializer(serializers.ModelSerializer):
    class Meta(WorkflowSerializer.Meta):
        fields = ("id", "is_active", "created", "state", "name")


class WorkflowComponentDefinitionSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkflowComponentDefinition
        fields = ("name", "tag", "description", "input", "type", "ui_schema")

    def validate_input(self, value):
        try:
            Draft7Validator.check_schema(value)
        except SchemaError as e:
            raise serializers.ValidationError(f"Invalid JSON schema: {e.message}")
