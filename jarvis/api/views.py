import logging

from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from django.shortcuts import redirect, get_object_or_404
from django.utils import timezone
from google.auth.exceptions import RefreshError
from google.oauth2.credentials import Credentials
from googleapiclient._auth import refresh_credentials
from googleapiclient.discovery import build
from rest_framework.decorators import action
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.filters import OrderingFilter
from rest_framework.mixins import ListModelMixin
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet, ModelViewSet, ViewSet

from accounts.utils.base import verify_and_decode_oauth_state
from accounts.utils.google import get_google_sign_in_flow
from jarvis.api.serializers import WorkflowComponentDefinitionSerializer, WorkflowSerializer, WorkflowListSerializer
from jarvis.models import Workflow, WorkflowComponentDefinition, JarvisGoogleAuthCredentials
from jarvis.tasks import configure_workflow
from mailbot.utils.defaults import JARVIS_GOOGLE_AUTH_REQUIRED_SCOPES

User = get_user_model()
logger = logging.getLogger(__name__)


class WorkflowViewSet(ModelViewSet):
    lookup_field = "id"
    filter_backends = [OrderingFilter]
    ordering_fields = ["created"]

    def get_queryset(self):
        user = self.request.user
        queryset = Workflow.objects.filter(user=user)
        return queryset

    def get_serializer_class(self):
        if self.action == "list":
            return WorkflowListSerializer
        else:
            return WorkflowSerializer

    def create(self, request, *args, **kwargs):
        response = super().create(request, *args, **kwargs)
        configure_workflow.delay(response.data["id"])
        return response


class WorkflowComponentDefinitionViewSet(ListModelMixin, GenericViewSet):
    serializer_class = WorkflowComponentDefinitionSerializer
    queryset = WorkflowComponentDefinition.objects.all()


class JarvisGoogleAuthViewSet(ViewSet):
    CALLBACK_URI = f"{settings.BACKEND_BASE_URL}/api/v1/jarvis/google-auth/callback/"

    @action(detail=False, methods=["GET"], url_path="login")
    def login(self, request: Request):
        flow = get_google_sign_in_flow(
            client_id=settings.JARVIS_GOOGLE_AUTH.get("client_id"),
            client_secret=settings.JARVIS_GOOGLE_AUTH.get("client_secret"),
            scopes=JARVIS_GOOGLE_AUTH_REQUIRED_SCOPES,
            callback_uri=self.CALLBACK_URI,
        )
        # Generate URL for request to Google's OAuth 2.0 server.
        authorization_url, _ = flow.authorization_url(
            # Enable offline access so that you can refresh an access token without
            # re-prompting the user for permission. Recommended for web server apps.
            access_type="offline",
            prompt="consent",
            # Enable incremental authorization. Recommended as a best practice.
            include_granted_scopes="false",
        )
        return redirect(authorization_url)

    @action(detail=False, methods=["GET"], url_path="callback", authentication_classes=(), permission_classes=())
    def callback(self, request: Request):
        response = redirect(settings.JARVIS_GOOGLE_AUTH.get("redirect_url"))
        state_token = request.query_params.get("state")
        code = request.query_params.get("code")
        try:
            verify_and_decode_oauth_state(settings.SERVICE_PROVIDER_GOOGLE, state_token)
        except AuthenticationFailed:
            return response
        flow = get_google_sign_in_flow(
            client_id=settings.JARVIS_GOOGLE_AUTH.get("client_id"),
            client_secret=settings.JARVIS_GOOGLE_AUTH.get("client_secret"),
            scopes=JARVIS_GOOGLE_AUTH_REQUIRED_SCOPES,
            callback_uri=self.CALLBACK_URI,
        )
        try:
            flow.fetch_token(code=code)
        except:
            # User clicks "cancel" on any consent screen
            logger.info("Incremental auth flow for JARVIS failed due to unknown error")
            return response
        else:
            credentials: Credentials = flow.credentials
            service = build("oauth2", "v2", credentials=credentials)
            user_info = service.userinfo().get().execute()
            if not (email := user_info.get("email")):
                logger.exception("Google user email not found in user info", extra={"user_info": user_info})
                return response
            granted_scopes = credentials.granted_scopes
            if set(JARVIS_GOOGLE_AUTH_REQUIRED_SCOPES).difference(set(granted_scopes)):
                logger.info("All scopes were not granted for JARVIS")
                # TODO : Add a message to response so that user can be notified on the frontend
                return response
            user = get_object_or_404(User, email=email)
            auth_creds, _ = JarvisGoogleAuthCredentials.objects.get_or_create(user=user)
            auth_creds.granted_scopes = granted_scopes
            auth_creds.access_token = credentials.token
            auth_creds.refresh_token = credentials.refresh_token
            auth_creds.expires_at = credentials.expiry
            auth_creds.save()
            return response

    @action(detail=False, methods=["GET"], url_path="check")
    def check(self, request: Request):
        valid_auth_creds = False
        with transaction.atomic():
            try:
                auth_creds = JarvisGoogleAuthCredentials.objects.select_for_update(of=("self",)).get(user=request.user)
            except JarvisGoogleAuthCredentials.DoesNotExist:
                pass
            else:
                access_token, refresh_token = auth_creds.get_credentials()
                valid_auth_creds = all([access_token, refresh_token])
        return Response(data={"auth_creds_exist": valid_auth_creds})
