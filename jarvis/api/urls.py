from rest_framework.routers import DefaultRouter

from jarvis.api.views import WorkflowViewSet, WorkflowComponentDefinitionViewSet, JarvisGoogleAuthViewSet

router = DefaultRouter()
router.register(f"workflow", WorkflowViewSet, basename="workflow")
router.register(f"workflow-component-def", WorkflowComponentDefinitionViewSet, basename="workflow-component-definition")
router.register(f"google-auth", JarvisGoogleAuthViewSet, basename="google-auth")

urlpatterns = router.urls
