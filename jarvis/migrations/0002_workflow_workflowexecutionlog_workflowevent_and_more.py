# Generated by Django 4.2.5 on 2023-12-07 10:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        ("applications", "0011_exceptionlist"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("jarvis", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Workflow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("user_query", models.TextField()),
                ("json_workflow", models.<PERSON><PERSON><PERSON><PERSON>(null=True)),
                ("state", models.<PERSON>r<PERSON>ield(default="initiated", max_length=55)),
                ("is_active", models.Boolean<PERSON>ield(default=True)),
                (
                    "assistant",
                    models.Foreign<PERSON>ey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="jarvis.assistant",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="WorkflowExecutionLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("metadata", models.JSONField(null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("success", "Execution success"),
                            ("failure", "Execution failure"),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    "workflow",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="jarvis.workflow",
                    ),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="WorkflowEvent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("tag", models.CharField(max_length=125)),
                (
                    "workflow",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="jarvis.workflow",
                    ),
                ),
            ],
            options={
                "unique_together": {("tag", "workflow")},
            },
        ),
        migrations.CreateModel(
            name="ScheduledWorkflowTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "scheduled_task",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="applications.scheduledtask",
                    ),
                ),
                (
                    "workflow",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="jarvis.workflow",
                    ),
                ),
            ],
            options={
                "unique_together": {("workflow", "scheduled_task")},
            },
        ),
    ]
