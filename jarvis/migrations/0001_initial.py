# Generated by Django 4.2.5 on 2023-11-27 12:26

from django.db import migrations, models
import django.db.models.deletion
import django_jsonform.models.fields


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Assistant",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("openai_id", models.CharField(max_length=255, unique=True)),
                ("name", models.CharField(max_length=55, unique=True)),
                (
                    "openai_file_ids",
                    django_jsonform.models.fields.ArrayField(
                        base_field=models.TextField(), blank=True, null=True, size=None
                    ),
                ),
                ("instructions", models.TextField()),
                ("user_prompt", models.TextField(blank=True, null=True)),
                ("run_instructions", models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="AssistantLogs",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                ("user_query", models.TextField()),
                ("output", models.TextField()),
                (
                    "assistant",
                    models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to="jarvis.assistant"),
                ),
            ],
        ),
    ]
