# Generated by Django 4.2.5 on 2024-02-05 10:50

from django.db import migrations, models
import django_extensions.db.fields
import django_jsonform.models.fields


class Migration(migrations.Migration):
    dependencies = [
        ("jarvis", "0007_alter_workflow_type_alter_workflowevent_workflow"),
    ]

    operations = [
        migrations.CreateModel(
            name="WorkflowComponentDefinition",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("name", models.CharField(max_length=128)),
                ("tag", models.CharField(max_length=64, unique=True)),
                ("description", models.TextField()),
                ("input", django_jsonform.models.fields.J<PERSON><PERSON><PERSON>(default=list)),
                (
                    "type",
                    models.CharField(
                        choices=[("trigger", "Trigger"), ("constraint", "Constraint"), ("action", "Action")],
                        db_index=True,
                        max_length=16,
                    ),
                ),
            ],
            options={
                "verbose_name": "Workflow Component Definition",
                "verbose_name_plural": "Workflow Component Definitions",
            },
        ),
    ]
