# Generated by Django 4.2.5 on 2024-10-22 02:50

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("jarvis", "0012_workflow_metadata"),
    ]

    operations = [
        migrations.CreateModel(
            name="JarvisGoogleAuthCredentials",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("encrypted_access_token", models.BinaryField(blank=True, null=True)),
                ("encrypted_refresh_token", models.BinaryField(blank=True, null=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("granted_scopes", models.JSONField(default=list)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="jarvis_google_auth_credentials",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
    ]
