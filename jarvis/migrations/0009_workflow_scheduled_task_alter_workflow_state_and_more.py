# Generated by Django 4.2.5 on 2024-02-05 15:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("applications", "0016_alter_subscription_secondary_users"),
        ("jarvis", "0008_workflowcomponentdefinition"),
    ]

    operations = [
        migrations.AddField(
            model_name="workflow",
            name="scheduled_task",
            field=models.ForeignKey(
                blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to="applications.scheduledtask"
            ),
        ),
        migrations.AlterField(
            model_name="workflow",
            name="state",
            field=models.CharField(
                choices=[
                    ("failed", "Failed"),
                    ("configured", "Configured"),
                    ("processing", "Processing"),
                    ("initiated", "Initiated"),
                ],
                default="initiated",
                max_length=55,
            ),
        ),
        migrations.AlterField(
            model_name="workflowevent",
            name="workflow",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="workflow_events",
                related_query_name="workflow_events",
                to="jarvis.workflow",
            ),
        ),
    ]
