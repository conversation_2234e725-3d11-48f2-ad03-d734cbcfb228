# Generated by Django 4.2.5 on 2024-10-23 14:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("jarvis", "0013_jarvisgoogleauthcredentials"),
    ]

    operations = [
        migrations.CreateModel(
            name="OpenAICostAnalysis",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "created",
                    django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name="created"),
                ),
                (
                    "modified",
                    django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name="modified"),
                ),
                ("llm", models.CharField()),
                ("total_tokens", models.PositiveIntegerField()),
                ("prompt_tokens", models.PositiveIntegerField()),
                ("completion_tokens", models.PositiveIntegerField()),
                ("total_cost", models.FloatField()),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="openai_cost_analysis",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "workflow",
                    models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to="jarvis.workflow"),
                ),
            ],
            options={
                "get_latest_by": "modified",
                "abstract": False,
            },
        ),
    ]
