from itertools import chain

from django.core.management.base import BaseCommand, CommandParser

from jarvis.models import WorkflowComponentDefinition
from jarvis.utils.actions import BaseAction
from jarvis.utils.workflow import JARVIS_ACTIONS, JARVIS_CONSTRAINTS


class Command(BaseCommand):
    help = "Import workflow component definition from code to the database"

    def add_arguments(self, parser: CommandParser) -> None:
        parser.add_argument("--resync", action="store_true", help="Sync existing components if the flag is set")

    def handle(self, *args, **options):
        resync = options["resync"]
        component_definitions = {item.tag: item for item in WorkflowComponentDefinition.objects.all()}
        combined_dict = chain(JARVIS_ACTIONS.items(), JARVIS_CONSTRAINTS.items())
        for tag, cls in combined_dict:
            try:
                name_description = " ".join(tag.capitalize().split("_"))
                type = "action" if issubclass(cls, BaseAction) else "constraint"

                # Check if component definition exists in db
                component_definition_in_db = component_definitions.get(tag)
                if not component_definition_in_db:
                    # Create component definition
                    create_defaults = {
                        "tag": tag,
                        "name": name_description,
                        "description": name_description,
                        "input": getattr(cls, "schema", {}),
                        "ui_schema": getattr(cls, "ui_schema", None),
                        "type": type,
                    }
                    WorkflowComponentDefinition.objects.create(**create_defaults)
                elif resync:
                    # Update component definition if resync is allowed
                    defaults = {
                        "input": getattr(cls, "schema", {}),
                        "ui_schema": getattr(cls, "ui_schema", None),
                        "type": type,
                    }
                    component_definition_in_db.__dict__.update(defaults)
                    component_definition_in_db.save()
            except Exception as err:
                self.stderr.write(f"{err}")
                self.stderr.write(f"Import failed for {cls.tag}")

        self.stdout.write(self.style.SUCCESS("Components imported successfully"))
