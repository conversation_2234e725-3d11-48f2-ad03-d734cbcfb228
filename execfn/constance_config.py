# Default archive settings for internal message categories

from datetime import date

AUTO_ARCHIVE_DEFAULTS = {
    "first_time_sender_overlay": 60 * 24,
    "digest": 60 * 24,
    "first_time_sender_overlay_if_read": 60,
    "lucene_alert": 60 * 24,
}

ARCHIVING_EMAILZAP_DEFAULTS = {
    "remove_archive_after": 10080,  # Remove archive label 7 days after received time
    "remove_inbox_after": 120,  # Remove inbox label 120 minutes after received time
    "remove_inbox_after_if_read": 5,  # Remove inbox label 5 minutes after read time
}

CONSTANCE_ADDITIONAL_FIELDS = {
    "dict_field": ["django.forms.fields.JSONField", {"widget": "django.forms.Textarea"}],
}

# Include Constance settings and backend configuration
CONSTANCE_CONFIG = {
    "INTENT_PROMPT_TEMPLATE": ("", "This is prompt for intent", str),
    "ENTITY_PROMPT_TEMPLATE": ("", "This is prompt for entity", str),
    "ASSISTANT_TIMEOUT_CUTOFF": (40, "This is the max cutoff for assistant timeout", int),
    "ASSISTANT_POLLING_RETRY_INTERVAL": (5, "This is the interval for assistant run status retrieval", int),
    "AUTO_ARCHIVE_DEFAULTS": (
        AUTO_ARCHIVE_DEFAULTS,
        "Default internal messages archival time in minutes",
        "dict_field",
    ),
    "NON_ARCHIVE_LUCENE_TAGS": (
        ["CalendarInvite", "OrderTicket", "PaymentAlert", "BankStatement"],
        "Lucene category tags for which to skip archiving and sending alerts",
        "dict_field",
    ),
    "SENT_MESSAGE_FILTER_DOMAIN_THRESHOLD": (
        2,
        "Threshold to whitelist a domain when the user has sent emails to at least this many different senders",
        int,
    ),
    "PROCESS_EMAIL_FOR_DIGEST_GENERATION_INSTRUCTION": (
        "",
        "This is an assistant instruction for processing emails to generate digest",
        str,
    ),
    "GENERATE_DIGEST_HTML_EMAIL_INSTRUCTION": (
        "",
        "This is an assistant instruction to generate a digest html email",
        str,
    ),
    "FSO_EXEMPT_LIST": (
        [
            "noreply",
            "support",
            "newsletter",
            "info",
            "sales",
            "contact",
            "help",
            "feedback",
            "admin",
            "billing",
            "careers",
            "press",
            "team",
            "hello",
            "marketing",
        ],
        "This is an exemption list of email first parts, for which the user will not receive a first time sender "
        "overlay.",
        "dict_field",
    ),
    "PUSHER_UPDATE_STATISTICS_THRESHOLD": (
        10,
        "Threshold for updating statistics on client side",
        int,
    ),
    "ARCHIVING_EMAILZAP_SETTINGS": (
        ARCHIVING_EMAILZAP_DEFAULTS,
        "Default settings for archiving emails by emailzap",
        "dict_field",
    ),
    "TEST_USER_EMAILS": (
        [],
        "List of test environment users",
        "dict_field",
    ),
    "SES_SUPPORT_EMAIL": (
        "<EMAIL>",
        "Support email to receive support query emails",
        str,
    ),
    "NEW_UI_RELEASE_DATE": (
        date(2024, 10, 17),
        "Date on which latest UI is released",
    ),
    "PRICES_V2_DATE": (
        date(2024, 11, 7),
        "Date on which new pricing plans are released",
    ),
    "NEW_ONBOARDING_TOUR_RELEASE_DATE": (
        date(2024, 11, 15),
        "Date on which new onboarding tour is released",
    ),
    "PRICES_V3_DATE": (
        date(2024, 12, 17),
        "Date on which new pricing with discounted amounts are released",
    ),
    "PRICES_V4_DATE": (
        date(2025, 1, 15),
        "Date on freebie and discounts from new pricing are removed",
    ),
    "PRICES_V5_DATE": (
        date(2025, 2, 26),
        "Date when power_v3 pricing changes are introduced",
    ),
    "PRICES_V6_DATE": (
        date(2025, 3, 17),
        "Date when power_v4 pricing changes are introduced",
    ),
    "FIRST_TIME_SENDER_OVERLAY_THRESHOLD": (
        3,
        "Threshold for first time sender overlay filter ",
        int,
    ),
    "READ_FRACTION_FILTER_THRESHOLD": (
        4,
        "Threshold for read fraction filter",
        int,
    ),
}
