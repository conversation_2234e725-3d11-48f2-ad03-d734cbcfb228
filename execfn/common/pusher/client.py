import pusher
from django.conf import LazyObject
from django.conf import settings


class PusherClient(LazyObject):
    def _setup(self):
        self._wrapped = pusher.Pusher(
            app_id=settings.PUSHER_APP_ID,
            key=settings.PUSHER_KEY,
            secret=settings.PUSHER_SECRET,
            cluster=settings.PUSHER_CLUSTER,
            ssl=True,
        )


pusher_client = PusherClient()
