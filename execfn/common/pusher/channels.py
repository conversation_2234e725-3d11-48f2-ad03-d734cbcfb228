import logging
from abc import ABC
from typing import Any, Optional, Dict

from execfn.common.pusher.client import pusher_client

logger = logging.getLogger(__name__)


class BasePrivateChannel(ABC):
    name = None
    allowed_events = ()

    def get_channel_name(self, user_id: int) -> str:
        return f"private-{self.name}-{user_id}"

    def authenticate(self, user_id: int, socket_id: str) -> dict:
        return pusher_client.authenticate(
            channel=self.get_channel_name(user_id),
            socket_id=socket_id,
            custom_data={
                "user_id": user_id,
            },
        )

    def should_trigger_event(self, event_name, data: Optional[Dict[str, Any]] = None, **kwargs) -> bool:
        return True

    def trigger(self, user_id: int, event_name, data: Optional[Dict[str, Any]] = None, **kwargs) -> None:
        """
        Trigger an event on the channel for the user with the given data.
        If data is None, the event will be equivalent to a signal that the event occurred.

        Args:
            user_id (int): User ID
            event_name (Enum): Event name
            data (Optional[Dict[str, Any]], optional): Event data. Defaults to None.
        """
        if event_name in self.allowed_events:
            if self.should_trigger_event(event_name, data, **kwargs):
                try:
                    pusher_client.trigger(
                        channels=[self.get_channel_name(user_id)],
                        event_name=event_name.value,
                        data=data,
                    )
                except Exception:
                    logger.exception(
                        "Failed to trigger pusher event due to unhandled error",
                        extra={"event": event_name.value, "channel": self.get_channel_name(user_id)},
                    )
                else:
                    logger.info(f"Triggered event {event_name.value} for channel {self.get_channel_name(user_id)}")
            else:
                logger.info(
                    f"Trigger condition not met for event {event_name.value} for channel {self.get_channel_name(user_id)}"
                )
        else:
            logger.info(f"Event {event_name.value} is not allowed for channel {self.get_channel_name(user_id)}")
