import logging

import gspread
from django.conf import settings
from google.oauth2.credentials import Credentials

logger = logging.getLogger(__name__)


class GoogleSheetManager:
    """A class to manage Google Sheets operations.

    This class provides methods to create, read, update, and delete Google Sheets
    and their contents using the gspread library.

    Attributes:
        client: An authorized gspread client.
    """

    def __init__(self, access_token, refresh_token):
        """Initialize the GoogleSheetManager with OAuth2 credentials.

        Args:
            access_token (str): The OAuth2 access token.
            refresh_token (str): The OAuth2 refresh token.

        Raises:
            ValueError: If Google configuration is not found in settings.
        """
        client_id = settings.GOOGLE["client_id"]
        client_secret = settings.GOOGLE["client_secret"]
        token_uri = settings.GOOGLE["token_uri"]
        if not all([client_id, client_secret, token_uri]):
            raise ValueError("Google configuration not found in settings")
        creds = Credentials(
            token=access_token,
            refresh_token=refresh_token,
            token_uri=token_uri,
            client_id=client_id,
            client_secret=client_secret,
        )
        self.client = gspread.authorize(creds)

    def create_spreadsheet(self, title):
        """Create a new Google Spreadsheet.

        Args:
            title (str): The title of the new spreadsheet.

        Returns:
            str: The ID of the created spreadsheet.
        """
        spreadsheet = self.client.create(title)
        return spreadsheet.id

    def open_spreadsheet(self, spreadsheet_id):
        """Open an existing Google Spreadsheet by ID.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet to open.

        Returns:
            gspread.Spreadsheet: The opened spreadsheet object.
        """
        return self.client.open_by_key(spreadsheet_id)

    def read_worksheet(self, spreadsheet_id, worksheet_name):
        """Read all records from a specific worksheet.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet.
            worksheet_name (str): The name of the worksheet to read.

        Returns:
            list: A list of dictionaries containing the worksheet data.
        """
        spreadsheet = self.open_spreadsheet(spreadsheet_id)
        worksheet = spreadsheet.worksheet(worksheet_name)
        return worksheet.get_all_records()

    def update_cell(self, spreadsheet_id, worksheet_name, cell, value):
        """Update a specific cell in a worksheet.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet.
            worksheet_name (str): The name of the worksheet.
            cell (str): The cell to update (e.g., 'A1').
            value: The value to set in the cell.
        """
        spreadsheet = self.open_spreadsheet(spreadsheet_id)
        worksheet = spreadsheet.worksheet(worksheet_name)
        worksheet.update(cell, value)

    def append_row(self, spreadsheet_id, worksheet_name, row_data):
        """Append a row to a worksheet.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet.
            worksheet_name (str): The name of the worksheet.
            row_data (list): The data to append as a new row.
        """
        spreadsheet = self.open_spreadsheet(spreadsheet_id)
        worksheet = spreadsheet.worksheet(worksheet_name)
        worksheet.append_row(row_data)

    def create_worksheet(self, spreadsheet_id, title, rows=100, cols=26):
        """Create a new worksheet in an existing spreadsheet.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet.
            title (str): The title of the new worksheet.
            rows (int, optional): The number of rows. Defaults to 100.
            cols (int, optional): The number of columns. Defaults to 26.

        Returns:
            gspread.Worksheet: The created worksheet object.
        """
        spreadsheet = self.open_spreadsheet(spreadsheet_id)
        return spreadsheet.add_worksheet(title=title, rows=rows, cols=cols)

    def delete_worksheet(self, spreadsheet_id, worksheet_name):
        """Delete a worksheet from a spreadsheet.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet.
            worksheet_name (str): The name of the worksheet to delete.
        """
        spreadsheet = self.open_spreadsheet(spreadsheet_id)
        worksheet = spreadsheet.worksheet(worksheet_name)
        spreadsheet.del_worksheet(worksheet)

    def share_spreadsheet(self, spreadsheet_id, email, role="reader"):
        """Share a spreadsheet with a specific email address.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet to share.
            email (str): The email address to share the spreadsheet with.
            role (str, optional): The role to grant. Defaults to "reader".
        """
        spreadsheet = self.open_spreadsheet(spreadsheet_id)
        spreadsheet.share(email, perm_type="user", role=role)

    def write_dicts_to_sheet(self, spreadsheet_id, worksheet_name, data_dicts):
        """Write a list of dictionaries to a worksheet.

        If the worksheet doesn't exist, it will be created. If the worksheet is empty,
        headers will be written based on the keys of the first dictionary.

        Args:
            spreadsheet_id (str): The ID of the spreadsheet.
            worksheet_name (str): The name of the worksheet to write to.
            data_dicts (list): A list of dictionaries containing the data to write.

        Returns:
            gspread.Worksheet: The worksheet object that was written to.
        """
        spreadsheet = self.open_spreadsheet(spreadsheet_id)
        try:
            worksheet = spreadsheet.worksheet(worksheet_name)
        except gspread.WorksheetNotFound:
            worksheet = self.create_worksheet(spreadsheet_id, worksheet_name)
        if not data_dicts:
            logger.info("No data to write.")
            return worksheet
        headers = list(data_dicts[0].keys())
        # If the worksheet is empty, write headers
        if worksheet.get_all_values() == [[]]:
            worksheet.append_row(headers)
        data_rows = []
        for row_data in data_dicts:
            data_row = []
            for header in headers:
                header_value = row_data.get(header, "")
                if isinstance(header_value, (list, set, tuple)):
                    header_value = ", ".join(header_value)
                elif not isinstance(header_value, str):
                    header_value = str(header_value)
                data_row.append(header_value)
            data_rows.append(data_row)
        worksheet.append_rows(data_rows)
        return worksheet
