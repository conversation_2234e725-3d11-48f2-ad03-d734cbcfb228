import logging

import boto3
from botocore.exceptions import ClientError

logger = logging.getLogger(__name__)


def get_ssm_parameter(parameter_name):
    """
    Get value from SSM Parameter store
    Args:
        parameter_name: name of the parameter

    Returns:
        value of the parameter
    """
    # Create an SSM client
    ssm_client = boto3.client("ssm")

    # Get the parameter value
    response = ssm_client.get_parameter(
        Name=parameter_name, WithDecryption=True  # Set to True if the parameter is encrypted
    )

    # Extract the value from the response
    parameter_value = response["Parameter"]["Value"]

    return parameter_value


def get_secret_value(secret_name):
    secrets_manager_client = boto3.client("secretsmanager")
    try:
        get_secret_value_response = secrets_manager_client.get_secret_value(SecretId=secret_name)
    except ClientError as e:
        if e.response["Error"]["Code"] == "ResourceNotFoundException":
            logger.error("The requested secret was not found", extra={"secret_name": secret_name})
        elif e.response["Error"]["Code"] == "InvalidRequestException":
            logger.error("The request was invalid")
        elif e.response["Error"]["Code"] == "InvalidParameterException":
            logger.error("The request had invalid params")
        elif e.response["Error"]["Code"] == "DecryptionFailure":
            logger.error("The requested secret can't be decrypted using the provided KMS key")
        elif e.response["Error"]["Code"] == "InternalServiceError":
            logger.error("An error occurred on the server side")
    else:
        # Secrets Manager decrypts the secret value using the associated KMS CMK
        # Depending on whether the secret was a string or binary, only one of these fields will be populated
        if "SecretString" in get_secret_value_response:
            return get_secret_value_response["SecretString"]
        else:
            return get_secret_value_response["SecretBinary"]
