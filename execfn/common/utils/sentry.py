from enum import Enum


class SentryErrorTypes(Enum):
    ALERT = "alert"


def set_sentry_tags(event, hint):
    """
    Set sentry custom tags
    Args:
        event: sentry event
        hint: objects containing various information used to put together an event or a breadcrumb

    Returns:
        updated sentry event
    """
    tags = {
        "error_type": event.get("error_type"),
    }
    event.setdefault("tags", {}).update(tags)
    return event
