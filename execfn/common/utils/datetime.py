import datetime
from typing import Union

import pytz
from dateutil import parser
from django.utils import timezone

from applications.utils.email import logger

STANDARD_DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"


def get_utc_datetime(input_datetime: Union[datetime.datetime, str], time_zone: pytz.timezone):
    """
    Converts the datetime string to utc datetime object based on user's time zone
    Args:
        input_datetime: input date time which needs to be converted to utc
        time_zone: Time zone of the user

    Returns:
        Corresponding utc datetime object
    """
    if isinstance(input_datetime, str):
        try:
            input_datetime = parser.parse(input_datetime)
        except parser.ParserError:
            logger.exception("Failed to convert datetime to UTC")
            return None
    elif isinstance(input_datetime, datetime.datetime):
        pass  # do nothing since it's a valid dt object
    else:
        raise NotImplementedError("Invalid datetime value.")
    input_datetime = input_datetime.replace(tzinfo=None)
    localized_dt = time_zone.localize(input_datetime)
    utc_datetime = localized_dt.astimezone(pytz.utc)
    return utc_datetime


def round_to_next_12am(dt: timezone.datetime) -> timezone.datetime:
    """
    Round a timezone aware datetime object to the next 12 AM (midnight).

    Args:
    dt (timezone.datetime): The timezone aware datetime object to be rounded.

    Returns:
    timezone.datetime: The rounded timezone aware datetime object to the next 12 AM.
    """
    if dt.time() != timezone.datetime.min.time():
        dt = dt + timezone.timedelta(days=1)
    rounded_dt = dt.replace(hour=0, minute=0, second=0, microsecond=0)
    return rounded_dt
