import logging
import time
from contextlib import contextmanager

from django.core.cache import cache

logger = logging.getLogger(__name__)


@contextmanager
def cache_lock(key, lock_timeout=300, max_blocking_time=0, polling_interval=0.01):
    """
    A context manager that locks a key in the cache for the duration of the context.
    Args:
        key: The key to lock
        lock_timeout: The number of seconds to lock the key for
        max_blocking_time: The maximum number of seconds to block for while waiting for the lock to be released.
        polling_interval: The number of seconds to wait between attempts to acquire the lock.

    Yields:
        True if the lock was acquired, False if not.
    """
    timeout_at = time.monotonic() + lock_timeout
    # Add a key only if it does not already exist, using the add() method.
    # It takes the same parameters as set(), but it will not attempt to
    # update the cache if the key specified is already present.
    acquired = cache.add(key=key, value=True, timeout=lock_timeout)
    if not acquired and max_blocking_time:
        block_until = time.monotonic() + max_blocking_time
        while time.monotonic() + polling_interval <= block_until:
            logger.info(f"Failed to acquire cache lock for {key}. Retrying in {polling_interval} seconds.")
            time.sleep(polling_interval)
            acquired = cache.add(key=key, value=True, timeout=lock_timeout)
            if acquired:
                break
            else:
                # Increase the polling interval exponentially until it reaches 0.1 * lock_timeout
                polling_interval = min(polling_interval * 2, 0.1 * lock_timeout)
    if acquired:
        logger.info(f"Acquired lock on {key}")
    else:
        logger.info(f"Failed to acquire lock on {key}")
    try:
        yield acquired
    finally:
        if time.monotonic() < timeout_at and acquired:
            # If the lock has not timed out, release it
            logger.info(f"Released lock on {key}")
            cache.delete(key=key)
