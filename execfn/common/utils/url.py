import urllib.parse as urlparse
from typing import Dict, Any
from urllib.parse import urlenco<PERSON>


def append_query_params_to_url(url: str, query_params: Dict[str, Any]) -> str:
    """
    Given a set of query params, append them to a URL
    Args:
        url: url link
        query_params: query params to append

    Returns:
        updated url
    """
    url_parts = list(urlparse.urlparse(url))
    query = dict(urlparse.parse_qsl(url_parts[4]))
    query.update(query_params)
    url_parts[4] = urlencode(query)
    return urlparse.urlunparse(url_parts)
