import logging

from django.conf import settings
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

logger = logging.getLogger(__name__)


def send_slack_message(text: str, channel_id: str):
    """
    Send a message to a Slack channel

    Args:
        text (str): The message to send
        channel_id (str): The Slack channel id to send the message to
    """
    client = WebClient(token=settings.SLACK_OAUTH_TOKEN)
    message = {"channel": channel_id, "text": text}
    try:
        client.chat_postMessage(**message)
    except SlackApiError:
        logger.exception("Error posting message to Slack channel")
    else:
        logger.info(f"Message sent to slack channel {channel_id}")
