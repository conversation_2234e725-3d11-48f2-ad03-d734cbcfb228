from cryptography.fernet import Fernet

from execfn.common.utils.aws import get_secret_value


def get_fernet_key_value(fernet_key: str, fetch_key_from_secret_manager: bool = False):
    """
    Get the fernet key value from the cache or secret manager

    Args:
        fernet_key (str): The fernet key
        fetch_key_from_secret_manager (bool): Whether to fetch the key from the secret manager or not
    """
    if fetch_key_from_secret_manager:
        return get_secret_value(fernet_key)
    else:
        return fernet_key


def decrypt_secret(fernet_key: str, secret, fetch_key_from_secret_manager: bool = False) -> str:
    """
    Decrypt the secret using the fernet key

    Args:
        fernet_key (str): The fernet key
        secret (str): The secret to decrypt
        fetch_key_from_secret_manager (bool): Whether to fetch the key from the secret manager or not
    """
    if not secret:
        return ""
    cipher = Fernet(key=get_fernet_key_value(fernet_key, fetch_key_from_secret_manager))
    if isinstance(secret, memoryview):
        secret = secret.tobytes()
    return cipher.decrypt(secret).decode()


def encrypt_secret(fernet_key: str, secret: str, fetch_key_from_secret_manager: bool = False):
    """
    Encrypt the secret using the fernet key

    Args:
        fernet_key (str): The fernet key
        secret (str): The secret to encrypt
        fetch_key_from_secret_manager (bool): Whether to fetch the key from the secret manager or not
    """
    if not secret:
        return b""
    cipher = Fernet(key=get_fernet_key_value(fernet_key, fetch_key_from_secret_manager))
    return cipher.encrypt(secret.encode())
