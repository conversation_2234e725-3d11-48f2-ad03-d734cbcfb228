import json

from django.conf import settings
from django.test import RequestFactory
from django.urls import resolve


def resolve_and_fetch_view_data(user, url_path, query_params=None):
    """
    Resolve the view and fetch the data from the view

    Args:
        user: User object
        url_path: URL path to resolve
    """
    request_factory = RequestFactory(user=user)
    request = request_factory.get(url_path)
    request.user = user
    request.META["SERVER_NAME"] = settings.BACKEND_DOMAIN
    if query_params:
        request.GET = query_params
    view, args, kwargs = resolve(url_path)
    kwargs["request"] = request
    response = view(*args, **kwargs)
    return json.loads(response.render().content)
