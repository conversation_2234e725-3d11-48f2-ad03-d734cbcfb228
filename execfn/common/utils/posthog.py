from posthog import Client
from typing import Optional
import os
import logging
from django.contrib.auth.models import User

logger = logging.getLogger(__name__)

class PostHogClient:
    """
    A utility class to initialize the PostHog analytics client.
    """
    client: Optional[Client] = None

    def __init__(self):
        posthog_api_key = os.environ.get('POSTHOG_API_KEY')
        posthog_host = os.environ.get('POSTHOG_HOST')

        if posthog_api_key:
            self.client = Client(project_api_key=posthog_api_key, host=posthog_host)
            self.client.sync_mode = True
            logger.info(f"PostHog client initialized")
        else:
            self.client = None
            logger.error("POSTHOG_API_KEY environment variable not found. PostHog client not initialized.")

_posthog_client_instance = PostHogClient()

def get_posthog_client() -> Optional[Client]:
    """Retrieves the singleton instance of the PostHog client.

    This function provides access to the initialized PostHog client, which is
    created and managed by the PostHogClient class. If the client was not
    initialized successfully (e.g., due to a missing API key environment variable),
    this function will return None.

    Returns:
        Optional[Client]: The initialized PostHog Client instance, or None if
                          initialization failed.
    """
    return _posthog_client_instance.client
