import logging

import boto3
from botocore.exceptions import Client<PERSON>rror
from django.conf import settings

logger = logging.getLogger(__name__)


class S3Manager:
    def __init__(self):
        self.s3_client = boto3.client("s3")
        self.region_name = settings.AWS_DEFAULT_REGION

    def upload_memory_file(self, memory_file, bucket, object_name):
        """
        Upload an in-memory file to an S3 bucket and return its URL

        Args:
            memory_file: in memory uploaded file
            bucket: bucket to which the file needs to be uploaded
            object_name: uploaded object name

        Returns:
            object URL
        """
        try:
            self.s3_client.upload_fileobj(memory_file, bucket, object_name)
            logger.info(f"File {memory_file.name} uploaded to {bucket}/{object_name}")
            return self.generate_signed_url(bucket, object_name)
        except ClientError as e:
            logging.error(e)
            return None

    def generate_signed_url(self, bucket, object_name, expiration=604800):
        """
        Generate a signed URL for an S3 object

        Args:
            bucket: bucket to which the object needs to be uploaded
            object_name: uploaded object name
            expiration: expiration time in seconds

        Returns:
            signed URL
        """
        try:
            response = self.s3_client.generate_presigned_url(
                "get_object", Params={"Bucket": bucket, "Key": object_name}, ExpiresIn=expiration
            )
            return response
        except ClientError as e:
            logging.error(e)
            return None
