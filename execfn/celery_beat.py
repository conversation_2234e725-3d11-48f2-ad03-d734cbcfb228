from celery.schedules import crontab

CELERY_BEAT_SCHEDULE = {
    "refresh-watch-channel": {
        "task": "mailbot.tasks.refresh_watch_channel",
        "schedule": crontab(minute="0", hour="4"),
    },
    "schedule-mailbot-digests": {"task": "mailbot.tasks.schedule_mailbot_digests", "schedule": crontab(minute="0,30")},
    "schedule-secondary-profile-mailbot-digests": {
        "task": "mailbot.tasks.schedule_secondary_profile_mailbot_digests",
        "schedule": crontab(minute="0"),
    },
    "mailbot-health-check": {
        "task": "mailbot.tasks.mailbot_health_check",
        "schedule": crontab(minute="0", hour="0,12"),
    },
    "cleanup-expired-tasks": {
        "task": "applications.tasks.cleanup_expired_tasks",
        "schedule": crontab(minute="30", hour="4"),
    },
    "refresh-calendar-watches": {
        "task": "calendarbot.tasks.refresh_calendar_watches_task",
        "schedule": crontab(minute="0", hour="3"),
    },
}
