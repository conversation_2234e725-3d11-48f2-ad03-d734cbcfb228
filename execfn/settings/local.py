from . import APP_ENV_LOCAL
from . import APP_ENV_LOCAL
from .base import *  # noqa

APP_ENV = APP_ENV_LOCAL
ALLOWED_HOSTS = ["localhost", "127.0.0.1"]

DEBUG = True

BACKEND_BASE_URL = os.environ.get("LOCAL_TUNNEL_BACKEND_URL")
if BACKEND_BASE_URL:
    BACKEND_DOMAIN = BACKEND_BASE_URL.split("//")[1]
    ALLOWED_HOSTS.append(BACKEND_DOMAIN)
else:
    BACKEND_DOMAIN = "localhost"
    BACKEND_BASE_URL = f"http://{BACKEND_DOMAIN}:8000"

# Database Configuration
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "execfn",
        "USER": "execfn_user",
        "PASSWORD": os.environ.get("DJANGO_DB_PASSWORD"),
        "HOST": "localhost",
        "PORT": "5432",
    }
}

# FRONTEND
FRONTEND_DOMAIN = "localhost"
FRONTEND_BASE_URL = f"http://{FRONTEND_DOMAIN}:4000"
COOKIE_DOMAIN = "localhost"

# CORS
CORS_ALLOWED_ORIGINS = ["http://localhost:4000"]

# CSRF
CSRF_TRUSTED_ORIGINS = ["http://localhost:4000"]

# Celery
CELERY_BROKER_URL = "redis://localhost:6379"
CELERY_TASK_ALWAYS_EAGER = False  # Set TRUE if you want to run tasks in sync

# Microsoft Authentication
MICROSOFT = {
    "app_id": os.environ.get("MICROSOFT_AUTH_CLIENT_ID"),
    "app_secret": os.environ.get("MICROSOFT_AUTH_CLIENT_SECRET"),
    "client_state": os.environ.get("MICROSOFT_CLIENT_STATE"),
}

# Google Authentication
GOOGLE = {
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "client_id": os.environ.get("GOOGLE_CLIENT_ID"),
    "client_secret": os.environ.get("GOOGLE_CLIENT_SECRET"),
    # TODO : We're not using this anywhere. Check with Ritik if it can be removed
    "client_state": os.environ.get("GOOGLE_CLIENT_STATE"),
}

# Jarvis Auth
JARVIS_GOOGLE_AUTH = {
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "client_id": os.environ.get("JARVIS_GOOGLE_CLIENT_ID"),
    "client_secret": os.environ.get("JARVIS_GOOGLE_CLIENT_SECRET"),
    "redirect_url": f"{FRONTEND_BASE_URL}/workflows",
}

# Google Calendar Authentication
GOOGLE_CALENDAR_AUTH = {
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "client_id": os.environ.get("GOOGLE_CALENDAR_CLIENT_ID"),
    "client_secret": os.environ.get("GOOGLE_CALENDAR_CLIENT_SECRET"),
    "redirect_url": f"{FRONTEND_BASE_URL}/calendar-settings",
}

# Email
SES_FROM_NAME = "EmailZap (local)"
SES_FROM_EMAIL = "<EMAIL>"
