import sentry_sdk
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.django import DjangoIntegration

from . import APP_ENV_DEV
from .base import *  # noqa
from ..common.utils.sentry import set_sentry_tags

APP_ENV = APP_ENV_DEV
# BACKEND
BACKEND_DOMAIN = "api.dev.emailzap.co"
BACKEND_BASE_URL = f"https://{BACKEND_DOMAIN}"

ALLOWED_HOSTS = [
    "************",
    BACKEND_DOMAIN,
    f"www.{BACKEND_DOMAIN}",
]

DEBUG = True

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "execfn",
        "USER": "execfn_user",
        "PASSWORD": os.environ.get("DJANGO_DB_PASSWORD"),
        "HOST": "execfn-dev.cny2hpdrocok.us-east-1.rds.amazonaws.com",
        "PORT": "5432",
    }
}

# FRONTEND
FRONTEND_DOMAIN = "dev.emailzap.co"
FRONTEND_BASE_URL = f"https://{FRONTEND_DOMAIN}"
COOKIE_DOMAIN = ".emailzap.co"

# CORS
CORS_ALLOWED_ORIGINS = [f"https://{FRONTEND_DOMAIN}"]

# CSRF
CSRF_TRUSTED_ORIGINS = [f"https://{FRONTEND_DOMAIN}"]

sentry_sdk.init(
    dsn="https://<EMAIL>/4506115400138752",
    traces_sample_rate=1.0,
    before_send=set_sentry_tags,
    profiles_sample_rate=1.0,
    environment=APP_ENV,
    integrations=[DjangoIntegration(), CeleryIntegration()],
)

# Celery
CELERY_BROKER_URL = "sqs://"
CELERY_BROKER_TRANSPORT_OPTIONS = {
    "predefined_queues": {"celery": {"url": "https://sqs.us-east-1.amazonaws.com/************/execfn-dev"}}
}

# Microsoft Authentication
MICROSOFT = {
    "app_id": os.environ.get("MICROSOFT_AUTH_CLIENT_ID"),
    "app_secret": os.environ.get("MICROSOFT_AUTH_CLIENT_SECRET"),
    "client_state": os.environ.get("MICROSOFT_CLIENT_STATE"),
}

# Google Authentication
GOOGLE = {
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "client_id": os.environ.get("GOOGLE_CLIENT_ID"),
    "client_secret": os.environ.get("GOOGLE_CLIENT_SECRET"),
    "client_state": os.environ.get("GOOGLE_CLIENT_STATE"),
}

# Google Calendar Authentication
GOOGLE_CALENDAR_AUTH = {
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "client_id": os.environ.get("GOOGLE_CALENDAR_CLIENT_ID"),
    "client_secret": os.environ.get("GOOGLE_CALENDAR_CLIENT_SECRET"),
    "redirect_url": f"{FRONTEND_BASE_URL}/calendar-settings",
}

# Jarvis Auth
JARVIS_GOOGLE_AUTH = {
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "client_id": os.environ.get("JARVIS_GOOGLE_CLIENT_ID"),
    "client_secret": os.environ.get("JARVIS_GOOGLE_CLIENT_SECRET"),
    "redirect_url": f"{FRONTEND_BASE_URL}/workflows",
}

# Email
EMAIL_BACKEND = "django_ses.SESBackend"
USE_SES_V2 = True
SES_FROM_EMAIL = "<EMAIL>"
SES_FROM_NAME = "EmailZap (dev)"
AWS_SES_CONFIGURATION_SET = "execfn-backend-dev"

# SESSION COOKIE
SESSION_COOKIE_DOMAIN = COOKIE_DOMAIN
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_SAMESITE = "None"

# CSRF COOKIE
CSRF_COOKIE_DOMAIN = COOKIE_DOMAIN
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_SAMESITE = "None"

# Using S3 storage with django-storages
DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"

# S3 bucket for storing invoices
AWS_STORAGE_BUCKET_NAME = "execfn-media-dev"
