"""
Django settings for execfn project.

Generated by 'django-admin startproject' using Django 4.2.5.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import os
from pathlib import Path

import stripe
from corsheaders.defaults import default_headers
from dotenv import load_dotenv

import execfn.logging.config
from execfn.celery_beat import CELERY_BEAT_SCHEDULE
from execfn.constance_config import CONSTANCE_CONFIG, CONSTANCE_ADDITIONAL_FIELDS

load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-mp7ar(&5p9+o0-m%g)_3z7ao_4y3$ln5@5hlsf+w$q9x3gmt3v"

ALLOWED_HOSTS = []

# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_extensions",
    "django_jsonform",
    "django_structlog",
    "django_filters",
    "constance",
    "accounts",
    "applications",
    "nlp",
    "jarvis",
    "corsheaders",
    "rest_framework",
    "django_celery_beat",
    "mailbot",
    "calendarbot",
    "storages",
    "webpush",
    "payments",
    "automation",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django_structlog.middlewares.RequestMiddleware",
    "mailbot.middlewares.FeatureFlagMiddleware",
]

ROOT_URLCONF = "execfn.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "execfn.wsgi.application"

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

AUTH_USER_MODEL = "accounts.User"

# Rest Framework
REST_FRAMEWORK = {
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
}


# STRIPE
STRIPE_API_KEY = os.environ.get("STRIPE_API_KEY")
STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE = os.environ.get("STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE")
STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE_PRICING_V4 = os.environ.get(
    "STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE_PRICING_V4"
)
STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE_PRICING_V5 = os.environ.get(
    "STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE_PRICING_V5"
)
STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE_PRICING_V6 = os.environ.get(
    "STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE_PRICING_V6"
)
stripe.api_key = STRIPE_API_KEY

# STATIC ROOT : TODO Replace this with S3 storages
PROJECT_DIR = os.path.dirname(BASE_DIR)
STATIC_ROOT = os.path.join(PROJECT_DIR, "static")

# OpenAI API
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
OPENAI_MODEL_NAME = "gpt-3.5-turbo-0125"

# CORS
CORS_ALLOW_HEADERS = (
    *default_headers,
    "Stripe-Signature" "X-Application",
)

CORS_ALLOW_CREDENTIALS = True
# Launch Darkly
MAILBOT_LD_SECRET_KEY = os.environ.get("MAILBOT_LD_SECRET_KEY")
CALENDARBOT_LD_SECRET_KEY = os.environ.get("CALENDARBOT_LD_SECRET_KEY")
REMINDERBOT_LD_SECRET_KEY = os.environ.get("REMINDERBOT_LD_SECRET_KEY")
STORAGEBOT_LD_SECRET_KEY = os.environ.get("STORAGEBOT_LD_SECRET_KEY")
LAUNCH_DARKLY_WEBHOOK_SECRET = os.environ.get("LAUNCH_DARKLY_WEBHOOK_SECRET")

# Cookies
COOKIE_MAX_AGE_DAYS = 365

# Microsoft
BASE_MICROSOFT_GRAPH_URL = "https://graph.microsoft.com/v1.0"
BASE_MICROSOFT_GRAPH_BETA_URL = "https://graph.microsoft.com/beta"

# Google Pub/Sub
GOOGLE_PUSH_SERVER_TOPIC_NAME = os.environ.get("GOOGLE_PUSH_SERVER_TOPIC_NAME")
GOOGLE_PUSH_SERVER_SUBSCRIPTION_NAME = os.environ.get("GOOGLE_PUSH_SERVER_SUBSCRIPTION_NAME")
PUBSUB_VERIFICATION_TOKEN = os.environ.get("PUBSUB_VERIFICATION_TOKEN")

# Google Service Account
GOOGLE_SERVICE_ACCOUNT = {
    "client_email": os.environ.get("GOOGLE_SERVICE_ACCOUNT_CLIENT_EMAIL"),
    "private_key_id": os.environ.get("GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY_ID"),
    "private_key": os.environ.get("GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY"),
    "client_id": os.environ.get("GOOGLE_SERVICE_CLIENT_ID"),
}

# Mailbot
GENERAL_EMAIL_DOMAINS = [
    "gmail.com",
    "yahoo.com",
    "hotmail.com",
    "outlook.com",
]
EMAIL_DOMAIN_WHITELIST = ["execfn.com", "execfxn.com", "emailzap.co", "dev.emailzap.co"]

# Logging
# LOGGING_CONFIG is defined name hence cannot direct import
LOGGING = execfn.logging.config.LOGGING_CONFIG
DJANGO_STRUCTLOG_CELERY_ENABLED = True

# Service Providers
SERVICE_PROVIDER_MICROSOFT = "microsoft"
SERVICE_PROVIDER_GOOGLE = "google"

CONSTANCE_ADDITIONAL_FIELDS = CONSTANCE_ADDITIONAL_FIELDS
CONSTANCE_CONFIG = CONSTANCE_CONFIG

CONSTANCE_BACKEND = "constance.backends.database.DatabaseBackend"

# Celery Beat
CELERY_BEAT_SCHEDULE = CELERY_BEAT_SCHEDULE
CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"

# Cryptography
FERNET_KEY = os.environ.get("FERNET_KEY")
JWT_KEY = os.environ.get("JWT_KEY")
AWS_STRIPE_FERNET_KEY_NAME = os.environ.get("AWS_STRIPE_FERNET_KEY_NAME")
AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME = os.environ.get("AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME")

# AMP
AMP_ALLOWED_ORIGINS = ["https://mail.google.com"]

# Cache
CACHES = {"default": {"BACKEND": "django.core.cache.backends.memcached.PyMemcacheCache", "LOCATION": "127.0.0.1:11211"}}

WEBPUSH_SETTINGS = {
    "VAPID_PUBLIC_KEY`": os.environ.get("VAPID_PUBLIC_KEY"),
    "VAPID_PRIVATE_KEY": os.environ.get("VAPID_PRIVATE_KEY"),
    "VAPID_ADMIN_EMAIL": os.environ.get("VAPID_ADMIN_EMAIL"),
}

# Pusher
PUSHER_APP_ID = os.environ.get("PUSHER_APP_ID")
PUSHER_KEY = os.environ.get("PUSHER_KEY")
PUSHER_SECRET = os.environ.get("PUSHER_SECRET")
PUSHER_CLUSTER = os.environ.get("PUSHER_CLUSTER")

# Slack
SLACK_OAUTH_TOKEN = os.environ.get("SLACK_OAUTH_TOKEN")

# AI Bot
AI_BOT_EMAIL = "<EMAIL>"
AI_BOT_IDENTIFIERS = ["@ai", "@execfn", "@efn", "@efn-bot"]

# AWS
AWS_DEFAULT_REGION = os.environ.get("AWS_DEFAULT_REGION")
AWS_STORAGE_BUCKET_NAME = "execfn-media-dev"

# Gemini
GOOGLE_GEMINI_API_KEY = os.environ.get("GOOGLE_API_KEY")
