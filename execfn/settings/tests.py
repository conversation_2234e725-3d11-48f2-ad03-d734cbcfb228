"""
The file has been intentionally named as `tests` (plural) to avoid conflicts with the pytest autodiscovery
"""
from . import APP_ENV_TEST
from .base import *

APP_ENV = APP_ENV_TEST

# BACKEND
BACKEND_DOMAIN = "api.test.emailzap.co"
BACKEND_BASE_URL = f"https://{BACKEND_DOMAIN}"  # noqa (Duplicate line of code)

# Database Configuration
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "execfn_test",
        "USER": "execfn_test_user",
        "PASSWORD": "1234567890",
        "HOST": "localhost",
        "PORT": "5432",
    }
}

# FRONTEND
FRONTEND_DOMAIN = "localhost"
FRONTEND_BASE_URL = f"http://{FRONTEND_DOMAIN}:4000"
COOKIE_DOMAIN = "localhost"

# CORS
CORS_ALLOWED_ORIGINS = ["http://localhost:4000"]

# CSRF
CSRF_TRUSTED_ORIGINS = ["http://localhost:4000"]

# Celery
CELERY_BROKER_URL = "redis://localhost:6379"

# Email
SES_FROM_NAME = "EmailZap (test)"
SES_FROM_EMAIL = "<EMAIL>"
