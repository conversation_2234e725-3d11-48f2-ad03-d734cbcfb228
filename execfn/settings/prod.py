import sentry_sdk
from celery.schedules import crontab
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.django import DjangoIntegration

from . import APP_ENV_PROD
from .base import *  # noqa
from ..common.utils.sentry import set_sentry_tags

SECRET_KEY = os.environ.get("DJANGO_SECRET_KEY")

APP_ENV = APP_ENV_PROD
# BACKEND
BACKEND_DOMAIN = "api.app.emailzap.co"
BACKEND_BASE_URL = f"https://{BACKEND_DOMAIN}"

ALLOWED_HOSTS = [
    "**************",
    BACKEND_DOMAIN,
    f"www.{BACKEND_DOMAIN}",
]

DEBUG = False

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "execfn",
        "USER": "execfn_user",
        "PASSWORD": os.environ.get("DJANGO_DB_PASSWORD"),
        "HOST": "execfn-prod.cny2hpdrocok.us-east-1.rds.amazonaws.com",
        "PORT": "5432",
    }
}

# FRONTEND
FRONTEND_DOMAIN = "app.emailzap.co"
FRONTEND_BASE_URL = f"https://{FRONTEND_DOMAIN}"
COOKIE_DOMAIN = ".emailzap.co"

# CORS
CORS_ALLOWED_ORIGINS = [f"https://{FRONTEND_DOMAIN}"]

# CSRF
CSRF_TRUSTED_ORIGINS = [f"https://{FRONTEND_DOMAIN}"]

# TODO : Move sentry initialisation to a single place
sentry_sdk.init(
    dsn="https://<EMAIL>/4506115400138752",
    before_send=set_sentry_tags,
    traces_sample_rate=1.0,
    profiles_sample_rate=1.0,
    environment=APP_ENV,
    integrations=[DjangoIntegration(), CeleryIntegration()],
)

# Celery
CELERY_BROKER_URL = "sqs://"
CELERY_BROKER_TRANSPORT_OPTIONS = {
    "predefined_queues": {
        "celery": {"url": "https://sqs.us-east-1.amazonaws.com/209541061695/execfn-prod"},
        "celery_long_running": {"url": "https://sqs.us-east-1.amazonaws.com/209541061695/execfn-prod-long-running"},
    }
}

# Microsoft Authentication
MICROSOFT = {
    "app_id": os.environ.get("MICROSOFT_AUTH_CLIENT_ID"),
    "app_secret": os.environ.get("MICROSOFT_AUTH_CLIENT_SECRET"),
    "client_state": os.environ.get("MICROSOFT_CLIENT_STATE"),
}

# Email
EMAIL_BACKEND = "django_ses.SESBackend"
USE_SES_V2 = True
SES_FROM_EMAIL = "<EMAIL>"
SES_FROM_NAME = "EmailZap"
AWS_SES_CONFIGURATION_SET = "execfn-backend-prod"

# SESSION COOKIE
SESSION_COOKIE_DOMAIN = COOKIE_DOMAIN
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_SAMESITE = "None"

# CSRF COOKIE
CSRF_COOKIE_DOMAIN = COOKIE_DOMAIN
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_SAMESITE = "None"

# Using S3 storage with django-storages
DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"

# S3 bucket for storing invoices
AWS_STORAGE_BUCKET_NAME = "execfn-media-prod"

# Google Authentication
GOOGLE = {
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "client_id": os.environ.get("GOOGLE_CLIENT_ID"),
    "client_secret": os.environ.get("GOOGLE_CLIENT_SECRET"),
    "client_state": os.environ.get("GOOGLE_CLIENT_STATE"),
}

# Jarvis Auth
JARVIS_GOOGLE_AUTH = {
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "client_id": os.environ.get("JARVIS_GOOGLE_CLIENT_ID"),
    "client_secret": os.environ.get("JARVIS_GOOGLE_CLIENT_SECRET"),
    "redirect_url": f"{FRONTEND_BASE_URL}/workflows",
}

CELERY_BEAT_SCHEDULE.update(
    {
        "upload-google-ads-click-conversion": {
            "task": "mailbot.tasks.upload_google_ads_click_conversions",
            "schedule": crontab(minute="0", hour="*"),
        }
    }
)
