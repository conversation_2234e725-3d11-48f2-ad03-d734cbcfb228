"""
URL configuration for execfn project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import include, path

urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/v1/accounts/", include(("accounts.api.urls", "accounts"), namespace="accounts")),
    path("api/v1/applications/", include(("applications.api.urls", "applications"), namespace="applications")),
    path("api/v1/mailbot/", include(("mailbot.api.urls", "mailbot"), namespace="mailbot")),
    path("api/v1/jarvis/", include(("jarvis.api.urls", "jarvis"), namespace="jarvis")),
    path("api/v1/webpush/", include(("webpush.urls", "webpush"), namespace="webpush")),
    path("api/v1/payments/", include(("payments.api.urls", "payments"), namespace="payments")),
    path("api/v1/automation/", include(("automation.api.urls", "automation"), namespace="test-automation")),
    path("api/v1/calendarbot/", include(("calendarbot.api.urls", "calendarbot"), namespace="calendarbot")),
]
