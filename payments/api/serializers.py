from django.utils import timezone
from rest_framework import serializers

from payments.models import StripePrice, StripeSubscription, StripeInvoice, StripeCoupon


class StripeCouponSerializer(serializers.ModelSerializer):
    class Meta:
        model = StripeCoupon
        fields = (
            "id",
            "name",
            "percent_off",
            "amount_off",
            "currency",
            "duration",
            "duration_in_months",
        )


class StripePriceSerializer(serializers.ModelSerializer):
    class Meta:
        model = StripePrice
        fields = (
            "id",
            "product",
            "nickname",
            "currency",
            "unit_amount",
            "interval",
            "interval_count",
            "metadata",
        )


class StripeSubscriptionSerializer(serializers.ModelSerializer):
    price = StripePriceSerializer()
    expired = serializers.SerializerMethodField()
    phases = serializers.ReadOnlyField()

    class Meta:
        model = StripeSubscription
        fields = (
            "id",
            "customer",
            "price",
            "product",
            "cancel_at_period_end",
            "cancel_at",
            "current_period_start",
            "current_period_end",
            "status",
            "metadata",
            "expired",
            "phases",
        )

    def get_expired(self, obj):
        return obj.current_period_end < timezone.now()


class StripeInvoiceSerializer(serializers.ModelSerializer):
    coupon = serializers.SerializerMethodField()
    price = serializers.SerializerMethodField()

    class Meta:
        model = StripeInvoice
        fields = (
            "id",
            "created",
            "coupon",
            "amount_paid",
            "price",
        )

    def get_coupon(self, obj):
        if discount := obj.stripe_data.get("discount"):
            return discount["coupon"]["id"]
        else:
            return None

    def get_price(self, obj):
        return obj.stripe_data["lines"]["data"][0]["price"]["id"]
