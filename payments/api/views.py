import logging

import stripe
from django.db.models import OuterRef, Subquery
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ReadOnlyModelViewSet, GenericViewSet, ViewSet

from applications.api.serializers import CheckoutSessionSerializer
from mailbot.models import SecondaryMailBotProfilesThrough
from mailbot.utils.pricing import get_pricing_version
from payments.api.serializers import StripeSubscriptionSerializer, StripeInvoiceSerializer
from payments.models import (
    StripeSubscriptionSchedule,
    StripeCustomer,
    StripeInvoice,
    StripeSubscription,
    StripeWebhookEndpoint,
    StripeWebhookEvent,
)
from payments.tasks import process_application_specific_webhook, process_customer_specific_webhook
from payments.utils.session import create_customer_portal_session, create_update_subscription_deep_link_session

logger = logging.getLogger(__name__)


class Webhook(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request, uuid):
        raw_payload = request.body
        sig_header = request.headers.get("Stripe-Signature")
        if not sig_header:
            return Response("Signature header is missing", status=status.HTTP_400_BAD_REQUEST)
        webhook_endpoint = get_object_or_404(StripeWebhookEndpoint, uuid=uuid)
        try:
            event = stripe.Webhook.construct_event(raw_payload, sig_header, webhook_endpoint.secret)
        except ValueError:
            return Response("Invalid payload", status=status.HTTP_400_BAD_REQUEST)
        except stripe.error.SignatureVerificationError:
            return Response("Invalid signature", status=status.HTTP_400_BAD_REQUEST)
        else:
            if event.type in webhook_endpoint.enabled_events or webhook_endpoint.enabled_events == ["*"]:
                webhook_event, created = StripeWebhookEvent.objects.get_or_create(
                    id=event.id,
                    defaults={
                        "stripe_data": event.data,
                        "endpoint": webhook_endpoint,
                        "type": event.type,
                        "data": event.data.object,
                        "status": StripeWebhookEvent.EVENT_STATUS_SCHEDULED,
                    },
                )
                if created or webhook_event.status == StripeWebhookEvent.EVENT_STATUS_FAILED:
                    if webhook_event.type.startswith(("product.", "price.", "coupon.")):
                        # Application specific webhooks
                        process_application_specific_webhook.delay(webhook_event.id)
                    elif webhook_event.type.startswith(("subscription_schedule.", "customer.", "invoice.")):
                        # Customer specific webhooks
                        if webhook_event.type in ("customer.created", "customer.updated", "customer.deleted"):
                            customer_id = webhook_event.data["id"]
                        else:
                            customer_id = webhook_event.data["customer"]
                        process_customer_specific_webhook.delay(webhook_event.id, customer_id)
                    else:
                        logger.error(f"Event {webhook_event.id} is neither application specific nor customer specific")
                else:
                    logger.info(f"Webhook event {event.id} already exists")
                return Response("Event processed")
            else:
                return Response("Unsupported event type", status=status.HTTP_400_BAD_REQUEST)


class SubscriptionViewSet(GenericViewSet):
    serializer_class = StripeSubscriptionSerializer
    lookup_field = "id"

    def get_queryset(self):
        try:
            secondary_mailbot_profile = SecondaryMailBotProfilesThrough.objects.get(
                secondary_mailbot_profile=self.request.user.user_mailbot_profile
            )
        except SecondaryMailBotProfilesThrough.DoesNotExist:
            primary_user = self.request.user
        else:
            primary_user = secondary_mailbot_profile.primary_mailbot_profile.user
        try:
            schedule = StripeSubscriptionSchedule.objects.filter(
                subscription=OuterRef("id"), status=StripeSubscriptionSchedule.STATUS_ACTIVE
            )
            return StripeSubscription.objects.filter(
                customer_id=primary_user.stripe_customer.id,
                status__in=[StripeSubscription.STATUS_ACTIVE, StripeSubscription.STATUS_PAST_DUE],
            ).annotate(phases=Subquery(schedule.values("phases")[:1]))
        except StripeCustomer.DoesNotExist:
            return StripeSubscription.objects.none()

    @action(detail=False, methods=("GET",), url_path="latest")
    def latest_subscription(self, request):
        queryset = self.get_queryset()
        if queryset.exists():
            subscription = queryset.latest("created")
            serializer = self.get_serializer(subscription)
            return Response(serializer.data)
        else:
            return Response("No subscriptions found", status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=("POST",), url_path="cancel")
    def cancel_subscription(self, request, id):
        subscription: StripeSubscription = self.get_object()
        subscription.cancel(reason=request.data.get("reason"), reason_text=request.data.get("reason_text"))
        serializer = self.get_serializer(subscription)
        return Response(serializer.data)

    @action(detail=True, methods=("POST",), url_path="renew")
    def renew_subscription(self, request, id):
        subscription: StripeSubscription = self.get_object()
        subscription.renew()
        serializer = self.get_serializer(subscription)
        return Response(serializer.data)

    @action(detail=True, methods=("POST",), url_path="schedule-upgrade")
    def schedule_upgrade_subscription(self, request, id):
        """
        For pricing upgrades from monthly to annual where features provided remain same,
        we were scheduling the upgrade on current subscription end.
        TODO: Deprecate this
        """
        price_id = request.data.get("price_id")
        logger.info(f"Upgrading subscription {id} to price {price_id}")
        subscription: StripeSubscription = self.get_object()
        if subscription.cancel_at_period_end:
            subscription.renew()
        subscription.schedule(new_price_id=price_id)
        return Response("Upgrade scheduled")

    @action(detail=True, methods=("POST",), url_path="upgrade")
    def upgrade_subscription(self, request, id):
        """
        Return the stripe customer portal flow URL for confirming the subscription upgrade from the user, collecting the full payment.
        """
        current_pricing_version = get_pricing_version(user_mailbot_profile=request.user.user_mailbot_profile)
        serializer = CheckoutSessionSerializer(data=request.data, context={"pricing_version": current_pricing_version})
        serializer.is_valid(raise_exception=True)
        subscription: StripeSubscription = self.get_object()
        for subscription_schedule in subscription.subscription_schedules.all():
            subscription_schedule.release()
        session = create_update_subscription_deep_link_session(
            customer_id=subscription.customer_id,
            subscription_id=subscription.id,
            subscription_item_id=subscription.stripe_data["items"]["data"][0]["id"],
            new_price=serializer.validated_data["price"],
            coupon=serializer.validated_data.get("coupon"),
            current_pricing_version=current_pricing_version,
        )
        return Response(
            data={
                "subscription_update_confirm_url": session.url,
            }
        )


class InvoiceViewSet(ReadOnlyModelViewSet):
    serializer_class = StripeInvoiceSerializer
    lookup_field = "id"

    def get_queryset(self):
        try:
            secondary_mailbot_profile = SecondaryMailBotProfilesThrough.objects.get(
                secondary_mailbot_profile=self.request.user.user_mailbot_profile
            )
        except SecondaryMailBotProfilesThrough.DoesNotExist:
            primary_user = self.request.user
        else:
            primary_user = secondary_mailbot_profile.primary_mailbot_profile.user
        try:
            return StripeInvoice.objects.filter(
                customer_id=primary_user.stripe_customer.id, status=StripeInvoice.STATUS_PAID
            ).order_by("-created")
        except StripeCustomer.DoesNotExist:
            return StripeInvoice.objects.none()

    @action(detail=False, methods=("GET",), url_path="latest")
    def latest_invoice(self, request):
        queryset = self.get_queryset()
        invoice = queryset.first()
        serializer = self.get_serializer(invoice)
        return Response(serializer.data)


class CustomerPortalViewSet(ViewSet):
    @action(detail=False, methods=["POST"], url_path="session")
    def customer_portal_session(self, request):
        try:
            customer = request.user.stripe_customer
        except StripeCustomer.DoesNotExist:
            return Response(data="Customer does not exist", status=status.HTTP_404_NOT_FOUND)
        else:
            session = create_customer_portal_session(customer_id=customer.id)
            return Response(
                data={
                    "customer_portal_url": session.url,
                }
            )
