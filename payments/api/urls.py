from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import Webhook, InvoiceViewSet, SubscriptionViewSet, CustomerPortalViewSet

router = SimpleRouter()
router.register("invoices", InvoiceViewSet, basename="invoices")
router.register("subscriptions", SubscriptionViewSet, basename="subscriptions")
router.register(r"customer-portal", CustomerPortalViewSet, basename="CustomerPortalViewSet")

urlpatterns = router.urls
urlpatterns.extend(
    [
        path("webhook/<uuid:uuid>", Webhook.as_view(), name="webhook"),
    ]
)
