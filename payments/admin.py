from django.contrib import admin

from payments.models import (
    StripeCoupon,
    StripeCustomer,
    StripeDiscount,
    StripeFeature,
    StripeInvoice,
    StripeProduct,
    StripeSubscription,
    StripeSubscriptionSchedule,
    StripePrice,
    CouponPriceThrough,
)


# Register your models here.


@admin.register(StripeCustomer)
class StripeCustomerAdmin(admin.ModelAdmin):
    ordering = ("created",)


admin.site.register(StripeFeature)
admin.site.register(StripeProduct)


@admin.register(StripeSubscription)
class StripeSubscriptionAdmin(admin.ModelAdmin):
    ordering = ("current_period_start",)
    list_display = ("__str__", "current_period_start")


admin.site.register(StripeSubscriptionSchedule)
admin.site.register(StripePrice)
admin.site.register(StripeCoupon)
admin.site.register(StripeDiscount)
admin.site.register(StripeInvoice)


@admin.register(CouponPriceThrough)
class CouponPriceThroughAdmin(admin.ModelAdmin):
    ordering = ("version",)
    list_display = ("price", "coupon", "version")
