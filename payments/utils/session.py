from typing import Optional

from django.conf import settings
from stripe.billing_portal import Session as BillingPortalSession
from stripe.checkout import Session as CheckoutSession

from payments.models import StripeCustomer, StripePrice, CouponPriceThrough, StripeCoupon


def apply_coupon_to_session(session_kwargs: dict, coupon: Optional[StripeCoupon]):
    session_kwargs.update({"discounts": [{"coupon": coupon.id}]})
    return session_kwargs


def create_checkout_session(
    price: StripePrice,
    user,
    ui_mode: str,
    current_pricing_version: str,
    use_embedded_checkout: bool,
    is_trial=False,
    trial_period_days=0,
    coupon: Optional[StripeCoupon] = None,
    return_url: Optional[str] = None,
    success_url: Optional[str] = None,
    cancel_url: Optional[str] = None,
) -> CheckoutSession:
    if not CouponPriceThrough.objects.filter(
        price=price,
        coupon=coupon,
        version=current_pricing_version,
    ).exists():
        raise ValueError("price, coupon, and pricing version mismatches")
    session_kwargs = {
        "payment_method_types": ["card"],
        "mode": "subscription",
        "line_items": [
            {
                "price": price.id,
                "quantity": 1,
            },
        ],
        **(
            {"ui_mode": ui_mode, "return_url": return_url}
            if use_embedded_checkout
            else {"success_url": success_url, "cancel_url": cancel_url}
        ),
    }
    if is_trial:
        session_kwargs["subscription_data"] = {
            "trial_period_days": trial_period_days,
        }
    try:
        customer = StripeCustomer.objects.get(user=user)
    except StripeCustomer.DoesNotExist:
        # Stripe will create a new customer
        session_kwargs["customer_email"] = user.email
    else:
        session_kwargs["customer"] = customer.id
    if coupon:
        session_kwargs = apply_coupon_to_session(
            session_kwargs=session_kwargs,
            coupon=coupon,
        )
    return CheckoutSession.create(**session_kwargs)


def create_customer_portal_session(customer_id) -> BillingPortalSession:
    """
    Customer portal session is created by stripe for users to easily edit their payment preferences and view the invoice history.
    As no configuration is provided, this uses the default settings from https://dashboard.stripe.com/settings/billing/portal
    """
    return BillingPortalSession.create(customer=customer_id, return_url=f"{settings.FRONTEND_BASE_URL}/subscription/")


def create_update_subscription_deep_link_session(
    customer_id: str,
    subscription_id: str,
    subscription_item_id: str,
    new_price: StripePrice,
    current_pricing_version: str,
    coupon: Optional[StripeCoupon] = None,
) -> BillingPortalSession:
    """
    For upgrading subscription to a different price, we need to charge user immediately.
    We can use deep linking in stripe customer portal which will open the required flow instead of user clicking it.
    For that we have to allow subscription update in customer portal setting.
    Since we don't allow user updating subscription from customer portal when they go there clicking via manage payment,
    we are using custom configuration.
    """
    if not CouponPriceThrough.objects.filter(
        price=new_price,
        coupon=coupon,
        version=current_pricing_version,
    ).exists():
        raise ValueError("price, coupon, and pricing version mismatches")
    subscription_update_confirm = {
        "subscription": subscription_id,
        "items": [
            {
                "id": subscription_item_id,
                "quantity": 1,
                "price": new_price.id,
            },
        ],
    }
    if coupon:
        subscription_update_confirm = apply_coupon_to_session(coupon=coupon, session_kwargs=subscription_update_confirm)
    flow_data = {
        "type": "subscription_update_confirm",
        "subscription_update_confirm": subscription_update_confirm,
        "after_completion": {
            "type": "redirect",
            "redirect": {"return_url": f"{settings.FRONTEND_BASE_URL}/completion?price={new_price.nickname}"},
        },
    }

    configuration_mapping = {
        "power_v2": settings.STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE_PRICING_V4,
        "power_v3": settings.STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE_PRICING_V5,
        "power_v4": settings.STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE_PRICING_V6,
    }

    configuration = configuration_mapping.get(new_price.nickname, settings.STRIPE_PORTAL_CONFIGURATION_PAYMENT_UPGRADE)
    return BillingPortalSession.create(
        customer=customer_id,
        return_url=f"{settings.FRONTEND_BASE_URL}/subscription/",
        flow_data=flow_data,
        configuration=configuration,
    )


def create_setup_mode_checkout_session(success_url, cancel_url, user) -> CheckoutSession:
    """
    We are creating free subscription for user showing them $0 per month on checkout page so that we can collect their card details.
    We can enhance this experience using the "setup" mode, where only card details are required. After that we have to create the subscription, etc. on our side.
    TODO: Use this function when only card details are required
    """
    session_kwargs = {
        "payment_method_types": ["card"],
        "mode": "setup",
        "success_url": success_url,
        "cancel_url": cancel_url,
        "customer_email": user.email,
    }
    # Stripe will create a new customer
    return CheckoutSession.create(**session_kwargs)
