import logging
from django.conf import settings
from django.contrib.auth.models import User
from execfn.common.utils.posthog import get_posthog_client
from execfn.common.utils.url import append_query_params_to_url
from stripe import Coupon, Price
from stripe.checkout import Session as CheckoutSession
from payments.utils.session import create_checkout_session

logger = logging.getLogger(__name__)

class CheckoutFlow:
    def __init__(self):
        """Initializes the CheckoutFlow class."""
        self.posthog_client = get_posthog_client()

    def _get_embedded_checkout(self, user: User) -> bool:
        """Checks if the embedded checkout experience is enabled for a user.

        This function uses PostHog feature flags to determine if the 'new-checkout-experience'
        flag is enabled and has a payload of 'true' for the given user.

        Feature Flag Details:
            - feature flag: Name of the feature to check eg. new-checkout-experience
            - flag_enabled: Boolean, True if the flag is enabled for the user.
            - flag_variant: String, The variant of the feature flag eg. embedded

        Args:
            user: The user object to check the feature flag for.

        Returns:
            True if the embedded checkout is enabled for the user, False otherwise.
        """
        if self.posthog_client:
            flag_enabled = self.posthog_client.feature_enabled("new-checkout-experience", distinct_id=user.id)
            flag_variant = self.posthog_client.get_feature_flag("new-checkout-experience", distinct_id=user.id)
            if flag_enabled and flag_variant == "embedded":
                return True
        return False

    def get_success_redirect_url(
        self, price: Price, show_onboarding_tour: bool, user: User
    ) -> str:
        """Generates the success redirect URL after a checkout completion.

        Args:
            price: The Stripe Price object for the purchased plan.
            show_onboarding_tour: Boolean indicating if the onboarding tour should be shown.
            user: The user who completed the checkout.

        Returns:
            The URL to redirect the user to upon successful checkout completion.
            Includes query parameters for onboarding, price nickname, and checkout type.
        """
        return append_query_params_to_url(
            url=f"{settings.FRONTEND_BASE_URL}/completion",
            query_params={
                "show_onboarding_tour": show_onboarding_tour,
                "price": price.nickname,
                "checkout_type": "redirect" if not self._get_embedded_checkout(user=user) else "embedded",
            },
        )

    def _get_checkout_session(
        self,
        price: Price,
        coupon: Coupon,
        embedded_checkout: bool,
        user: User,
        current_pricing_version: str,
        success_redirect_url: str,
    ) -> CheckoutSession:
        """Creates a Stripe Checkout Session.

        Args:
            price: The Stripe Price object for the checkout.
            coupon: An optional Stripe Coupon object to apply.
            embedded_checkout: Boolean indicating if the embedded UI mode should be used.
            user: The user initiating the checkout.
            current_pricing_version: Identifier for the current pricing model version.
            success_redirect_url: The URL for redirection upon success (used for hosted or return URL for embedded).

        Returns:
            A Stripe CheckoutSession object configured based on the provided parameters.
        """
        return create_checkout_session(
            price=price,
            success_url=success_redirect_url if not embedded_checkout else None,
            cancel_url=settings.FRONTEND_BASE_URL if not embedded_checkout else None,
            user=user,
            coupon=coupon,
            current_pricing_version=current_pricing_version,
            ui_mode="embedded" if embedded_checkout else "hosted",
            return_url=success_redirect_url if embedded_checkout else None,
            use_embedded_checkout=embedded_checkout,
        )

    def get_checkout_url(
        self,
        price: Price,
        coupon: Coupon,
        user: User,
        current_pricing_version: str,
        show_onboarding_tour: bool,
    ) -> str:
        """Gets the appropriate checkout URL for the user.

        Determines whether to use the embedded or hosted Stripe checkout flow
        based on feature flags and plan type.

        Special Case:
            - The embedded checkout experience currently only applies to the 'basic_v4'
              plan when a coupon is used.

        Args:
            price: The Stripe Price object for the checkout.
            coupon: An optional Stripe Coupon object.
            user: The user initiating the checkout.
            current_pricing_version: Identifier for the current pricing model version.
            show_onboarding_tour: Boolean indicating if the onboarding tour parameter
                should be included in the success URL.

        Returns:
            The URL for the checkout. This will either be a Stripe hosted checkout URL
            or a frontend URL for the embedded checkout flow, including necessary parameters
            like client_secret.
        """
        embedded_checkout = self._get_embedded_checkout(user=user)
        success_redirect_url = self.get_success_redirect_url(
            price=price,
            show_onboarding_tour=show_onboarding_tour,
            user=user,
        )
        session = self._get_checkout_session(
            price=price,
            coupon=coupon,
            embedded_checkout=embedded_checkout,
            user=user,
            current_pricing_version=current_pricing_version,
            success_redirect_url=success_redirect_url,
        )

        if embedded_checkout and price.nickname == "basic_v4" and coupon:
            return append_query_params_to_url(
                url=f"{settings.FRONTEND_BASE_URL}/checkout",
                query_params={
                    "price": price.nickname,
                    "coupon": coupon.id,
                    "client_secret": session.client_secret,
                },
            )

        return session.url
    