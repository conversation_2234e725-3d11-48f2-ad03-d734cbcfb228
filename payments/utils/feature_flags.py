from payments.models import StripeCustomer, StripeSubscription
import logging

logger = logging.getLogger(__name__)


def get_payment_properties(user):
    try:
        customer = user.stripe_customer
        subscription = StripeSubscription.objects.filter(customer=customer, created__isnull=False).latest("created")
    except (StripeCustomer.DoesNotExist, StripeSubscription.DoesNotExist):
        return {}
    else:
        return {
            "subscription_status": subscription.status,
            "subscription_cancel_at_period_end": subscription.cancel_at_period_end,
            "subscription_created": subscription.created.isoformat(),
            "subscription_current_period_start": subscription.current_period_start.isoformat(),
            "subscription_current_period_end": subscription.current_period_end.isoformat(),
            "subscription_price": subscription.price.nickname,
        }
