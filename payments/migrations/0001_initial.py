# Generated by Django 4.2.5 on 2024-08-09 03:26

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="StripeCoupon",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                ("name", models.CharField(max_length=128)),
                (
                    "amount_off",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Amount in the currency specified that will be taken off the subtotal of any invoices for this customer",
                        null=True,
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        blank=True,
                        help_text="If amount_off has been set, the three-letter ISO code for the currency of the amount to take off",
                        max_length=3,
                        null=True,
                    ),
                ),
                (
                    "duration",
                    models.CharField(
                        choices=[("forever", "Forever"), ("once", "Once"), ("repeating", "Repeating")], max_length=32
                    ),
                ),
                (
                    "duration_in_months",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="If duration is repeating, the number of months the coupon applies",
                        null=True,
                    ),
                ),
                (
                    "percent_off",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Percent that will be taken off the subtotal of any invoices for this customer",
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                (
                    "max_redemptions",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="A positive integer specifying the number of times the coupon can be redeemed before it’s no longer valid",
                        null=True,
                    ),
                ),
                (
                    "redeem_by",
                    models.DateTimeField(
                        blank=True, help_text="Date after which the coupon can no longer be redeemed", null=True
                    ),
                ),
                (
                    "times_redeemed",
                    models.PositiveIntegerField(
                        default=0, help_text="Number of times the coupon has been applied to a customer"
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StripeCustomer",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="stripe_customer",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StripeFeature",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                ("name", models.CharField(max_length=128)),
                ("tag", models.CharField(max_length=32, unique=True)),
                ("active", models.BooleanField(default=True)),
                ("description", models.TextField()),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StripePrice",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                ("active", models.BooleanField(default=True)),
                (
                    "nickname",
                    models.CharField(blank=True, help_text="Nickname for the price", max_length=250, null=True),
                ),
                ("currency", models.CharField(default="usd", help_text="Currency code", max_length=3)),
                ("unit_amount", models.PositiveIntegerField(help_text="Amount in smallest currency denomination")),
                (
                    "interval",
                    models.CharField(
                        blank=True,
                        choices=[("month", "Month"), ("week", "Week"), ("year", "Year"), ("day", "Day")],
                        max_length=16,
                        null=True,
                    ),
                ),
                ("interval_count", models.PositiveIntegerField(blank=True, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StripeProduct",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                ("name", models.CharField(help_text="Product name can be basic, premium, etc.", max_length=128)),
                ("active", models.BooleanField(default=True)),
                ("description", models.TextField()),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StripeSubscription",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                ("cancel_at_period_end", models.BooleanField(default=False)),
                (
                    "currency",
                    models.CharField(
                        default="usd", help_text="Currency used for paying for this subscription", max_length=3
                    ),
                ),
                ("current_period_start", models.DateTimeField()),
                ("current_period_end", models.DateTimeField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("incomplete", "Incomplete"),
                            ("incomplete_expired", "Incomplete Expired"),
                            ("trialing", "Trialing"),
                            ("active", "Active"),
                            ("past_due", "Past Due"),
                            ("canceled", "Canceled"),
                            ("unpaid", "Unpaid"),
                            ("paused", "Paused"),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscriptions",
                        to="payments.stripecustomer",
                    ),
                ),
                (
                    "price",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscriptions",
                        to="payments.stripeprice",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscriptions",
                        to="payments.stripeproduct",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StripeWebhookEndpoint",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                ("uuid", models.UUIDField(unique=True)),
                ("enabled_events", models.JSONField(default=list)),
                ("secret", models.CharField(editable=False, max_length=256)),
                ("status", models.CharField(choices=[("enabled", "Enabled"), ("disabled", "Disabled")], max_length=32)),
                ("url", models.URLField(max_length=2048)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StripeWebhookEvent",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                ("data", models.JSONField(default=dict)),
                ("type", models.CharField(max_length=255)),
                (
                    "endpoint",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="events",
                        to="payments.stripewebhookendpoint",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="StripeSubscriptionSchedule",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("not_started", "Not Started"),
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("canceled", "Canceled"),
                            ("released", "Released"),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    "end_behavior",
                    models.CharField(
                        choices=[("release", "Release"), ("cancel", "Cancel")], default="release", max_length=32
                    ),
                ),
                ("phases", models.JSONField(blank=True, null=True)),
                (
                    "customer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscription_schedules",
                        to="payments.stripecustomer",
                    ),
                ),
                (
                    "subscription",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subscription_schedules",
                        to="payments.stripesubscription",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="stripeprice",
            name="product",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, related_name="prices", to="payments.stripeproduct"
            ),
        ),
        migrations.CreateModel(
            name="StripeInvoice",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                ("currency", models.CharField(max_length=3)),
                ("period_start", models.DateTimeField()),
                ("period_end", models.DateTimeField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("open", "Open"),
                            ("paid", "Paid"),
                            ("uncollectible", "Uncollectible"),
                            ("void", "Void"),
                        ],
                        max_length=32,
                    ),
                ),
                ("amount_due", models.PositiveIntegerField()),
                ("amount_paid", models.PositiveIntegerField()),
                ("attempt_count", models.PositiveIntegerField()),
                ("attempted", models.BooleanField()),
                (
                    "billing_reason",
                    models.CharField(
                        choices=[
                            ("manual", "Manual"),
                            ("subscription_create", "Subscription create"),
                            ("subscription_cycle", "Subcription cycle"),
                            ("subscription_threshold", "Subscription threshold"),
                            ("subscription_update", "Subscription update"),
                        ],
                        max_length=32,
                    ),
                ),
                (
                    "collection_method",
                    models.CharField(
                        choices=[("charge_automatically", "Charge automatically"), ("send_invoice", "Send Invoice")],
                        max_length=32,
                    ),
                ),
                ("status_transitions", models.JSONField()),
                ("hosted_invoice_url", models.URLField(blank=True, null=True)),
                (
                    "customer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoices",
                        to="payments.stripecustomer",
                    ),
                ),
                (
                    "subscription",
                    models.ForeignKey(
                        blank=True,
                        help_text="The subscription that this invoice was prepared for, if any.",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="invoices",
                        to="payments.stripesubscription",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Stripe Invoices",
            },
        ),
        migrations.CreateModel(
            name="StripeDiscount",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                ("start", models.DateTimeField(help_text="Date at which the coupon was applied")),
                (
                    "end",
                    models.DateTimeField(
                        blank=True, help_text="If the coupon has duration of repeating, the end date", null=True
                    ),
                ),
                (
                    "coupon",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="discounts",
                        to="payments.stripecoupon",
                    ),
                ),
                (
                    "customer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="discounts",
                        to="payments.stripecustomer",
                    ),
                ),
                (
                    "subscription",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="discounts",
                        to="payments.stripesubscription",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="CouponPriceThrough",
            fields=[
                ("id", models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name="ID")),
                (
                    "coupon",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="prices_through",
                        to="payments.stripecoupon",
                    ),
                ),
                (
                    "price",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="coupons_through",
                        to="payments.stripeprice",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ProductFeatureThrough",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was created in stripe", null=True
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        blank=True, help_text="DateTime the stripe object was last updated in stripe", null=True
                    ),
                ),
                ("id", models.CharField(max_length=255, primary_key=True, serialize=False, unique=True)),
                ("stripe_data", models.JSONField(default=dict)),
                ("metadata", models.JSONField(default=dict, help_text="Metadata for the stripe object")),
                (
                    "feature",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="products_through",
                        to="payments.stripefeature",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="features_through",
                        to="payments.stripeproduct",
                    ),
                ),
            ],
            options={
                "unique_together": {("product", "feature")},
            },
        ),
    ]
