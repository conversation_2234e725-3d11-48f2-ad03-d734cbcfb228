# Generated by Django 4.2.5 on 2025-01-15 08:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("payments", "0005_alter_stripesubscription_cancel_at_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="couponpricethrough",
            name="version",
            field=models.CharField(
                blank=True, help_text="Version of the pricing for which coupon is valid", max_length=4, null=True
            ),
        ),
        migrations.AlterField(
            model_name="couponpricethrough",
            name="coupon",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="prices_through",
                to="payments.stripecoupon",
            ),
        ),
        migrations.AlterField(
            model_name="couponpricethrough",
            name="price",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, related_name="coupons_through", to="payments.stripeprice"
            ),
        ),
        migrations.AddConstraint(
            model_name="couponpricethrough",
            constraint=models.UniqueConstraint(fields=("price", "version"), name="unique_coupon_price_version"),
        ),
    ]
