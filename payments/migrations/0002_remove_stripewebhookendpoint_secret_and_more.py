# Generated by Django 4.2.5 on 2024-08-13 07:48

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("payments", "0001_initial"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="stripewebhookendpoint",
            name="secret",
        ),
        migrations.AddField(
            model_name="stripewebhookendpoint",
            name="encrypted_secret",
            field=models.BinaryField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="stripewebhookevent",
            name="status",
            field=models.CharField(
                choices=[
                    ("scheduled", "Scheduled"),
                    ("processing", "Processing"),
                    ("succeeded", "Succeeded"),
                    ("failed", "Failed"),
                ],
                default="scheduled",
                max_length=32,
            ),
        ),
        migrations.AddConstraint(
            model_name="stripesubscriptionschedule",
            constraint=models.UniqueConstraint(
                condition=models.Q(("status", "active")),
                fields=("subscription", "status"),
                name="unique_active_subscription",
            ),
        ),
    ]
