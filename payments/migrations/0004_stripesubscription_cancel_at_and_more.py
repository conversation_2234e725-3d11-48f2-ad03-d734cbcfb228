# Generated by Django 4.2.5 on 2024-09-23 16:56

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("payments", "0003_alter_stripewebhookendpoint_encrypted_secret"),
    ]

    operations = [
        migrations.AddField(
            model_name="stripesubscription",
            name="cancel_at",
            field=models.DateTimeField(
                blank=True,
                help_text="A date in the future at which the subscription will automatically get canceled",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="stripesubscription",
            name="canceled_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
