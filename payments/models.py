import datetime
import json
import logging
from typing import Union
from uuid import uuid4

import stripe
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db.models.constraints import UniqueConstraint
from django.db import models
from stripe import (
    APIResource,
    Customer,
    Event,
    Product,
    Subscription,
    SubscriptionSchedule,
    WebhookEndpoint,
    Coupon,
    ProductFeature,
    CreateableAPIResource,
    UpdateableAPIResource,
    DeletableAPIResource,
    ListableAPIResource,
    Price,
    Discount,
    Invoice,
)
from stripe.entitlements import Feature

from execfn.common.utils.cryptography import decrypt_secret, encrypt_secret
from execfn.settings import APP_ENV_LOCAL
from payments.signals import subscription_canceled, subscription_renewed

User = get_user_model()
logger = logging.getLogger(__name__)


class StripeBaseModel(models.Model):
    stripe_class: Union[
        APIResource, DeletableAPIResource, CreateableAPIResource, UpdateableAPIResource, ListableAPIResource
    ]
    created = models.DateTimeField(null=True, blank=True, help_text="DateTime the stripe object was created in stripe")
    updated = models.DateTimeField(
        null=True, blank=True, help_text="DateTime the stripe object was last updated in stripe"
    )
    id = models.CharField(max_length=255, unique=True, primary_key=True)
    stripe_data = models.JSONField(default=dict)
    metadata = models.JSONField(default=dict, help_text="Metadata for the stripe object")

    class Meta:
        abstract = True

    def api_retrieve(self):
        """Retrieves the stripe object"""
        return self.stripe_class.retrieve(self.id)

    @classmethod
    def api_create(cls, **kwargs):
        """Creates the stripe object"""
        if not issubclass(cls.stripe_class, CreateableAPIResource):
            raise ValueError(f"{cls.stripe_class} is not a createable API resource")
        return cls.stripe_class.create(**kwargs)

    def api_delete(self, **kwargs):
        """Deletes the stripe object"""
        if not issubclass(self.stripe_class, DeletableAPIResource):
            raise ValueError(f"{self.stripe_class} is not a deletable API resource")
        return self.stripe_class.delete(self.id, **kwargs)

    def api_update(self, **kwargs):
        """Updates the stripe object"""
        if not issubclass(self.stripe_class, UpdateableAPIResource):
            raise ValueError(f"{self.stripe_class} is not an updateable API resource")
        return self.stripe_class.modify(self.id, **kwargs)

    @classmethod
    def is_valid_object(cls, data):
        """
        Returns whether the data is a valid object for the class
        """
        object_name: str = getattr(cls.stripe_class, "OBJECT_NAME", "")
        if not object_name:
            return False
        return data and data.get("object") == object_name

    @classmethod
    def should_sync_stripe_object(cls, stripe_object):
        """
        Returns whether the stripe object should be synced.
        We want to skip syncing objects that are deleted or non active.
        """
        return True

    @classmethod
    def stripe_data_to_record(cls, data):
        """
        Converts the stripe data to a record
        """
        if not cls.is_valid_object(data):
            raise ValueError(f"Invalid object for {cls.__name__}")
        record = {
            "stripe_data": data,
        }
        for field in cls._meta.get_fields():
            if field.name in data:
                if isinstance(field, (models.ManyToManyField, models.ManyToOneRel)):
                    # don't sync from reverse side of Many relationship
                    continue
                if isinstance(field, (models.ForeignKey, models.OneToOneField)) and issubclass(
                    field.related_model, StripeBaseModel
                ):
                    logger.info(f"Syncing related field {field.name} recursively from {cls.__name__}")
                    field_data = data[field.name]
                    if not field_data:
                        if field.null:
                            record[field.name] = None
                            continue
                        else:
                            raise ValueError(f"Related field {field.name} is required")
                    related_field = field.related_model.update_or_create_from_stripe_data(field_data, force_fetch=False)
                    if not related_field and not field.null:
                        # if related field is required and not found, return None
                        # This happens when the related field is not valid anymore, example: customer is deleted
                        return None
                    else:
                        record[field.name] = related_field
                elif isinstance(field, models.DateTimeField):
                    if datetime_field := data.get(field.name):
                        record[field.name] = datetime.datetime.fromtimestamp(datetime_field, tz=datetime.timezone.utc)
                    else:
                        record[field.name] = None
                else:
                    record[field.name] = data.get(field.name)
        stripe_data = record.pop("stripe_data")
        record["stripe_data"] = stripe_data
        return record

    @classmethod
    def get_stripe_id_from_record(cls, record: dict):
        """
        Returns the stripe id from the record
        """
        return record.pop("id")

    @classmethod
    def update_or_create_from_stripe_data(cls, data, force_sync=False, force_fetch=True):
        """
        Updates or creates a record from stripe data.

        Args:
            data: Stripe data or stripe id
            force_sync: Whether to surpass the should_sync_stripe_object check, true if stripe webhook event returns deleted or inactive objects
            force_fetch: Whether to force fetch the object from stripe if not found in the database, this is false if we are syncing parent objects
        """
        if isinstance(data, str):
            if not force_fetch:
                try:
                    obj = cls.objects.get(id=data)
                    logger.info(f"Found {cls.__name__} with stripe id {data} in database")
                    return obj
                except cls.DoesNotExist:
                    pass
            data = cls.stripe_class.retrieve(data)
        if not (force_sync or cls.should_sync_stripe_object(data)):
            logger.info(f"Skipping {cls.__name__} with stripe id {data['id']} as it is deleted or inactive")
            return None
        record = cls.stripe_data_to_record(data)
        if not record:
            logger.info(f"Skipping {cls.__name__} with stripe id {data['id']} as it is invalid")
            return None
        stripe_id = cls.get_stripe_id_from_record(record)
        defaults = {
            "stripe_data": data,
            **record,
        }
        obj, created = cls.objects.update_or_create(
            id=stripe_id,
            defaults=defaults,
        )
        if created:
            logger.info(f"Created new instance of {cls.__name__} with stripe id {data['id']}")
        return obj

    @classmethod
    def get_or_create_from_stripe_data(cls, data, force_sync=False):
        """
        Gets or creates a record from stripe data.

        Args:
            data: Stripe data or stripe id
            force_sync: Whether to surpass the should_sync_stripe_object check, true if stripe webhook event returns deleted or inactive objects
            force_fetch: Whether to force fetch the object from stripe if not found in the database, this is false if we are syncing parent objects
        """
        if isinstance(data, str):
            try:
                obj = cls.objects.get(id=data)
                logger.info(f"Found {cls.__name__} with stripe id {data} in database")
                return obj
            except cls.DoesNotExist:
                pass
            data = cls.stripe_class.retrieve(data)
        if not (force_sync or cls.should_sync_stripe_object(data)):
            logger.info(f"Skipping {cls.__name__} with stripe id {data['id']} as it is deleted or inactive")
            return None
        record = cls.stripe_data_to_record(data)
        if not record:
            logger.info(f"Skipping {cls.__name__} with stripe id {data['id']} as it is invalid")
            return None
        stripe_id = cls.get_stripe_id_from_record(record)
        defaults = {
            "stripe_data": data,
            **record,
        }
        obj, created = cls.objects.get_or_create(
            id=stripe_id,
            defaults=defaults,
        )
        if created:
            logger.info(f"Created new instance of {cls.__name__} with stripe id {data['id']}")
        return obj

    @classmethod
    def sync_from_stripe_data(cls, id=None, force_sync=False, force_fetch=True):
        """
        Syncs the stripe data to the database for the class.
        If id is provided, only that object is synced.

        Args:
            id: Stripe id of the object to sync
            force_sync: Whether to surpass the should_sync_stripe_object check, true if stripe webhook event returns deleted or inactive objects
            force_fetch: Whether to force fetch the object from stripe if not found in the database, this is false if we are syncing parent objects
        """
        if id:
            logger.info(f"Syncing single instance {id} for {cls.__name__}")
            cls.update_or_create_from_stripe_data(id, force_sync=force_sync, force_fetch=force_fetch)
        else:
            logger.info(f"Syncing all instances for {cls.__name__}")
            if not issubclass(cls.stripe_class, ListableAPIResource):
                raise ValueError(f"Stripe class {cls.stripe_class} is not listable")
            for stripe_object in cls.stripe_class.list().auto_paging_iter():
                cls.update_or_create_from_stripe_data(stripe_object, force_sync=force_sync, force_fetch=force_fetch)


class StripeCustomer(StripeBaseModel):
    stripe_class = Customer
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="stripe_customer")

    def __str__(self) -> str:
        return self.user.email

    @classmethod
    def should_sync_stripe_object(cls, stripe_object):
        if isinstance(stripe_object, dict):
            return stripe_object.get("deleted") is not True
        else:
            return True

    @classmethod
    def stripe_data_to_record(cls, data):
        email = data.get("email")
        if not email:
            raise ValueError("Email is required for a customer")
        record = super().stripe_data_to_record(data)
        user_id = User.objects.get(email=email).id
        return {
            "user_id": user_id,
            **record,
        }


class StripeProduct(StripeBaseModel):
    stripe_class = Product
    name = models.CharField(max_length=128, help_text="Product name can be basic, premium, etc.")
    active = models.BooleanField(default=True)
    description = models.TextField()

    def __str__(self) -> str:
        return self.name

    @classmethod
    def should_sync_stripe_object(cls, stripe_object):
        return stripe_object["active"]

    @classmethod
    def create(cls, name, description):
        """Creates a new stripe product"""
        stripe_product = stripe.Product.create(name=name, description=description)
        product = cls.objects.create(
            name=stripe_product.name, description=stripe_product.description, id=stripe_product.id
        )
        return product

    def purge(self):
        stripe.Product.delete(self.id)
        self.delete()

    def modify(self, name=None, description=None):
        """Updates the stripe product"""
        stripe_product = stripe.Product.modify(self.id, name=name, description=description)
        self.name = stripe_product.name
        self.description = stripe_product.description
        self.save()


class StripeFeature(StripeBaseModel):
    stripe_class = Feature
    name = models.CharField(max_length=128)
    tag = models.CharField(max_length=32, unique=True)
    active = models.BooleanField(default=True)
    description = models.TextField()

    def __str__(self) -> str:
        return self.name

    @classmethod
    def should_sync_stripe_object(cls, stripe_object):
        return stripe_object["active"]

    @classmethod
    def create(cls, name, tag, description):
        entitlement = stripe.entitlements.Feature.create(
            name=name,
            lookup_key=tag,
        )
        feature = cls.objects.create(name=name, tag=tag, description=description, id=entitlement.id)
        return feature

    def purge(self):
        stripe.entitlements.Feature.modify(self.id, active=False)
        self.delete()

    def modify(self, name=None, description=None):
        if name:
            stripe.entitlements.Feature.modify(self.id, name=name)
        self.name = name
        if description:
            self.description = description
        self.save()


class ProductFeatureThrough(StripeBaseModel):
    stripe_class = ProductFeature
    product = models.ForeignKey(StripeProduct, on_delete=models.CASCADE, related_name="features_through")
    feature = models.ForeignKey(StripeFeature, on_delete=models.CASCADE, related_name="products_through")

    class Meta:
        unique_together = ("product", "feature")

    @classmethod
    def create(cls, product_id, feature_id):
        product_feature = stripe.Product.create_feature(
            product=product_id,
            entitlement_feature=feature_id,
        )
        cls.objects.create(
            id=product_feature.id,
            product_id=product_id,
            feature_id=feature_id,
        )

    def purge(self):
        stripe.Product.delete_feature(self.product.id, self.feature.id)
        self.delete()


class StripePrice(StripeBaseModel):
    INTERVAL_DAY = "day"
    INTERVAL_WEEKLY = "week"
    INTERVAL_MONTHLY = "month"
    INTERVAL_YEARLY = "year"

    INTERVAL_CHOICES = (
        (INTERVAL_MONTHLY, "Month"),
        (INTERVAL_WEEKLY, "Week"),
        (INTERVAL_YEARLY, "Year"),
        (INTERVAL_DAY, "Day"),
    )
    stripe_class = Price
    product = models.ForeignKey(StripeProduct, on_delete=models.CASCADE, related_name="prices")
    active = models.BooleanField(default=True)
    nickname = models.CharField(max_length=250, help_text="Nickname for the price", null=True, blank=True)
    currency = models.CharField(max_length=3, help_text="Currency code", default="usd")
    unit_amount = models.PositiveIntegerField(help_text="Amount in smallest currency denomination")
    interval = models.CharField(max_length=16, choices=INTERVAL_CHOICES, null=True, blank=True)
    interval_count = models.PositiveIntegerField(null=True, blank=True)

    def __str__(self) -> str:
        if not self.active:
            return f"{self.nickname} (Inactive)"
        else:
            return f"{self.nickname}"

    @classmethod
    def should_sync_stripe_object(cls, stripe_object):
        return stripe_object["active"]

    @classmethod
    def stripe_data_to_record(cls, data):
        recurring = data.get("recurring")
        if recurring:
            data["interval"] = data["recurring"]["interval"]
            data["interval_count"] = data["recurring"]["interval_count"]
        else:
            data["interval"] = None
            data["interval_count"] = None
        return super().stripe_data_to_record(data)

    @classmethod
    def create(cls, product_id, unit_amount, currency, interval, interval_count, nickname):
        stripe_price = stripe.Price.create(
            product=product_id,
            nickname=nickname,
            unit_amount=unit_amount,
            currency=currency,
            recurring={"interval": interval, "interval_count": interval_count},
        )
        price = cls.objects.create(
            id=stripe_price.id,
            product_id=product_id,
            nickname=stripe_price.nickname,
            unit_amount=stripe_price.unit_amount,
            currency=stripe_price.currency,
            interval=stripe_price.recurring.interval,
            interval_count=stripe_price.recurring.interval_count,
        )
        return price

    def purge(self):
        stripe.Price.modify(self.id, active=False)
        self.delete()


class StripeSubscription(StripeBaseModel):
    STATUS_INCOMPLETE = "incomplete"
    STATUS_INCOMPLETE_EXPIRED = "incomplete_expired"
    STATUS_TRIALING = "trialing"
    STATUS_ACTIVE = "active"
    STATUS_PAST_DUE = "past_due"
    STATUS_CANCELED = "canceled"
    STATUS_UNPAID = "unpaid"
    STATUS_PAUSED = "paused"
    STATUS_CHOICES = (
        (STATUS_INCOMPLETE, "Incomplete"),
        (STATUS_INCOMPLETE_EXPIRED, "Incomplete Expired"),
        (STATUS_TRIALING, "Trialing"),
        (STATUS_ACTIVE, "Active"),
        (STATUS_PAST_DUE, "Past Due"),
        (STATUS_CANCELED, "Canceled"),
        (STATUS_UNPAID, "Unpaid"),
        (STATUS_PAUSED, "Paused"),
    )
    CANCELED_DUE_TO_TOKENS_EXPIRED = "tokens_expired"
    CANCELED_DUE_TO_ACCOUNT_DELETION = "account_deleted"
    stripe_class = Subscription
    customer = models.ForeignKey(StripeCustomer, on_delete=models.CASCADE, related_name="subscriptions")
    price = models.ForeignKey(StripePrice, on_delete=models.CASCADE, related_name="subscriptions")
    product = models.ForeignKey(StripeProduct, on_delete=models.CASCADE, related_name="subscriptions")
    cancel_at_period_end = models.BooleanField(default=False)
    currency = models.CharField(max_length=3, help_text="Currency used for paying for this subscription", default="usd")
    current_period_start = models.DateTimeField()
    current_period_end = models.DateTimeField()
    cancel_at = models.DateTimeField(
        null=True, blank=True, help_text="Date in the future at which the subscription will automatically get canceled"
    )
    canceled_at = models.DateTimeField(null=True, blank=True, help_text="Date at which the subscription was canceled")
    status = models.CharField(max_length=32, choices=STATUS_CHOICES)

    def __str__(self) -> str:
        status = "canceled" if self.cancel_at_period_end else self.status
        return f"{self.customer.user.email} - {self.price.nickname} ({status})"

    @classmethod
    def stripe_data_to_record(cls, data):
        data["price"] = data["items"]["data"][0]["price"]["id"]
        data["product"] = data["items"]["data"][0]["price"]["product"]
        return super().stripe_data_to_record(data)

    @classmethod
    def create(cls, customer_id, price_id):
        stripe_subscription = stripe.Subscription.create(
            customer=customer_id,
            items=[{"price": price_id}],
        )
        subscription = cls.objects.create(
            id=stripe_subscription.id,
            customer_id=customer_id,
            price_id=price_id,
            product_id=stripe_subscription.plan.product,
            cancel_at_period_end=stripe_subscription.cancel_at_period_end,
            currency=stripe_subscription.currency,
            current_period_start=datetime.datetime.fromtimestamp(
                stripe_subscription.current_period_start, tz=datetime.timezone.utc
            ),
            current_period_end=datetime.datetime.fromtimestamp(
                stripe_subscription.current_period_end, tz=datetime.timezone.utc
            ),
            status=stripe_subscription.status,
        )
        return subscription

    def cancel(self, reason=None, reason_text=None):
        """
        Cancels the subscription with the given reason and text reason.

        Args:
            reason: The reason for canceling the subscription
            reason_text: The text reason for canceling the subscription
        """
        if self.status == self.STATUS_ACTIVE and not self.cancel_at_period_end:
            # release all subscription schedule if exists as stripe does not allow canceling subscription with active
            # schedules
            for subscription_schedule in self.subscription_schedules.all():
                subscription_schedule.release()
            if reason:
                self.metadata["cancel_reason"] = json.dumps(reason)
            if reason_text:
                self.metadata["cancel_reason_text"] = reason_text
            # Cancel at the period end if cancel_at is not provided
            stripe.Subscription.modify(
                self.id, cancel_at_period_end=True, metadata=self.metadata, proration_behavior="none"
            )
            self.cancel_at_period_end = True
            self.save(update_fields=["cancel_at_period_end", "metadata"])
            subscription_canceled.send(sender=self.__class__, instance=self)

    def renew(self):
        """
        Renews the subscription if it was canceled
        """
        if self.status == self.STATUS_ACTIVE and self.cancel_at_period_end:
            stripe.Subscription.modify(self.id, cancel_at_period_end=False, proration_behavior="none")
            self.cancel_at_period_end = False
            self.save(update_fields=["cancel_at_period_end"])
            subscription_renewed.send(sender=self.__class__, subscription=self)

    def schedule(self, new_price_id):
        """
        Schedules a subscription to transition to a new price after the current billing cycle ends.

        Args:
            new_price_id: The new price to transition to
        """
        stripe_subscription_schedule = stripe.SubscriptionSchedule.create(from_subscription=self.id)
        stripe_subscription_schedule = stripe.SubscriptionSchedule.modify(
            stripe_subscription_schedule.id,
            phases=[
                {
                    "items": [
                        {
                            "price": stripe_subscription_schedule.phases[0]["items"][0].price,
                            "quantity": stripe_subscription_schedule.phases[0]["items"][0].quantity,
                        }
                    ],
                    "start_date": stripe_subscription_schedule.phases[0].start_date,
                    "end_date": stripe_subscription_schedule.phases[0].end_date,
                },
                {
                    "items": [
                        {
                            "price": new_price_id,
                            "quantity": 1,
                        }
                    ],
                    "iterations": 1,
                },
            ],
        )
        subscription_schedule, created = StripeSubscriptionSchedule.objects.update_or_create(
            id=stripe_subscription_schedule.id,
            defaults={
                "customer_id": self.customer_id,
                "subscription_id": self.id,
                "status": stripe_subscription_schedule.status,
                "end_behavior": stripe_subscription_schedule.end_behavior,
                "phases": stripe_subscription_schedule.phases,
            },
        )
        if not created:
            # This is possible if webhook for subscription schedule creation is processed before we create schedule
            # in database
            logger.info(f"Subscription schedule is already created for subscription {self.id}")
        return subscription_schedule


class StripeSubscriptionSchedule(StripeBaseModel):
    STATUS_NOT_STARTED = "not_started"
    STATUS_ACTIVE = "active"
    STATUS_COMPLETED = "completed"
    STATUS_CANCELED = "canceled"
    STATUS_RELEASED = "released"
    STATUS_CHOICES = (
        (STATUS_NOT_STARTED, "Not Started"),
        (STATUS_ACTIVE, "Active"),
        (STATUS_COMPLETED, "Completed"),
        (STATUS_CANCELED, "Canceled"),
        (STATUS_RELEASED, "Released"),
    )
    END_BEHAVIOR_RELEASE = "release"
    END_BEHAVIOR_CANCEL = "cancel"
    END_BEHAVIOR_CHOICES = (
        (END_BEHAVIOR_RELEASE, "Release"),
        (END_BEHAVIOR_CANCEL, "Cancel"),
    )
    stripe_class = SubscriptionSchedule
    customer = models.ForeignKey(StripeCustomer, on_delete=models.CASCADE, related_name="subscription_schedules")
    subscription = models.ForeignKey(
        StripeSubscription, null=True, on_delete=models.CASCADE, related_name="subscription_schedules"
    )
    status = models.CharField(max_length=32, choices=STATUS_CHOICES)
    end_behavior = models.CharField(max_length=32, choices=END_BEHAVIOR_CHOICES, default=END_BEHAVIOR_RELEASE)
    phases = models.JSONField(null=True, blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=["subscription", "status"],
                condition=models.Q(status=StripeSubscription.STATUS_ACTIVE),
                name="unique_active_subscription",
            )
        ]

    def __str__(self) -> str:
        return f"{self.customer.user.email} - {self.status}"

    @classmethod
    def should_sync_stripe_object(cls, stripe_object):
        return stripe_object["status"] not in [cls.STATUS_CANCELED, cls.STATUS_RELEASED]

    def release(self):
        """
        Releases the subscription schedule without affecting the underlying subscription.
        """
        if self.status in [self.STATUS_ACTIVE, self.STATUS_NOT_STARTED]:
            stripe.SubscriptionSchedule.release(self.id)
            self.status = self.STATUS_RELEASED
            self.save(update_fields=["status"])


class StripeInvoice(StripeBaseModel):
    REASON_MANUAL = "manual"
    REASON_SUBSCRIPTION_CREATE = "subscription_create"
    REASON_SUBSCRIPTION_CYCLE = "subscription_cycle"
    REASON_SUBSCRIPTION_THRESHOLD = "subscription_threshold"
    REASON_SUBSCRIPTION_UPDATE = "subscription_update"

    BILLING_REASON_CHOICES = (
        (REASON_MANUAL, "Manual"),
        (REASON_SUBSCRIPTION_CREATE, "Subscription create"),
        (REASON_SUBSCRIPTION_CYCLE, "Subcription cycle"),
        (REASON_SUBSCRIPTION_THRESHOLD, "Subscription threshold"),
        (REASON_SUBSCRIPTION_UPDATE, "Subscription update"),
    )

    METHOD_CHARGE_AUTOMATICALLY = "charge_automatically"
    METHOD_SEND_INVOICE = "send_invoice"

    COLLECTION_METHOD_CHOICES = (
        (METHOD_CHARGE_AUTOMATICALLY, "Charge automatically"),
        (METHOD_SEND_INVOICE, "Send Invoice"),
    )

    STATUS_DRAFT = "draft"
    STATUS_OPEN = "open"
    STATUS_PAID = "paid"
    STATUS_UNCOLLECTIBLE = "uncollectible"
    STATUS_VOID = "void"

    STATUS_CHOICES = (
        (STATUS_DRAFT, "Draft"),
        (STATUS_OPEN, "Open"),
        (STATUS_PAID, "Paid"),
        (STATUS_UNCOLLECTIBLE, "Uncollectible"),
        (STATUS_VOID, "Void"),
    )
    stripe_class = Invoice
    customer = models.ForeignKey(StripeCustomer, on_delete=models.CASCADE, related_name="invoices")
    subscription = models.ForeignKey(
        StripeSubscription,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        help_text="The subscription that this invoice was prepared for, if any.",
        related_name="invoices",
    )
    currency = models.CharField(max_length=3)
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    status = models.CharField(choices=STATUS_CHOICES, max_length=32)
    amount_due = models.PositiveIntegerField()
    amount_paid = models.PositiveIntegerField()
    attempt_count = models.PositiveIntegerField()
    attempted = models.BooleanField()
    billing_reason = models.CharField(choices=BILLING_REASON_CHOICES, max_length=32)
    collection_method = models.CharField(choices=COLLECTION_METHOD_CHOICES, max_length=32)
    status_transitions = models.JSONField()
    hosted_invoice_url = models.URLField(null=True, blank=True)

    class Meta:
        verbose_name_plural = "Stripe Invoices"


class StripeCoupon(StripeBaseModel):
    DURATION_FOREVER = "forever"
    DURATION_ONCE = "once"
    DURATION_REPEATING = "repeating"
    DURATION_CHOICES = (
        (DURATION_FOREVER, "Forever"),
        (DURATION_ONCE, "Once"),
        (DURATION_REPEATING, "Repeating"),
    )
    stripe_class = Coupon
    name = models.CharField(max_length=128)
    amount_off = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Amount in the currency specified that will be taken off the subtotal of any invoices for this customer",
    )
    currency = models.CharField(
        max_length=3,
        null=True,
        blank=True,
        help_text="If amount_off has been set, the three-letter ISO code for the currency of the amount to take off",
    )
    duration = models.CharField(max_length=32, choices=DURATION_CHOICES)
    duration_in_months = models.PositiveIntegerField(
        null=True, blank=True, help_text="If duration is repeating, the number of months the coupon applies"
    )
    percent_off = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Percent that will be taken off the subtotal of any invoices for this customer",
        validators=[MinValueValidator(1), MaxValueValidator(100)],
    )
    max_redemptions = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="A positive integer specifying the number of times the coupon can be redeemed before it’s no longer valid",
    )
    redeem_by = models.DateTimeField(
        null=True, blank=True, help_text="Date after which the coupon can no longer be redeemed"
    )
    times_redeemed = models.PositiveIntegerField(
        default=0, help_text="Number of times the coupon has been applied to a customer"
    )

    def __str__(self) -> str:
        return self.name

    @classmethod
    def create(
        cls,
        name,
        duration,
        amount_off=None,
        currency=None,
        duration_in_months=None,
        percent_off=None,
        max_redemptions=None,
        redeem_by=None,
    ):
        stripe_coupon = stripe.Coupon.create(
            name=name,
            duration=duration,
            amount_off=amount_off,
            currency=currency,
            duration_in_months=duration_in_months,
            percent_off=percent_off,
            max_redemptions=max_redemptions,
            redeem_by=redeem_by,
        )
        coupon = cls.objects.create(
            id=stripe_coupon.id,
            name=stripe_coupon.name,
            duration=stripe_coupon.duration,
            amount_off=stripe_coupon.amount_off,
            currency=stripe_coupon.currency,
            duration_in_months=stripe_coupon.duration_in_months,
            percent_off=stripe_coupon.percent_off,
            max_redemptions=stripe_coupon.max_redemptions,
            redeem_by=stripe_coupon.redeem_by,
        )
        return coupon


class StripeDiscount(StripeBaseModel):
    stripe_class = Discount
    coupon = models.ForeignKey(StripeCoupon, on_delete=models.CASCADE, related_name="discounts")
    customer = models.ForeignKey(StripeCustomer, on_delete=models.CASCADE, related_name="discounts")
    subscription = models.ForeignKey(
        StripeSubscription, null=True, blank=True, on_delete=models.CASCADE, related_name="discounts"
    )
    start = models.DateTimeField(help_text="Date at which the coupon was applied")
    end = models.DateTimeField(null=True, blank=True, help_text="If the coupon has duration of repeating, the end date")

    def delete_for_customer(self):
        stripe.Customer.delete_discount(self.customer_id)
        self.delete()

    def delete_for_subscription(self):
        stripe.Subscription.delete_discount(self.subscription_id)
        self.delete()


class StripeWebhookEndpoint(StripeBaseModel):
    STATUS_ENABLED = "enabled"
    STATUS_DISABLED = "disabled"
    STATUS_CHOICES = (
        (STATUS_ENABLED, "Enabled"),
        (STATUS_DISABLED, "Disabled"),
    )
    stripe_class = WebhookEndpoint
    uuid = models.UUIDField(unique=True)
    enabled_events = models.JSONField(default=list)
    encrypted_secret = models.BinaryField()
    status = models.CharField(max_length=32, choices=STATUS_CHOICES)
    url = models.URLField(max_length=2048)

    def __str__(self) -> str:
        return self.url

    @property
    def secret(self) -> str:
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        return decrypt_secret(
            fernet_key=settings.AWS_STRIPE_FERNET_KEY_NAME if fetch_key_from_secret_manager else settings.FERNET_KEY,
            secret=self.encrypted_secret,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @secret.setter
    def secret(self, value: str):
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        self.encrypted_secret = encrypt_secret(
            fernet_key=settings.AWS_STRIPE_FERNET_KEY_NAME if fetch_key_from_secret_manager else settings.FERNET_KEY,
            secret=value,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @classmethod
    def create(cls, enabled_events):
        uuid_suffix = uuid4()
        url = f"{settings.BACKEND_BASE_URL}/api/v1/payments/webhook/{uuid_suffix}"
        stripe_webhook = stripe.WebhookEndpoint.create(url=url, enabled_events=enabled_events)
        webhook = cls.objects.create(
            id=stripe_webhook.id,
            uuid=uuid_suffix,
            url=stripe_webhook.url,
            enabled_events=stripe_webhook.enabled_events,
            secret=stripe_webhook.secret,
            status=stripe_webhook.status,
        )
        return webhook

    def modify(self, enabled_events=None, status=None):
        modify_fields = {
            "url": f"{settings.BACKEND_BASE_URL}/api/v1/payments/webhook/{self.uuid}",
        }
        if enabled_events:
            modify_fields["enabled_events"] = enabled_events
        if status:
            modify_fields["status"] = status
        stripe_webhook = stripe.WebhookEndpoint.modify(self.id, **modify_fields)
        self.enabled_events = stripe_webhook.enabled_events
        self.status = stripe_webhook.status
        self.url = stripe_webhook.url
        self.save()

    def purge(self):
        stripe.WebhookEndpoint.delete(self.id)
        self.delete()

    @classmethod
    def sync_from_stripe_data(cls):
        raise NotImplementedError(
            "Webhook endpoints not supported for syncing as secret is returned only once at creation"
        )


class StripeWebhookEvent(StripeBaseModel):
    EVENT_STATUS_PROCESSING = "processing"
    EVENT_STATUS_SUCCEEDED = "succeeded"
    EVENT_STATUS_FAILED = "failed"
    EVENT_STATUS_SCHEDULED = "scheduled"
    EVENT_STATUS_CHOICES = (
        (EVENT_STATUS_SCHEDULED, "Scheduled"),
        (EVENT_STATUS_PROCESSING, "Processing"),
        (EVENT_STATUS_SUCCEEDED, "Succeeded"),
        (EVENT_STATUS_FAILED, "Failed"),
    )
    stripe_class = Event
    endpoint = models.ForeignKey(StripeWebhookEndpoint, on_delete=models.CASCADE, related_name="events")
    data = models.JSONField(default=dict)
    type = models.CharField(max_length=255)
    status = models.CharField(max_length=32, choices=EVENT_STATUS_CHOICES, default=EVENT_STATUS_SCHEDULED)

    @classmethod
    def sync_from_stripe_data(cls):
        raise NotImplementedError("Webhook events not supported for syncing yet")


class CouponPriceThrough(models.Model):
    """
    Stripe coupon does not restrict the prices it can be applied to under a single product.
    This model restricts the prices that a coupon can be applied to.

    TODO: Rename or migrate the model and table as this is no longer only through table.
    It maintains which price is supported in which version (with or without coupon).
    """

    coupon = models.ForeignKey(
        StripeCoupon, on_delete=models.CASCADE, related_name="prices_through", null=True, blank=True
    )
    price = models.ForeignKey(StripePrice, on_delete=models.CASCADE, related_name="coupons_through")
    version = models.CharField(
        max_length=4, null=True, blank=True, help_text="Version of the pricing for which coupon is valid"
    )

    class Meta:
        constraints = [UniqueConstraint(fields=["price", "version"], name="unique_coupon_price_version")]

    def __str__(self):
        if self.coupon:
            return f"{self.coupon.name} - {self.price.nickname} ({self.version})"
        return f"{self.price.nickname} ({self.version})"
