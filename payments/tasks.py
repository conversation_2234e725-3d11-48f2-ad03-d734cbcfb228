import logging
from typing import Literal

from execfn.celery import app
from execfn.common.utils.cache import cache_lock
from payments.models import (
    StripeCoupon,
    StripeCustomer,
    StripeSubscriptionSchedule,
    StripeWebhookEvent,
    StripeProduct,
    StripePrice,
    StripeDiscount,
    StripeSubscription,
    StripeInvoice,
)
from payments.signals import invoice_paid, subscription_canceled

logger = logging.getLogger(__name__)

EVENT_TYPES = Literal[
    "coupon.created",
    "coupon.deleted",
    "coupon.updated",
    "customer.created",
    "customer.deleted",
    "customer.discount.created",
    "customer.discount.deleted",
    "customer.discount.updated",
    "customer.subscription.created",
    "customer.subscription.deleted",
    "customer.subscription.updated",
    "customer.updated",
    "invoice.paid",
    "price.created",
    "price.deleted",
    "price.updated",
    "product.created",
    "product.deleted",
    "product.updated",
    "subscription_schedule.canceled",
    "subscription_schedule.completed",
    "subscription_schedule.created",
    "subscription_schedule.released",
    "subscription_schedule.updated",
]


def process_customer_event(event_type: EVENT_TYPES, stripe_webhook_event: StripeWebhookEvent):
    if event_type == "customer.created":
        StripeCustomer.get_or_create_from_stripe_data(stripe_webhook_event.data)
    elif event_type == "customer.updated":
        StripeCustomer.update_or_create_from_stripe_data(stripe_webhook_event.data, force_sync=True)
    elif event_type == "customer.deleted":
        StripeCustomer.objects.filter(id=stripe_webhook_event.data["id"]).delete()


def process_product_event(event_type: EVENT_TYPES, stripe_webhook_event: StripeWebhookEvent):
    if event_type == "product.created":
        StripeProduct.get_or_create_from_stripe_data(stripe_webhook_event.data)
    elif event_type == "product.updated":
        StripeProduct.update_or_create_from_stripe_data(stripe_webhook_event.data, force_sync=True)
    elif event_type == "product.deleted":
        StripeProduct.objects.filter(id=stripe_webhook_event.data["id"]).delete()


def process_price_event(event_type: EVENT_TYPES, stripe_webhook_event: StripeWebhookEvent):
    if event_type == "price.created":
        StripePrice.get_or_create_from_stripe_data(stripe_webhook_event.data)
    elif event_type == "price.updated":
        StripePrice.update_or_create_from_stripe_data(stripe_webhook_event.data, force_sync=True)
    elif event_type == "price.deleted":
        StripePrice.objects.filter(id=stripe_webhook_event.data["id"]).delete()


def process_subscription_event(event_type: EVENT_TYPES, stripe_webhook_event: StripeWebhookEvent):
    if event_type == "customer.subscription.created":
        StripeSubscription.get_or_create_from_stripe_data(stripe_webhook_event.data)
    elif event_type in ("customer.subscription.updated", "customer.subscription.deleted"):
        # We don't want to delete subscriptions, as they are soft-deleted (marked as canceled)
        StripeSubscription.update_or_create_from_stripe_data(stripe_webhook_event.data, force_sync=True)
        if event_type == "customer.subscription.deleted":
            instance = StripeSubscription.objects.get(id=stripe_webhook_event.data["id"])
            subscription_canceled.send(instance=instance, sender=StripeSubscription)


def process_subscription_schedule_event(event_type: EVENT_TYPES, stripe_webhook_event: StripeWebhookEvent):
    if event_type == "subscription_schedule.created":
        StripeSubscriptionSchedule.get_or_create_from_stripe_data(stripe_webhook_event.data)
    else:
        StripeSubscriptionSchedule.update_or_create_from_stripe_data(stripe_webhook_event.data, force_sync=True)


def process_coupon_event(event_type: EVENT_TYPES, stripe_webhook_event: StripeWebhookEvent):
    if event_type == "coupon.created":
        StripeCoupon.get_or_create_from_stripe_data(stripe_webhook_event.data)
    elif event_type == "coupon.updated":
        StripeCoupon.update_or_create_from_stripe_data(stripe_webhook_event.data, force_sync=True)
    elif event_type == "coupon.deleted":
        StripeCoupon.objects.filter(id=stripe_webhook_event.data["id"]).delete()


def process_discount_event(event_type: EVENT_TYPES, stripe_webhook_event: StripeWebhookEvent):
    if event_type == "customer.discount.created":
        StripeDiscount.get_or_create_from_stripe_data(stripe_webhook_event.data)
    if event_type == "customer.discount.updated":
        StripeDiscount.update_or_create_from_stripe_data(stripe_webhook_event.data, force_sync=True)
    elif event_type == "customer.discount.deleted":
        StripeDiscount.objects.filter(id=stripe_webhook_event.data["id"]).delete()


def process_invoice_event(event_type: EVENT_TYPES, stripe_webhook_event: StripeWebhookEvent):
    if event_type == "invoice.paid":
        invoice = StripeInvoice.update_or_create_from_stripe_data(stripe_webhook_event.data, force_sync=True)
        invoice_paid.send(instance=invoice, sender=StripeInvoice)


@app.task(bind=True)
def process_application_specific_webhook(self, stripe_webhook_event_id):
    """
    Process webhooks related to product which will be processed after acquiring lock on key 'stripe:webhook'
    """
    logger.info(f"Received stripe webhook {stripe_webhook_event_id} for application")
    with cache_lock(key="stripe:webhook", lock_timeout=5 * 60, max_blocking_time=60) as locked:
        if locked:
            stripe_webhook_event = StripeWebhookEvent.objects.get(id=stripe_webhook_event_id)
            stripe_webhook_event.status = StripeWebhookEvent.EVENT_STATUS_PROCESSING
            stripe_webhook_event.save()
            logger.info(f"{stripe_webhook_event.id}: Processing webhook event {stripe_webhook_event.type}")
            try:
                if stripe_webhook_event.type.startswith("product."):
                    process_product_event(stripe_webhook_event.type, stripe_webhook_event)
                elif stripe_webhook_event.type.startswith("price."):
                    process_price_event(stripe_webhook_event.type, stripe_webhook_event)
                elif stripe_webhook_event.type.startswith("coupon."):
                    process_coupon_event(stripe_webhook_event.type, stripe_webhook_event)
            except Exception:
                logger.exception("Unhandled exception while processing stripe webhook event")
                stripe_webhook_event.status = StripeWebhookEvent.EVENT_STATUS_FAILED
                stripe_webhook_event.save()
            else:
                stripe_webhook_event.status = StripeWebhookEvent.EVENT_STATUS_SUCCEEDED
                stripe_webhook_event.save()
        else:
            self.retry(countdown=5 * 60, throw=False)


@app.task(bind=True)
def process_customer_specific_webhook(self, stripe_webhook_event_id, customer_id):
    """
    Process webhooks related to customer which will acquire lock on key 'stripe:webhook:customer_id'
    """
    logger.info(f"Received stripe webhook {stripe_webhook_event_id} for customer {customer_id}")
    with cache_lock(key=f"stripe:webhook:{customer_id}", lock_timeout=5 * 60, max_blocking_time=60) as locked:
        if locked:
            stripe_webhook_event = StripeWebhookEvent.objects.get(id=stripe_webhook_event_id)
            stripe_webhook_event.status = StripeWebhookEvent.EVENT_STATUS_PROCESSING
            stripe_webhook_event.save()
            logger.info(f"{stripe_webhook_event.id}: Processing webhook event {stripe_webhook_event.type}")
            try:
                if stripe_webhook_event.type.startswith("customer.subscription."):
                    process_subscription_event(stripe_webhook_event.type, stripe_webhook_event)
                elif stripe_webhook_event.type.startswith("subscription_schedule."):
                    process_subscription_schedule_event(stripe_webhook_event.type, stripe_webhook_event)
                elif stripe_webhook_event.type.startswith("customer.discount."):
                    process_discount_event(stripe_webhook_event.type, stripe_webhook_event)
                elif stripe_webhook_event.type in ("customer.created", "customer.updated", "customer.deleted"):
                    process_customer_event(stripe_webhook_event.type, stripe_webhook_event)
                elif stripe_webhook_event.type.startswith("invoice."):
                    process_invoice_event(stripe_webhook_event.type, stripe_webhook_event)
            except Exception:
                logger.exception("Unhandled exception while processing stripe webhook event")
                stripe_webhook_event.status = StripeWebhookEvent.EVENT_STATUS_FAILED
                stripe_webhook_event.save()
            else:
                stripe_webhook_event.status = StripeWebhookEvent.EVENT_STATUS_SUCCEEDED
                stripe_webhook_event.save()
        else:
            self.retry(countdown=5 * 60, throw=False)
