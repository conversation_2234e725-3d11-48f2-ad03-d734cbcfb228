from django.contrib.auth import get_user_model
from factory import Sequence, PostGenerationMethodCall, Faker
from factory.django import DjangoModelFactory

User = get_user_model()


class UserFactory(DjangoModelFactory):
    # using a sequence instead of faker will eliminate the off chance of collisions
    email = Sequence(lambda n: f"user{n}@domain.com")
    password = PostGenerationMethodCall("set_password", "default")
    first_name = Faker("first_name")
    last_name = Faker("last_name")

    class Meta:
        model = User
