import logging

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth import logout
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

User = get_user_model()
logger = logging.getLogger(__name__)


class LogoutAPIView(APIView):
    """
    Logout current session user.
    """

    def post(self, request: Request):
        """
        Remove the authenticated user's ID from the request and flush their session data.
        Clears specific cookies related to the user's session.

        Args:
            request (Request): The request object containing user session information.

        Returns:
            Response: A Response object indicating the result of the logout operation.
        """
        logout(request)
        # TODO : Delete all cookies for domain
        cookies_to_delete = (
            "token",
            "csrftoken",
            "mailbot_profile_id",
            "sessionid",
            "service_provider",
            "profile",
        )
        response = Response({"message": "Logout successful"})
        for cookie_to_delete in cookies_to_delete:
            response.delete_cookie(cookie_to_delete, domain=settings.COOKIE_DOMAIN, samesite="None")
        return response
