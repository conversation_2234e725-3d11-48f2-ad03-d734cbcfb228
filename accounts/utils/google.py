import logging
import time
from typing import List, Any, Dict

import jwt
import requests
from django.conf import settings
from django.utils import timezone
from google_auth_oauthlib.flow import Flow

from accounts.utils.base import generate_oauth_state

logger = logging.getLogger(__name__)


def get_google_sign_in_flow(
    client_id: str, client_secret: str, scopes: List[str], callback_uri: str, metadata: Dict[str, Any] = None
):
    # TODO : Auth and Token URI is same for all Oauth 2 apps, move these to a separate env variable
    client_config = {
        "web": {
            "auth_uri": settings.GOOGLE.get("auth_uri"),
            "token_uri": settings.GOOGLE.get("token_uri"),
            "client_id": client_id,
            "client_secret": client_secret,
        }
    }
    state = generate_oauth_state(
        provider=settings.SERVICE_PROVIDER_GOOGLE,
        expiry=timezone.now() + timezone.timedelta(seconds=300),
        metadata=metadata,
    )
    flow = Flow.from_client_config(client_config=client_config, scopes=scopes, redirect_uri=callback_uri, state=state)
    return flow


class GoogleRiscAPIService:
    """
    Risk Incident Sharing and Coordination (RISC) improves API efficiency and security by providing privacy-protected, secure webhooks.
    This will also help us to take steps if user's account is hijacked or lost.
    References:
        https://openid.net/specs/openid-risc-profile-specification-1_0.html
        https://developers.google.com/identity/protocols/risc#register_receiver

    Note:
         Events are delivered with limited retries so if your receiver is down for an extended period of time you may permanently miss some events.
         In that case we will identify the `TOKEN_REVOKED_EVENT` whenever we make first Google API call again.
    """

    issuer = "https://accounts.google.com"
    jwks_uri = "https://www.googleapis.com/oauth2/v3/certs"
    add_subject_endpoint = "https://risc.googleapis.com/v1beta/subjects:add"
    remove_subject_endpoint = "https://risc.googleapis.com/v1beta/subjects:remove"
    get_configuration_endpoint = "https://risc.googleapis.com/v1beta/stream"
    get_status_endpoint = "https://risc.googleapis.com/v1beta/stream/status"
    update_status_endpoint = "https://risc.googleapis.com/v1beta/stream/status:update"
    update_configuration_endpoint = "https://risc.googleapis.com/v1beta/stream:update"
    verification_endpoint = "https://risc.googleapis.com/v1beta/stream:verify"

    # https://developers.google.com/identity/protocols/risc#supported_event_types
    VERIFICATION_EVENT = "https://schemas.openid.net/secevent/risc/event-type/verification"
    TOKEN_REVOKED_EVENT = "https://schemas.openid.net/secevent/oauth/event-type/token-revoked"

    @staticmethod
    def make_bearer_token() -> str:
        """
        Sign the JWT using service account's private key.
        This authorization token can be used to make RISC API calls for one hour.
        When the token expires, generate a new one to continue to make RISC API calls.
        Returns:
            str: Auth bearer token that must be used while using RISC based API calls.
        """
        issuer = settings.GOOGLE_SERVICE_ACCOUNT.get("client_email")
        subject = settings.GOOGLE_SERVICE_ACCOUNT.get("client_email")
        private_key_id = settings.GOOGLE_SERVICE_ACCOUNT.get("private_key_id")
        private_key = settings.GOOGLE_SERVICE_ACCOUNT.get("private_key")
        issued_at = int(time.time())
        expires_at = issued_at + 3600
        payload = {
            "iss": issuer,
            "sub": subject,
            "aud": "https://risc.googleapis.com/google.identity.risc.v1beta.RiscManagementService",
            "iat": issued_at,
            "exp": expires_at,
        }
        encoded = jwt.encode(payload, private_key, algorithm="RS256", headers={"kid": private_key_id})
        return encoded

    @staticmethod
    def subscribe_risc_event_stream(auth_token, events_requested: List[str]):
        """
        Configure our project's security event stream, including registering our receiver endpoint.
        This will update or create the subscription configuration if not already present.
        If the subscription is stopped by us before, then we will have to resume it separately.
        Args:
            auth_token: Bearer token signed in using service account's private key.
            events_requested: Events that will be pushed to the webhook endpoint. Find available events at https://developers.google.com/identity/protocols/risc#supported_event_types.
        """
        headers = {"Authorization": f"Bearer {auth_token}"}
        stream_cfg = {
            "delivery": {
                "delivery_method": "https://schemas.openid.net/secevent/risc/delivery-method/push",
                "url": f"{settings.BACKEND_BASE_URL}/api/v1/accounts/google-security-events/",
            },
            "events_requested": events_requested,
        }
        response = requests.post(GoogleRiscAPIService.update_configuration_endpoint, json=stream_cfg, headers=headers)
        response.raise_for_status()

    @staticmethod
    def stop_risc_event_stream(auth_token):
        """
        Stop the event stream from Google.
        While the stream is deactivated, Google doesn't send events to your endpoint and doesn't buffer security events when they occur.
        Args:
            auth_token: Bearer token signed in using service account's private key.
        """
        headers = {"Authorization": f"Bearer {auth_token}"}
        response = requests.post(
            GoogleRiscAPIService.update_status_endpoint, json={"status": "disabled"}, headers=headers
        )
        response.raise_for_status()

    @staticmethod
    def resume_risc_event_stream(auth_token):
        """
        Re-enable the event stream to the same endpoint.
        Args:
            auth_token: Bearer token signed in using service account's private key.
        """
        headers = {"Authorization": f"Bearer {auth_token}"}
        response = requests.post(
            GoogleRiscAPIService.update_status_endpoint, json={"status": "enabled"}, headers=headers
        )
        response.raise_for_status()

    @staticmethod
    def is_risc_event_stream_enabled(auth_token):
        """
        Get the status of the subscription if it is enabled or not.
        This will be toggled by stop and resume APIs.
        Args:
            auth_token: Bearer token signed in using service account's private key.

        Returns:
            bool: True if google RISC subscription is enabled, false otherwise.
        """
        headers = {"Authorization": f"Bearer {auth_token}"}
        response = requests.get(GoogleRiscAPIService.get_status_endpoint, headers=headers)
        response.raise_for_status()
        return response.json().get("status") == "enabled"

    @staticmethod
    def get_configuration_status(auth_token):
        """
        If we ever want to modify our stream configuration, we can do so by getting the current stream configuration using this method,
        and modifying the response body, and sending it to the `subscription` API again.
        Args:
            auth_token: Bearer token signed in using service account's private key.
        """
        headers = {"Authorization": f"Bearer {auth_token}"}
        response = requests.get(GoogleRiscAPIService.get_configuration_endpoint, headers=headers)
        response.raise_for_status()
        return response.json()

    @staticmethod
    def test_risc_event_stream(auth_token):
        """
        We can verify that our stream configuration and receiver endpoint are working together correctly by
        sending a verification token through our event stream. Make sure to subscribe with VERIFICATION_EVENT first.
        Args:
            auth_token: Bearer token signed in using service account's private key.
        """
        headers = {"Authorization": f"Bearer {auth_token}"}
        state = {"state": f"Test token requested at {time.ctime()}"}
        response = requests.post(GoogleRiscAPIService.verification_endpoint, json=state, headers=headers)
        response.raise_for_status()


def watch_risc_events():
    """
    Subscribe to Google RISC event stream. Use of this method is optional as we have already handled the case
    when we first time initialize Google MailBox or making any Google API after access is revoked.
    TODO: Call this method from a startup script.
    """
    auth_token = GoogleRiscAPIService.make_bearer_token()
    if not GoogleRiscAPIService.is_risc_event_stream_enabled(auth_token=auth_token):
        logger.info("Subscribing to Google RISC event stream")
        GoogleRiscAPIService.subscribe_risc_event_stream(
            auth_token=auth_token, events_requested=[GoogleRiscAPIService.TOKEN_REVOKED_EVENT]
        )
        GoogleRiscAPIService.resume_risc_event_stream(auth_token=auth_token)
