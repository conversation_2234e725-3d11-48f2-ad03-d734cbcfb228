"""
This module contains functions for interacting with Microsoft API endpoints, either through direct requests
or via the official Microsoft Authentication Library (MSAL).
"""
import logging
from typing import Any, Dict, List, Optional

import requests
from django.conf import settings
from django.utils import timezone
from msal import ConfidentialClientApplication
from msal.token_cache import SerializableTokenCache
from rest_framework.request import Request

from accounts.utils.base import generate_oauth_state

logger = logging.getLogger(__name__)


def get_ms_email_id(access_token: str) -> Optional[str]:
    """
    Fetches user details using the provided access token and returns the Microsoft email Id if available.

    Args:
        access_token (str): Access token obtained through the authentication flow.

    Returns:
        Optional[str]: User's Microsoft email Id if available; otherwise, returns None.
    """
    try:
        r = requests.get(
            url=f"{settings.BASE_MICROSOFT_GRAPH_URL}/me",
            headers={"Authorization": f"Bearer {access_token}"},
        )
    except Exception as exc:
        logger.exception(
            "HTTP Error while fetching microsoft account detail",
            extra={"exception_details": str(exc)},
        )
        return None
    else:
        response = r.json()
        return response.get("mail")


"""
TODO: Discover if file-based persistence of access_token, refresh_token is required.
if os.path.exists("my_cache.bin"):
    token = open("my_cache.bin", "r").read()
    cache.deserialize(token)
    watch(cache.has_state_changed):
        if cache.has_state_changed:
            open("my_cache.bin", "w").write(cache.serialize())
"""


def load_cache(request: Request) -> SerializableTokenCache:
    """
    Loads the token cache from the request session, if available.

    Args:
        request (Request): The request object containing the user's session.

    Returns:
        SerializableTokenCache: The deserialized token cache.
    """
    cache = SerializableTokenCache()
    if token_cache := request.session.get("token_cache"):
        cache.deserialize(token_cache)
    return cache


def save_cache(request: Request, cache: SerializableTokenCache) -> None:
    """
    Saves the token cache to the user's session, if it has changed.

    Args:
        request (Request): The request object containing the user's session.
        cache (SerializableTokenCache): The token cache to be saved.
    """
    if cache.has_state_changed:
        request.session["token_cache"] = cache.serialize()


def get_msal_app(cache: SerializableTokenCache = None) -> ConfidentialClientApplication:
    """
    Initializes and returns the MSAL confidential client application.

    Args:
        cache (SerializableTokenCache, optional): The token cache to be used for caching tokens.

    Returns:
        ConfidentialClientApplication: The MSAL application instance.
    """
    # Initialize the MSAL confidential client
    auth_app = ConfidentialClientApplication(
        client_id=settings.MICROSOFT.get("app_id"),
        client_credential=settings.MICROSOFT.get("app_secret"),
        token_cache=cache,
    )
    return auth_app


def get_ms_sign_in_flow(scopes: List[str], callback_uri, metadata=None) -> Dict[str, Any]:
    """
    Initiates the MSAL authentication code flow and returns the authentication flow details.

    Returns:
        Dict[str, Any]: The authentication code flow details.
    """
    auth_app = get_msal_app()
    state = generate_oauth_state(
        provider=settings.SERVICE_PROVIDER_MICROSOFT,
        expiry=timezone.now() + timezone.timedelta(seconds=300),
        metadata=metadata,
    )
    return auth_app.initiate_auth_code_flow(scopes=scopes, redirect_uri=callback_uri, prompt="consent", state=state)


def get_ms_token_from_code(request: Request):
    """
    Acquires an access token using the provided request and authentication code flow.

    Args:
        request (Request): The request object containing the authentication flow details.

    Returns:
        Any: The result of the token acquisition.
    """
    cache = load_cache(request)
    auth_app = get_msal_app(cache)
    flow = request.session.pop("auth_flow", {})
    result = auth_app.acquire_token_by_auth_code_flow(flow, request.GET)
    save_cache(request, cache)
    return result


def remove_user_and_token(request: Request):
    """
    Removes the user and token-related data from the user's session.

    Args:
        request (Request): The request object containing the user's session.
    """
    if "token_cache" in request.session:
        del request.session["token_cache"]

    if "user" in request.session:
        del request.session["user"]


def get_ms_logout_url():
    """
    Constructs and returns the Microsoft logout URL with a post-logout redirect URI.

    Returns:
        str: The Microsoft logout URL.
    """
    return (
        settings.MICROSOFT["authority"]
        + "/oauth2/v2.0/logout"
        + "?post_logout_redirect_uri="
        + settings.MICROSOFT["logout_uri"]
    )
