from datetime import datetime
from typing import Dict, Any

import jwt
from django.conf import settings
from django.contrib.auth import get_user_model, login
from rest_framework.exceptions import AuthenticationFailed

from mailbot.utils.jwt import jwt_encode, jwt_decode

User = get_user_model()


def login_user(request, email, first_name, last_name):
    """
    Create or fetch existing user and login them by persisting user id and a backend in the request.
    Args:
        request: Request object
        email: Email of the user trying to log in.
        first_name: First name of the user.
        last_name: Last name of the user.

    Returns:
        User: Fetched or newly created user object.
    """
    user, _ = User.objects.get_or_create(
        email=email, defaults={"username": email, "first_name": first_name, "last_name": last_name}
    )
    login(request, user)
    return user


def generate_oauth_state(provider: str, expiry: datetime, metadata: Dict[str, Any] = None) -> str:
    """
    Generate OAuth verification state for the given provider. We only support GOOGLE and MICROSOFT as of today.
    This additionally allows us to send metadata with the OAuth request
    Args:
        provider: OAuth provider
        expiry: state token expiry
        metadata: state metadata

    Returns:
        state token
    """
    assert provider in (
        settings.SERVICE_PROVIDER_GOOGLE,
        settings.SERVICE_PROVIDER_MICROSOFT,
    ), f"Invalid oauth provider"
    jwt_payload = {
        "provider": provider,
        "exp": expiry,
    }
    if metadata:
        jwt_payload["metadata"] = metadata
    return jwt_encode(payload=jwt_payload)


def verify_and_decode_oauth_state(provider: str, jwt_token: str) -> Dict[str, Any]:
    """
    Verify OAuth state token for the provider and return the decoded state information
    Args:
        provider: OAuth provider to verify
        jwt_token: state token to verify

    Returns:
        state information
    """
    assert provider in (
        settings.SERVICE_PROVIDER_GOOGLE,
        settings.SERVICE_PROVIDER_MICROSOFT,
    ), f"Invalid oauth provider"
    try:
        payload = jwt_decode(jwt_token=jwt_token)
        assert provider == payload["provider"]
    except jwt.exceptions.DecodeError:
        raise AuthenticationFailed("Invalid state token.")
    except jwt.exceptions.ExpiredSignatureError:
        raise AuthenticationFailed("State token has expired")
    except (AssertionError, KeyError):
        raise AuthenticationFailed("OAuth provider does not match the state token's provider.")
    else:
        return payload
