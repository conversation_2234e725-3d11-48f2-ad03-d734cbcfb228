[tool.black]
line-length = 120
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "execfn.settings.tests"

[tool.poetry]
name = "backend"
version = "0.1.0"
description = "This is the backend directory of execfn project. Poetry is being used to manage dependencies."
authors = ["<PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
boto3 = "1.28.68"
celery = "5.3.4"
cryptography = "41.0.5"
django = "4.2.5"
django-celery-beat = "2.6.0"
django-constance = "3.1.0"
django-cors-headers = "4.3.0"
django-extensions = "3.2.3"
django-jsonform = "2.19.1"
django-ses = "3.5.0"
django-storages = "1.14.2"
django-structlog = {extras = ["celery"], version = "^7.1.0"}
djangorestframework = "3.14.0"
email-normalize = "^2.0.0"
factory-boy = "^3.3.0"
faker = "20.1.0"
google-api-python-client = "2.109.0"
google-auth-oauthlib = "1.1.0"
launchdarkly-server-sdk = "9.0.0"
msal = "1.25.0"
nltk = "^3.8.1"
o365 = "2.0.32"
openai = "1.3.5"
psycopg2-binary = "2.9.9"
pyjwt = "2.8.0"
pymemcache = "4.0.0"
pytest-django = "^4.7.0"
python = "3.9.16"
python-dotenv = "1.0.0"
stripe = "^10.5.0"
tldextract = "5.1.1"
gunicorn = "^21.2.0"
pycurl = "^7.45.2"
sentry-sdk = "^1.39.2"
jsonschema = "^4.21.1"
tiktoken = "^0.5.2"
ipython = "8.12.3"
minify-html = "^0.15.0"
google-ads = "^26.1.0"
django-webpush = "^0.3.5"
poetry = "^1.8.3"
setuptools = "^70.2.0"
redis = "^5.0.7"
pusher = "^3.3.2"
slack-sdk = "^3.33.1"
watchdog = "^5.0.2"
django-filter = "^24.3"
google-auth-httplib2 = "^0.2.0"
gspread = "^6.1.3"
pypdf2 = "^3.0.1"
python-docx = "^1.1.2"
langchain-google-genai = "^2.1.2"
langchain = "^0.3.23"
langchain-community = "^0.3.21"
pydantic = "^2.11.2"
posthog = "^3.23.0"



[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
