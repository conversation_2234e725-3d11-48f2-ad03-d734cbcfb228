import logging

import requests
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.authentication import BasicAuthentication
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from automation.api.authentication import IsTestUser
from automation.api.serializers import AutomationUserSerializer

User = get_user_model()
logger = logging.getLogger(__name__)


class AutomationUserViewSet(GenericViewSet):
    authentication_classes = (BasicAuthentication,)
    permission_classes = (IsAuthenticated, IsTestUser)

    def get_object(self):
        serializer = AutomationUserSerializer(data=self.request.data)
        serializer.is_valid(raise_exception=True)
        email = serializer.validated_data["email"]
        try:
            return User.objects.get(email=email)
        except User.DoesNotExist:
            return User.objects.none()

    @action(detail=False, methods=("POST",), url_path="delete")
    def delete_automation_user(self, request):
        user = self.get_object()
        if user:
            user.delete()
            return Response("User deleted successfully")
        else:
            return Response("User already deleted")

    @action(detail=False, methods=("POST",), url_path="revoke")
    def revoke_automation_user_access(self, request):
        user = self.get_object()
        if user:
            # TODO: Implement revoke for outlook
            revoke_endpoint = "https://oauth2.googleapis.com/revoke"
            payload = {"token": user.user_mailbot_profile.refresh_token}
            response = requests.post(revoke_endpoint, data=payload)
            if response.status_code == 200:
                return Response("Access revoked successfully")
            else:
                data = response.json()
                if data.get("error_description") == "Token expired or revoked":
                    return Response("Access already revoked")
                return Response(data=data, status=response.status_code)
        else:
            return Response("User does not exist for revoking access", status=status.HTTP_404_NOT_FOUND)
