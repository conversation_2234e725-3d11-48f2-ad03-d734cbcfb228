# Generated by Django 4.2.5 on 2025-05-20 09:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import django_extensions.db.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CalendarSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('subscription_id', models.CharField(default='', max_length=255)),
                ('resource_id', models.CharField(default='', max_length=255)),
                ('expire_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='SecondaryCalendarProfilesThrough',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
            ],
        ),
        migrations.CreateModel(
            name='UserCalendarProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('encrypted_access_token', models.BinaryField(blank=True, null=True)),
                ('encrypted_refresh_token', models.BinaryField(blank=True, null=True)),
                ('scope', models.JSONField(default=list)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('calendar_sync_enabled', models.BooleanField(default=True)),
                ('last_sync_time', models.DateTimeField(blank=True, null=True)),
                ('preferences', models.JSONField(default=dict)),
                ('deleted', models.DateTimeField(blank=True, null=True)),
                ('secondary_calendar_profiles', models.ManyToManyField(through='calendarbot.SecondaryCalendarProfilesThrough', to='calendarbot.usercalendarprofile')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='user_calendar_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='secondarycalendarprofilesthrough',
            name='primary_calendar_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='secondary_calendar_profiles_through', to='calendarbot.usercalendarprofile'),
        ),
        migrations.AddField(
            model_name='secondarycalendarprofilesthrough',
            name='secondary_calendar_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='calendarbot.usercalendarprofile', unique=True),
        ),
        migrations.CreateModel(
            name='CalendarWebhookLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('processing', 'Processing'), ('processed', 'Processed'), ('failed', 'Failed')], default='scheduled', max_length=32)),
                ('received_at', models.DateTimeField(auto_now_add=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('finished_at', models.DateTimeField(blank=True, null=True)),
                ('resource_state', models.CharField(blank=True, max_length=32, null=True)),
                ('channel_id', models.CharField(blank=True, max_length=255, null=True)),
                ('metadata', models.JSONField(default=dict)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('subscription', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='calendarbot.calendarsubscription')),
                ('user_calendar_profile', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='calendarbot.usercalendarprofile')),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='calendarsubscription',
            name='user_calendar_profile',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_subscription', to='calendarbot.usercalendarprofile'),
        ),
        migrations.CreateModel(
            name='CalendarEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('event_id', models.CharField(max_length=256)),
                ('source_calendar_id', models.CharField(max_length=256)),
                ('target_event_id', models.CharField(blank=True, max_length=256, null=True)),
                ('target_calendar_id', models.CharField(blank=True, max_length=256, null=True)),
                ('user_calendar_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='calendarbot.usercalendarprofile')),
            ],
        ),
        migrations.AddConstraint(
            model_name='secondarycalendarprofilesthrough',
            constraint=models.CheckConstraint(check=models.Q(('primary_calendar_profile', models.F('secondary_calendar_profile')), _negated=True), name='calendar_profile_cannot_be_both_primary_and_secondary'),
        ),
        migrations.AlterUniqueTogether(
            name='secondarycalendarprofilesthrough',
            unique_together={('primary_calendar_profile', 'secondary_calendar_profile')},
        ),
        migrations.AddIndex(
            model_name='calendarevent',
            index=models.Index(fields=['user_calendar_profile', 'event_id'], name='calendarbot_user_ca_58d56a_idx'),
        ),
        migrations.AddConstraint(
            model_name='calendarevent',
            constraint=models.UniqueConstraint(fields=('user_calendar_profile', 'event_id'), name='unique_event_per_profile'),
        ),
    ]
