from django.contrib import admin
from django.contrib import messages
from django.utils import timezone
from calendarbot.models import (
    UserCalendarProfile,
    CalendarEvent,
    SecondaryCalendarProfilesThrough,
    CalendarSubscription,
    CalendarWebhookLog,
)
from calendarbot.service.calendar import GoogleCalendarService


class ActiveProfileListFilter(admin.SimpleListFilter):
    title = "sync status"
    parameter_name = "sync_status"

    def lookups(self, request, model_admin):
        return (
            ("enabled", "Sync enabled"),
            ("disabled", "Sync disabled"),
        )

    def queryset(self, request, queryset):
        if self.value() == "enabled":
            return queryset.filter(calendar_sync_enabled=True)
        if self.value() == "disabled":
            return queryset.filter(calendar_sync_enabled=False)
        return queryset


@admin.action(description="Enable calendar sync")
def enable_sync(modeladmin, request, queryset):
    updated_count = queryset.filter(calendar_sync_enabled=False).update(calendar_sync_enabled=True)
    for profile in queryset.filter(calendar_sync_enabled=True):
        GoogleCalendarService(profile).setup_watch()

    messages.success(request, f"Calendar sync enabled for {updated_count} profiles.")


@admin.action(description="Disable calendar sync")
def disable_sync(modeladmin, request, queryset):
    profiles_to_update = queryset.filter(calendar_sync_enabled=True)

    for profile in profiles_to_update:
        try:
            subscription = CalendarSubscription.objects.get(user_calendar_profile=profile)
            subscription.delete()
        except CalendarSubscription.DoesNotExist:
            pass

    updated_count = profiles_to_update.update(calendar_sync_enabled=False)
    messages.success(request, f"Calendar sync disabled for {updated_count} profiles.")


@admin.register(UserCalendarProfile)
class UserCalendarProfileAdmin(admin.ModelAdmin):
    list_display = ["user", "calendar_sync_enabled", "last_sync_time", "is_primary", "has_active_watch"]
    list_filter = ["calendar_sync_enabled", ActiveProfileListFilter]
    search_fields = ["user__email"]
    readonly_fields = ["last_sync_time"]
    actions = [enable_sync, disable_sync]

    def has_active_watch(self, obj):
        """Check if the profile has an active watch subscription."""

        subscription = CalendarSubscription.objects.filter(user_calendar_profile=obj).first()

        if not subscription:
            return False

        return subscription.expire_at > timezone.now() if subscription.expire_at else False

    has_active_watch.boolean = True
    has_active_watch.short_description = "Active Watch"

    def get_queryset(self, request):
        # Show all profiles, including deleted ones
        return UserCalendarProfile.all_objects.all()


@admin.register(CalendarEvent)
class CalendarEventAdmin(admin.ModelAdmin):
    list_display = ["event_id", "user_calendar_profile", "source_calendar_id", "target_event_id"]
    list_filter = []
    search_fields = ["event_id", "user_calendar_profile__user__email"]


@admin.register(SecondaryCalendarProfilesThrough)
class SecondaryCalendarProfilesThroughAdmin(admin.ModelAdmin):
    list_display = ["primary_calendar_profile", "secondary_calendar_profile"]
    search_fields = ["primary_calendar_profile__user__email", "secondary_calendar_profile__user__email"]


@admin.register(CalendarSubscription)
class CalendarSubscriptionAdmin(admin.ModelAdmin):
    list_display = ["user_calendar_profile", "subscription_id", "expire_at"]
    search_fields = ["user_calendar_profile__user__email", "subscription_id"]
    readonly_fields = ["expire_at"]


@admin.register(CalendarWebhookLog)
class CalendarWebhookLogAdmin(admin.ModelAdmin):
    list_display = ["id", "user_calendar_profile", "status", "resource_state", "received_at", "finished_at"]
    list_filter = ["status", "resource_state"]
    search_fields = ["user_calendar_profile__user__email", "channel_id", "error_message"]
    readonly_fields = ["received_at", "started_at", "finished_at"]
    date_hierarchy = "received_at"
