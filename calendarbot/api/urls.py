from django.urls import path, include
from rest_framework.routers import DefaultRouter

from calendarbot.api.views import (
    GoogleCalendarAuthViewSet,
    CalendarProfileViewSet,
    CalendarWebhookAPIView,
)

router = DefaultRouter()
router.register(r"google-auth", GoogleCalendarAuthViewSet, basename="google-auth")
router.register(r"profiles", CalendarProfileViewSet, basename="profiles")

urlpatterns = [
    path("", include(router.urls)),
    path("webhook/", CalendarWebhookAPIView.as_view(), name="calendar-webhook"),
]
