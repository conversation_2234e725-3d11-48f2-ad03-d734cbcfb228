import logging
from django.conf import settings
from django.contrib.auth import get_user_model
from django.shortcuts import redirect, get_object_or_404
from django.utils import timezone

from rest_framework.decorators import action
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet
from rest_framework.views import APIView
from rest_framework import status

from accounts.utils.base import verify_and_decode_oauth_state
from calendarbot.models import (
    UserCalendarProfile,
    CalendarSubscription,
    CalendarWebhookLog,
)
from calendarbot.service.auth import GoogleCalendarAuthService
from calendarbot.service.calendar import GoogleCalendarService
from calendarbot.utils.profiles import create_calendar_profile, get_primary_user_from_request
from calendarbot.api.serializers import CalendarProfileSerializer
from calendarbot.tasks import process_calendar_webhook

User = get_user_model()
logger = logging.getLogger(__name__)


class GoogleCalendarAuthViewSet(ViewSet):
    """ViewSet for handling Google Calendar authorization."""

    @action(detail=False, methods=["GET"], url_path="login")
    def login(self, request):
        primary_user = get_primary_user_from_request(request.user)
        has_profile = hasattr(primary_user, "user_calendar_profile") and primary_user.user_calendar_profile
        metadata = {"primary_calendar_profile_id": primary_user.user_calendar_profile.id} if has_profile else {}

        flow = GoogleCalendarAuthService.get_sign_in_flow(metadata)
        authorization_url, _ = flow.authorization_url(
            access_type="offline",
            prompt="consent",
            login_hint=None if has_profile else primary_user.email,
        )

        return redirect(authorization_url)

    @action(detail=False, methods=["GET"], url_path="callback", authentication_classes=(), permission_classes=())
    def callback(self, request):
        response = redirect(settings.FRONTEND_BASE_URL)
        state_token = request.query_params.get("state")
        code = request.query_params.get("code")

        try:
            state_info = verify_and_decode_oauth_state(settings.SERVICE_PROVIDER_GOOGLE, state_token)
        except AuthenticationFailed:
            logger.warning("Invalid state token in Google Calendar auth callback")
            return response

        metadata = state_info.get("metadata", {})
        primary_calendar_profile_id = metadata.get("primary_calendar_profile_id")

        flow = GoogleCalendarAuthService.get_sign_in_flow(metadata)

        try:
            flow.fetch_token(code=code)
        except Exception as e:
            logger.exception(f"Incremental auth flow for Calendar failed: {e}")
            return response

        credentials = flow.credentials
        user_email = GoogleCalendarAuthService.verify_credentials(credentials)

        if not user_email:
            logger.warning("Invalid credentials for Calendar")
            return response

        try:
            user = get_object_or_404(User, email=user_email)
        except:
            logger.error(f"User with email {user_email} not found")
            return response

        create_calendar_profile(user=user, credentials=credentials, primary_id=primary_calendar_profile_id)

        return response

    @action(detail=False, methods=["GET"], url_path="check")
    def check(self, request):
        try:
            primary_user = get_primary_user_from_request(request.user)
            profile = UserCalendarProfile.objects.get(user=primary_user)
            service = GoogleCalendarService(profile)
            auth_creds_exist = service.is_authenticated()
            return Response(data={"auth_creds_exist": auth_creds_exist})
        except UserCalendarProfile.DoesNotExist:
            return Response(data={"auth_creds_exist": False})


class CalendarWebhookAPIView(APIView):
    authentication_classes = []  # No authentication required for webhooks
    permission_classes = []  # No permissions required for webhooks

    def post(self, request):
        channel_id = request.headers.get("X-Goog-Channel-ID")
        resource_state = request.headers.get("X-Goog-Resource-State")

        try:
            if not channel_id:
                logger.error("Missing X-Goog-Channel-ID header in webhook request")
                return Response(status=400)

            try:
                subscription = CalendarSubscription.objects.get(subscription_id=channel_id)
            except CalendarSubscription.DoesNotExist:
                logger.error(f"Received webhook for unknown channel: {channel_id}")
                return Response(status=404)

            webhook_log = CalendarWebhookLog.objects.create(
                user_calendar_profile=subscription.user_calendar_profile,
                subscription=subscription,
                status=CalendarWebhookLog.SCHEDULED,
                channel_id=channel_id,
                resource_state=resource_state,
                metadata={
                    "headers": dict(request.headers),
                    "body": request.data,
                },
            )

            process_calendar_webhook.delay(webhook_log.id)
            return Response(status=200)
        except Exception as e:
            logger.exception(f"Error processing calendar webhook: {str(e)}")
            return Response(status=500)


class CalendarProfileViewSet(ViewSet):
    @action(detail=False, methods=["GET"], url_path="primary-profile")
    def primary_profile(self, request):
        try:
            primary_user = get_primary_user_from_request(request.user)
            profile = UserCalendarProfile.objects.get(user=primary_user)
            serializer = CalendarProfileSerializer(profile)
            return Response(serializer.data)
        except UserCalendarProfile.DoesNotExist:
            return Response({"error": "No calendar profile found"}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=["GET"], url_path="secondary-profiles")
    def secondary_profiles(self, request):
        try:
            primary_user = get_primary_user_from_request(request.user)
            primary_profile = UserCalendarProfile.objects.get(user=primary_user)
            secondary_profiles = primary_profile.secondary_profiles
            serializer = CalendarProfileSerializer(secondary_profiles, many=True)
            return Response(serializer.data)
        except UserCalendarProfile.DoesNotExist:
            return Response({"error": "No calendar profile found"}, status=404)

    @action(detail=True, methods=["DELETE"])
    def delete_profile(self, request, pk=None):
        try:
            profile_to_delete = UserCalendarProfile.objects.get(id=pk)
            primary_user = get_primary_user_from_request(request.user)
            user_profile = UserCalendarProfile.objects.get(user=primary_user)
            is_secondary = user_profile.secondary_profiles.filter(id=pk).exists()

            if not is_secondary:
                return Response({"error": "No permission to delete this profile"}, status=status.HTTP_403_FORBIDDEN)
            profile_to_delete.soft_delete()
            return Response({"status": "Profile deleted successfully"})

        except UserCalendarProfile.DoesNotExist:
            return Response({"error": "Profile not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.exception(f"Error deleting profile: {str(e)}")
            return Response({"error": "Error occurred while deleting"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=["POST"])
    def toggle_active(self, request, pk=None):
        try:
            profile_to_toggle = UserCalendarProfile.objects.get(id=pk)
            primary_user = get_primary_user_from_request(request.user)
            user_profile = UserCalendarProfile.objects.get(user=primary_user)
            is_secondary = user_profile.secondary_profiles.filter(id=pk).exists()
            if not is_secondary:
                return Response({"error": "You don't have permission"}, status=status.HTTP_403_FORBIDDEN)

            if profile_to_toggle.calendar_sync_enabled:
                profile_to_toggle.calendar_sync_enabled = False
                GoogleCalendarService(profile_to_toggle).stop_watch()
            else:
                profile_to_toggle.calendar_sync_enabled = True
                GoogleCalendarService(profile_to_toggle).setup_watch()
            profile_to_toggle.save()
            return Response({"status": "Success", "calendar_sync_enabled": profile_to_toggle.calendar_sync_enabled})

        except UserCalendarProfile.DoesNotExist:
            return Response({"error": "Profile not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.exception(f"Error toggling profile sync status: {str(e)}")
            return Response(
                {"error": "An error occurred while toggling the profile's sync status"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
