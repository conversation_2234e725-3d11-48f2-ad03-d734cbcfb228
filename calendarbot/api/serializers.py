from rest_framework import serializers
from calendarbot.models import UserCalendarProfile


class CalendarProfileSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(source="user.email", read_only=True)
    first_name = serializers.CharField(source="user.first_name", read_only=True)
    last_name = serializers.CharField(source="user.last_name", read_only=True)
    is_primary = serializers.BooleanField(read_only=True)
    calendar_sync_enabled = serializers.BooleanField()

    class Meta:
        model = UserCalendarProfile
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "is_primary",
            "calendar_sync_enabled",
            "last_sync_time",
        ]
        read_only_fields = ["id", "email", "first_name", "last_name", "is_primary", "last_sync_time"]
