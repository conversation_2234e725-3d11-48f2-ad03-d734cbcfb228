import logging
from django.db import transaction
from calendarbot.models import SecondaryCalendarProfiles<PERSON>hrough, UserCalendarProfile
from calendarbot.service.calendar import GoogleCalendarService
from mailbot.models import SecondaryMailBotProfilesThrough

logger = logging.getLogger(__name__)


def get_primary_user_from_request(user):
    """
    Determines if the user is a primary or secondary user based on mailbot profiles.
    If secondary, returns the associated primary user, otherwise returns the user.

    Args:
        user: The user from the request

    Returns:
        User: The primary user (either the same user or their associated primary)
    """
    try:
        user_mailbot_profile = user.user_mailbot_profile
        try:
            secondary_profile = SecondaryMailBotProfilesThrough.objects.select_related(
                "primary_mailbot_profile__user"
            ).get(secondary_mailbot_profile=user_mailbot_profile)
            primary_user = secondary_profile.primary_mailbot_profile.user
            logger.info(f"Found primary user {primary_user.email} for secondary user {user.email}")
            return primary_user
        except SecondaryMailBotProfilesThrough.DoesNotExist:
            logger.info(f"User {user.email} is a primary user")
            return user
    except Exception as e:
        logger.exception(f"Error determining primary user for {user.email}: {str(e)}")
        return user


@transaction.atomic
def create_calendar_profile(user, credentials, primary_id=None):
    """Creates or updates a calendar profile and handles primary/secondary relationships.

    Args:
        user: User instance
        credentials: Google credentials object
        primary_id: Primary calendar profile

    Returns:
        bool: True if created, False otherwise
    """
    created = False
    profile = None

    try:
        existing_profile = UserCalendarProfile.all_objects.get(user=user)
        existing_profile.deleted = None
        existing_profile.scope = credentials.granted_scopes
        existing_profile.expires_at = credentials.expiry
        existing_profile.access_token = credentials.token
        existing_profile.refresh_token = credentials.refresh_token
        existing_profile.calendar_sync_enabled = True
        existing_profile.save()
        profile = existing_profile

    except UserCalendarProfile.DoesNotExist:
        profile = UserCalendarProfile.objects.create(
            user=user,
            scope=credentials.granted_scopes,
            expires_at=credentials.expiry,
            access_token=credentials.token,
            refresh_token=credentials.refresh_token,
        )
        created = True

    if primary_id:
        try:
            primary_profile = UserCalendarProfile.objects.get(id=primary_id)
            if primary_profile.id != profile.id:
                _, created = SecondaryCalendarProfilesThrough.objects.get_or_create(
                    primary_calendar_profile=primary_profile, secondary_calendar_profile=profile
                )
                if created:
                    GoogleCalendarService(profile).setup_watch()
                    logger.info(f"Secondary profile created: {profile.user.email} -> {primary_profile.user.email}")
            else:
                logger.warning(f"Attempted to create self-referential relationship for profile {profile.id}")
        except UserCalendarProfile.DoesNotExist:
            logger.error(f"Primary profile with ID {primary_id} not found or deleted")

    return created
