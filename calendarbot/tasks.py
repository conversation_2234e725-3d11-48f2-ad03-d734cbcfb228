import logging
from celery import shared_task
from django.utils import timezone
from calendarbot.service.calendar import GoogleCalendarService
from execfn.celery import app

from calendarbot.models import CalendarEvent, CalendarSubscription, CalendarWebhookLog, UserCalendarProfile

logger = logging.getLogger(__name__)


@app.task
def sync_all_calendars():
    """
    Sync all calendars for all users.

    This task is scheduled to run weekly.
    """
    profiles = UserCalendarProfile.objects.filter(calendar_sync_enabled=True, deleted__isnull=True)
    success_count, error_count = 0, 0

    for profile in profiles:
        try:
            service = GoogleCalendarService(profile)
            if service.is_authenticated():
                events_synced, errors, _ = service.sync_events()
                if errors == 0:
                    success_count += 1
                else:
                    error_count += 1
                logger.info(f"Synced {events_synced} events for profile {profile.id}")
            else:
                error_count += 1
                logger.error(f"Failed to authenticate for profile {profile.id}")
        except Exception as e:
            error_count += 1
            logger.exception(f"Error syncing calendar for profile {profile.id}: {str(e)}")

    return success_count, error_count


@app.task
def refresh_calendar_watches_task():
    """
    Refresh calendar watch subscriptions that are about to expire.

    Returns:
        tuple: (success_count, error_count)
    """
    expire_soon = timezone.now() + timezone.timedelta(days=1)
    subscriptions = CalendarSubscription.objects.filter(
        expire_at__lt=expire_soon, user_calendar_profile__calendar_sync_enabled=True
    )
    success_count, error_count = 0, 0

    for subscription in subscriptions:
        calendar_service = GoogleCalendarService(subscription.user_calendar_profile)
        if calendar_service.stop_watch(skip_cleanup=True):
            if calendar_service.setup_watch(skip_sync=True):
                success_count += 1
            else:
                error_count += 1
        else:
            error_count += 1
    return success_count, error_count


@app.task
def process_calendar_webhook(webhook_log_id):
    """
    Process a calendar webhook event asynchronously.

    Args:
        webhook_log_id: ID of the CalendarWebhookLog to process

    Returns:
        bool: True if processing was successful, False otherwise
    """
    try:
        webhook_log = CalendarWebhookLog.objects.get(id=webhook_log_id)
        webhook_log.mark_as_processing()

        resource_state = webhook_log.resource_state
        subscription = webhook_log.subscription

        if not subscription:
            logger.error(f"Subscription not found for webhook log {webhook_log_id}")
            webhook_log.mark_as_failed("Subscription not found")
            return False

        if resource_state == "sync":
            logger.info(f"Sync notification received for channel {webhook_log.channel_id}")
            webhook_log.mark_as_processed()
            return True
        elif resource_state in ("exists", "update", "not_exists"):
            profile = subscription.user_calendar_profile
            service = GoogleCalendarService(profile)

            try:
                events_synced, errors = service.sync_events()
                webhook_log.metadata.update({"events_synced": events_synced, "errors": errors})
                profile.last_sync_time = timezone.now()
                profile.save(update_fields=["last_sync_time"])
                logger.info(f"Calendar sync triggered for profile {profile.id}, synced {events_synced} events")
                webhook_log.mark_as_processed()
                return True
            except Exception as e:
                error_message = f"Error syncing events: {str(e)}"
                logger.exception(error_message)
                webhook_log.mark_as_failed(error_message)
                return False
        else:
            warning_message = f"Unknown resource state: {resource_state}"
            logger.warning(warning_message)
            webhook_log.mark_as_failed(warning_message)
            return False
    except CalendarWebhookLog.DoesNotExist:
        logger.error(f"Calendar webhook log with ID {webhook_log_id} not found")
        return False
    except Exception as e:
        logger.exception(f"Error processing calendar webhook {webhook_log_id}: {str(e)}")
        try:
            webhook_log = CalendarWebhookLog.objects.get(id=webhook_log_id)
            webhook_log.mark_as_failed(str(e))
        except CalendarWebhookLog.DoesNotExist:
            pass
        return False


@app.task
def clean_up_calendar_events(profile_id):
    try:
        profile = UserCalendarProfile.objects.get(id=profile_id)
        synced_events = CalendarEvent.objects.filter(user_calendar_profile=profile)
        for event in synced_events:
            event.delete()
        logger.info(f"Successfully cleaned up events for profile {profile_id}")
        return True
    except Exception as e:
        logger.exception(f"Error cleaning up events for profile {profile_id}: {str(e)}")
        return False


@app.task
def sync_calendar_events(profile_id):
    try:
        profile = UserCalendarProfile.objects.get(id=profile_id)
        service = GoogleCalendarService(profile)
        events_synced, errors = service.sync_events()
        logger.info(f"Async sync completed for profile {profile_id}: {events_synced} events, {errors} errors")
        return True
    except Exception as e:
        logger.exception(f"Error in async sync for profile {profile_id}: {str(e)}")
        return False
