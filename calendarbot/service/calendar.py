import logging
import uuid
from datetime import datetime
from django.utils import timezone
from django.conf import settings
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from calendarbot.service.auth import GoogleCalendarAuthService
from calendarbot.models import CalendarEvent, CalendarSubscription

logger = logging.getLogger(__name__)


class GoogleCalendarService:
    WEBHOOK_URL = f"{settings.BACKEND_BASE_URL}/api/v1/calendarbot/webhook/"
    WATCH_EXPIRATION_WEEKS = 4

    def __init__(self, user_calendar_profile):
        """
        Args:
            user_calendar_profile: UserCalendarProfile instance
        """
        self.profile = user_calendar_profile
        self.access_token, self.refresh_token = user_calendar_profile.get_credentials()
        self.credentials = None
        self.service = None
        if self.access_token and self.refresh_token:
            self.credentials = GoogleCalendarAuthService.get_credentials(self.access_token, self.refresh_token)
            self.service = build("calendar", "v3", credentials=self.credentials)

    def is_authenticated(self):
        return self.service is not None

    def get_setup_watch_request_body(self):
        expiration_time = timezone.now() + timezone.timedelta(weeks=self.WATCH_EXPIRATION_WEEKS)
        return {
            "id": str(uuid.uuid4()),
            "type": "web_hook",
            "address": self.WEBHOOK_URL,
            "token": str(self.profile.id),
            "expiration": int((expiration_time).timestamp() * 1000),
        }

    def get_default_params(self, response):
        return {
            "subscription_id": response.get("id"),
            "resource_id": response.get("resourceId"),
            "expire_at": timezone.make_aware(
                timezone.datetime.fromtimestamp(int(response.get("expiration", 0)) / 1000)
            ),
        }

    def get_default_sync_params(self):
        return {
            "calendarId": "primary",
            "maxResults": 100,
            "showDeleted": True,
            "singleEvents": True,
            "orderBy": "startTime",
        }

    def setup_watch(self, skip_sync=False):
        if self.profile.is_primary:
            logger.info(f"Skipping setup_watch for primary profile {self.profile.user.email}")
            return True
        try:
            watch_request_body = self.get_setup_watch_request_body()
            response = self.service.events().watch(calendarId="primary", body=watch_request_body).execute()
            CalendarSubscription.objects.update_or_create(
                user_calendar_profile=self.profile, defaults=self.get_default_params(response)
            )
            if not skip_sync:
                from calendarbot.tasks import sync_calendar_events

                sync_calendar_events.delay(profile_id=self.profile.id)
            return True
        except HttpError as e:
            logger.error(f"Google API error setting up calendar watch for {self.profile.user.email}: {str(e)}")
            return False
        except Exception as e:
            logger.exception(f"Error setting up calendar watch for {self.profile.user.email}: {str(e)}")
            return False

    def stop_watch(self, skip_cleanup=False):
        if self.profile.is_primary:
            logger.info(f"Skipping stop_watch for primary profile {self.profile.user.email}")
            return True
        try:
            subscription = CalendarSubscription.objects.get(user_calendar_profile=self.profile)
            subscription_id, resource_id = subscription.subscription_id, subscription.resource_id
            self.service.channels().stop(body={"id": subscription_id, "resourceId": resource_id}).execute()
            if not skip_cleanup:
                from calendarbot.tasks import clean_up_calendar_events

                clean_up_calendar_events.delay(profile_id=self.profile.id)
            subscription.delete()
            logger.info(f"Successfully stopped watch for {self.profile.user.email}")
            return True
        except CalendarSubscription.DoesNotExist:
            logger.exception(f"No subscription found for {self.profile.user.email}")
        except Exception as e:
            logger.exception(f"Error in stop_watch for {self.profile.user.email}: {str(e)}")
        return False

    def sync_events(self):
        sync_token = None
        if "sync_token" in self.profile.preferences:
            sync_token = self.profile.preferences["sync_token"]
        if not self.is_authenticated():
            logger.error(f"Cannot sync events: Missing credentials for {self.profile.user.email}")
            return 0, 1

        try:
            if not self.profile.calendar_sync_enabled:
                logger.info(f"Skipping sync for profile {self.profile.id} as sync is disabled")
                return 0, 0

            request_params = self.get_default_sync_params()
            if sync_token is not None:
                request_params["syncToken"] = sync_token
            else:
                start_time, end_time = self.profile.get_time_window()
                request_params["timeMin"] = start_time.isoformat()
                request_params["timeMax"] = end_time.isoformat()

            events_result = self.service.events().list(**request_params).execute()
            events_synced, errors = 0, 0

            for event in events_result.get("items", []):
                try:
                    event_id = event.get("id")
                    if not event_id:
                        logger.warning(f"Event without ID found for {self.profile.user.email}")
                        continue
                    if event.get("status") == "cancelled":
                        self.handle_deleted_event(event_id)
                    else:
                        self.process_event(event)
                    events_synced += 1
                except Exception as e:
                    logger.exception(f"Error processing event {event.get('id')}: {str(e)}")
                    errors += 1

            next_sync_token = events_result.get("nextSyncToken")
            if next_sync_token is not None:
                self.profile.preferences["sync_token"] = next_sync_token
                self.profile.save(update_fields=["preferences"])

            return events_synced, errors

        except HttpError as e:
            if e.resp.status == 410:  # Sync token expired
                logger.info(f"Sync token expired for {self.profile.user.email}, resetting")
                if "sync_token" in self.profile.preferences:
                    del self.profile.preferences["sync_token"]
                    self.profile.save(update_fields=["preferences"])
                return self.sync_events()
            else:
                logger.error(f"Google API error syncing events: {str(e)}")
                return 0, 1
        except Exception as e:
            logger.exception(f"Error syncing events for {self.profile.user.email}: {str(e)}")
            return 0, 1

    def handle_deleted_event(self, event_id):
        try:
            calendar_event = CalendarEvent.objects.get(user_calendar_profile=self.profile, event_id=event_id)
            if calendar_event.target_event_id:
                primary_profile = self.profile.primary_profile
                primary_service = GoogleCalendarService(primary_profile)
                try:
                    primary_service.service.events().delete(
                        calendarId="primary", eventId=calendar_event.target_event_id
                    ).execute()
                    logger.info(f"Deleted event {calendar_event.target_event_id} from primary calendar")
                except Exception as e:
                    logger.error(f"Error deleting event from primary calendar: {str(e)}")
            calendar_event.delete()
            logger.info(f"Deleted event record for {event_id}")
        except CalendarEvent.DoesNotExist:
            logger.info(f"Event {event_id} not found in database, already deleted")
        except Exception as e:
            logger.exception(f"Error handling deleted event {event_id}: {str(e)}")

    def process_event(self, event):
        # Only use fields that exist in the CalendarEvent model
        CalendarEvent.objects.update_or_create(
            user_calendar_profile=self.profile,
            event_id=event.get("id"),
            defaults={
                "source_calendar_id": event.get("calendarId", "primary"),
            },
        )

        if self.profile.is_secondary_profile:
            primary_profile = self.profile.primary_profile
            primary_service = GoogleCalendarService(primary_profile)
            if primary_service.is_authenticated():
                self._sync_event_to_primary(primary_service, event)

    def _sync_event_to_primary(self, primary_service, event):
        event_id = event.get("id")
        description = event.get("description", "")
        formatted_description = f"{description}\n\n" f"Synced from: {self.profile.user.email}"

        minimal_event = {
            "summary": event.get("summary", "No Title"),
            "description": formatted_description,
            "start": event.get("start"),
            "end": event.get("end"),
            "location": event.get("location", ""),
        }

        try:
            calendar_event = CalendarEvent.objects.get(user_calendar_profile=self.profile, event_id=event_id)

            if calendar_event.target_event_id:
                primary_service.service.events().update(
                    calendarId="primary", eventId=calendar_event.target_event_id, body=minimal_event
                ).execute()
                logger.info(f"Updated event {calendar_event.target_event_id} in primary calendar")
            else:
                created_event = (
                    primary_service.service.events().insert(calendarId="primary", body=minimal_event).execute()
                )
                calendar_event.target_event_id = created_event["id"]
                calendar_event.target_calendar_id = "primary"
                calendar_event.save()
                logger.info(f"Created event {created_event['id']} in primary calendar")

        except Exception as e:
            logger.exception(f"Error syncing event {event_id} to primary calendar: {str(e)}")

    def _parse_event_datetime(self, time_data):
        if not time_data:
            return None
        if "dateTime" in time_data:
            dt = datetime.fromisoformat(time_data["dateTime"].replace("Z", "+00:00"))
            return timezone.make_aware(dt) if timezone.is_naive(dt) else dt
        elif "date" in time_data:
            return datetime.strptime(time_data["date"], "%Y-%m-%d").replace(tzinfo=timezone.utc)
        return None
