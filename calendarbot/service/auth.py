import logging
from google.oauth2.credentials import Credentials
from django.conf import settings
from accounts.utils.google import get_google_sign_in_flow
from googleapiclient.discovery import build

logger = logging.getLogger(__name__)


class GoogleCalendarAuthService:
    """Service for handling Google Calendar authentication."""

    REQUIRED_SCOPES = [
        "https://www.googleapis.com/auth/calendar.events",
        "https://www.googleapis.com/auth/userinfo.email",
    ]

    @classmethod
    def get_credentials(cls, access_token, refresh_token):
        """
        Create Google OAuth2 credentials object from tokens.

        Args:
            access_token (str): The OAuth2 access token
            refresh_token (str): The OAuth2 refresh token

        Returns:
            google.oauth2.credentials.Credentials: Google credentials object
        """
        return Credentials(
            token=access_token,
            refresh_token=refresh_token,
            client_id=settings.GOOGLE_CALENDAR_AUTH.get("client_id"),
            client_secret=settings.GOOGLE_CALENDAR_AUTH.get("client_secret"),
            token_uri=settings.GOOGLE_CALENDAR_AUTH.get("token_uri"),
        )

    @classmethod
    def get_sign_in_flow(cls, metadata=None):
        """
        Get Google OAuth sign-in flow configured for Calendar API.

        Args:
            metadata (dict, optional): Additional metadata to include in the state. Defaults to None.

        Returns:
            google_auth_oauthlib.flow.Flow: Configured OAuth flow
        """
        CALLBACK_URI = f"{settings.BACKEND_BASE_URL}/api/v1/calendarbot/google-auth/callback/"

        return get_google_sign_in_flow(
            client_id=settings.GOOGLE_CALENDAR_AUTH.get("client_id"),
            client_secret=settings.GOOGLE_CALENDAR_AUTH.get("client_secret"),
            scopes=cls.REQUIRED_SCOPES,
            callback_uri=CALLBACK_URI,
            metadata=metadata,
        )

    @classmethod
    def verify_credentials(cls, credentials):
        """
        Verify if the user has valid calendar credentials.

        Args:
            credentials (Credentials): Google credentials object

        Returns:
            str: User email if valid, None otherwise
        """
        service = build("oauth2", "v2", credentials=credentials)
        user_info = service.userinfo().get().execute()

        granted_scopes = credentials.granted_scopes
        return user_info.get("email") if not set(cls.REQUIRED_SCOPES).difference(set(granted_scopes)) else None
