import logging
from django.dispatch import receiver
from django.db.models.signals import pre_delete
from calendarbot.signals import post_calendar_profile_delete
from calendarbot.models import CalendarSubscription, CalendarEvent
from calendarbot.service.calendar import GoogleCalendarService

logger = logging.getLogger(__name__)


@receiver(post_calendar_profile_delete)
def post_calendar_profile_deletion(sender, user, **kwargs):
    """
    Cancel user's calendar subscription once their calendar profile is deleted.
    This is for future scope if we attach it to stripe subscription like mailbot.
    """
    logger.info(f"Calendar profile deleted for user {user.email}")
    # Future implementation for subscription handling
    # try:
    #     subscription = StripeSubscription.objects.get(
    #         customer__user=user,
    #         status=StripeSubscription.STATUS_ACTIVE,
    #         cancel_at_period_end=False
    #     )
    # except StripeSubscription.DoesNotExist:
    #     pass
    # else:
    #     subscription.cancel(reason="Calendar profile deletion")
    #     logger.info(f"Subscription canceled for user {user.email} due to calendar profile deletion")


@receiver(pre_delete, sender=CalendarSubscription)
def pre_calendar_subscription_deletion(sender, instance, **kwargs):
    """
    Stop webhook and clean up synced events before deleting a calendar subscription.
    """
    logger.info(f"Stopping webhook for subscription {instance.subscription_id}")
    profile = instance.user_calendar_profile

    if profile.is_primary:
        ## Ideally this should not happen as we don't create primary profile's subscription
        logger.info(f"Skipping webhook stop for primary profile {profile.user.email}")
        return

    try:
        calendar_service = GoogleCalendarService(profile)
        try:
            calendar_service.service.channels().stop(
                body={"id": instance.subscription_id, "resourceId": instance.resource_id}
            ).execute()
            logger.info(f"Successfully stopped webhook for {profile.user.email}")
        except Exception as e:
            logger.exception(f"Error stopping webhook for {profile.user.email}: {str(e)}")
        calendar_service.clean_up_events()
        logger.info(f"Successfully cleaned up events for {profile.user.email}")
    except Exception as e:
        logger.exception(f"Error in pre_calendar_subscription_deletion for {profile.user.email}: {str(e)}")


@receiver(pre_delete, sender=CalendarEvent)
def pre_calendar_event_deletion(sender, instance, **kwargs):
    """
    Delete event from primary profile when a calendar event object is deleted.
    """
    try:
        if not instance.target_event_id or not instance.user_calendar_profile.is_secondary_profile:
            # Skip if there's no target event or if this isn't a secondary profile
            return

        primary_profile = instance.user_calendar_profile.primary_profile
        if not primary_profile:
            logger.warning(f"No primary profile found for {instance.user_calendar_profile.user.email}")
            return
        primary_service = GoogleCalendarService(primary_profile)
        primary_service.service.events().delete(calendarId="primary", eventId=instance.target_event_id).execute()
        logger.info(f"Successfully deleted event {instance.target_event_id} from primary calendar")
    except Exception as e:
        logger.exception(f"Error deleting event from primary profile: {str(e)}")
