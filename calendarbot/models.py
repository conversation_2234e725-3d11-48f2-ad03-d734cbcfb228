import logging
from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import models, transaction
from django.db.models.fields.related_descriptors import ReverseManyToOneDescriptor, ReverseOneToOneDescriptor
from django.utils import timezone
from django_extensions.db.models import TimeStampedModel
from google.auth.exceptions import RefreshError
from googleapiclient._auth import refresh_credentials
from calendarbot.managers import UserCalendarProfileManager
from calendarbot.service.auth import GoogleCalendarAuthService
from execfn.common.utils.cryptography import decrypt_secret, encrypt_secret
from execfn.settings import APP_ENV_LOCAL
from calendarbot.signals import post_calendar_profile_delete
from googleapiclient.discovery import build

User = get_user_model()
logger = logging.getLogger(__name__)


class UserCalendarProfile(TimeStampedModel):
    """
    Represents a user's calendar profile. This is the main model for the CalendarBot functionality, similar to UserMailBotProfile but completely independent.
    """

    DEFAULT_FUTURE_CALENDAR_WINDOW_WEEKS = 8
    DEFAULT_PAST_CALENDAR_WINDOW_WEEKS = 4

    objects = UserCalendarProfileManager()
    all_objects = models.Manager()

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name="user_calendar_profile")
    secondary_calendar_profiles = models.ManyToManyField(
        "self", through="SecondaryCalendarProfilesThrough", symmetrical=False
    )
    # TODO : Sometime in the future we should move this to an auth application
    encrypted_access_token = models.BinaryField(null=True, blank=True, editable=False)
    encrypted_refresh_token = models.BinaryField(null=True, blank=True, editable=False)
    scope = models.JSONField(default=list)
    expires_at = models.DateTimeField(null=True, blank=True)
    # Future Scope : We need to sync multiple calendars of a user
    # calendar_id = models.CharField(max_length=256, default="primary")  # ID of the calendar in Google
    calendar_sync_enabled = models.BooleanField(default=True)
    last_sync_time = models.DateTimeField(null=True, blank=True)
    preferences = models.JSONField(default=dict)
    deleted = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Calendar Profile for {self.user.email}"

    @property
    def is_primary(self):
        """Check if this is a primary calendar profile."""
        return not SecondaryCalendarProfilesThrough.objects.filter(secondary_calendar_profile=self).exists()

    def get_time_window(self):
        """Get the time window for calendar synchronization."""
        now = timezone.now()
        start = now - timezone.timedelta(weeks=self.DEFAULT_PAST_CALENDAR_WINDOW_WEEKS)
        end = now + timezone.timedelta(weeks=self.DEFAULT_FUTURE_CALENDAR_WINDOW_WEEKS)
        return start, end

    @property
    def access_token(self) -> str:
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        return decrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=self.encrypted_access_token,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @access_token.setter
    def access_token(self, value: str):
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        self.encrypted_access_token = encrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=value,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @property
    def refresh_token(self) -> str:
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        return decrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=self.encrypted_refresh_token,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @refresh_token.setter
    def refresh_token(self, value: str):
        fetch_key_from_secret_manager = settings.APP_ENV != APP_ENV_LOCAL
        self.encrypted_refresh_token = encrypt_secret(
            fernet_key=(
                settings.AWS_MAILBOT_AUTHENTICATION_FERNET_KEY_NAME
                if fetch_key_from_secret_manager
                else settings.FERNET_KEY
            ),
            secret=value,
            fetch_key_from_secret_manager=fetch_key_from_secret_manager,
        )

    @property
    def should_refresh_token(self):
        if not self.expires_at:
            return True
        now = timezone.now()
        expires_at = self.expires_at
        if timezone.is_naive(expires_at):
            expires_at = timezone.make_aware(expires_at)
        return now >= expires_at - timezone.timedelta(minutes=5)

    def get_credentials(self):
        access_token, refresh_token = None, None
        if self.should_refresh_token:
            try:
                credential = GoogleCalendarAuthService.get_credentials(self.access_token, self.refresh_token)
                refresh_credentials(credential)
            except RefreshError as refresh_error:
                logger.error(
                    f"Refreshing calendar credentials for user {self.user.email} failed due to {refresh_error}"
                )
            else:
                if credential.expiry and timezone.is_naive(credential.expiry):
                    self.expires_at = timezone.make_aware(credential.expiry)
                else:
                    self.expires_at = credential.expiry
                self.access_token = credential.token
                self.refresh_token = credential.refresh_token
                self.save(update_fields=["encrypted_access_token", "encrypted_refresh_token", "expires_at"])
                access_token, refresh_token = self.access_token, self.refresh_token
        else:
            access_token, refresh_token = self.access_token, self.refresh_token

        return access_token, refresh_token

    def soft_delete(self):
        """Soft deletes the calendar profile by setting deleted timestamp."""
        with transaction.atomic():
            if not self.is_primary:
                from calendarbot.service.calendar import GoogleCalendarService

                GoogleCalendarService(self).stop_watch()
            self.delete_related_objects()
            self.deleted = timezone.now()
            self.save()
            transaction.on_commit(lambda: post_calendar_profile_delete.send(user=self.user, sender=self))

    def get_related_objects(self):
        """Retrieves all related objects for reverse foreign keys and reverse one-to-one relationships."""
        attributes = vars(self._meta.model)
        reverse_attributes = [
            a for a, b in attributes.items() if type(b) in [ReverseManyToOneDescriptor, ReverseOneToOneDescriptor]
        ]
        related_objects = []
        for reverse_attribute in reverse_attributes:
            try:
                related_objects.extend(list(getattr(self, reverse_attribute).all()))
            except:
                pass
        return related_objects

    def delete_related_objects(self):
        """Deletes all related objects for reverse foreign keys and reverse one-to-one relationships."""
        for obj in self.get_related_objects():
            obj.delete()

    @property
    def secondary_profiles(self):
        return UserCalendarProfile.objects.filter(
            id__in=self.secondary_calendar_profiles_through.values_list("secondary_calendar_profile_id", flat=True)
        )

    @property
    def is_secondary_profile(self):
        return SecondaryCalendarProfilesThrough.objects.filter(secondary_calendar_profile=self).exists()

    @property
    def primary_profile(self):
        try:
            through = SecondaryCalendarProfilesThrough.objects.get(secondary_calendar_profile=self)
            return through.primary_calendar_profile
        except SecondaryCalendarProfilesThrough.DoesNotExist:
            return None

    def valid_credentials(self):
        access_token, refresh_token = self.get_credentials()
        return all([access_token, refresh_token])


class SecondaryCalendarProfilesThrough(TimeStampedModel):
    primary_calendar_profile = models.ForeignKey(
        UserCalendarProfile, on_delete=models.CASCADE, related_name="secondary_calendar_profiles_through"
    )
    secondary_calendar_profile = models.ForeignKey(UserCalendarProfile, on_delete=models.CASCADE, unique=True)

    class Meta:
        unique_together = ("primary_calendar_profile", "secondary_calendar_profile")
        constraints = [
            models.CheckConstraint(
                name="calendar_profile_cannot_be_both_primary_and_secondary",
                check=~models.Q(primary_calendar_profile=models.F("secondary_calendar_profile")),
            )
        ]

    def __str__(self):
        return f"Secondary Calendar Profile: {self.secondary_calendar_profile.user.email} -> Primary: {self.primary_calendar_profile.user.email}"


class CalendarEvent(TimeStampedModel):
    """
    Represents a calendar event.
    """

    user_calendar_profile = models.ForeignKey(UserCalendarProfile, on_delete=models.CASCADE)
    event_id = models.CharField(max_length=256)  # ID of the event in Google Calendar
    source_calendar_id = models.CharField(max_length=256)  # Original calendar ID
    target_event_id = models.CharField(max_length=256, null=True, blank=True)
    target_calendar_id = models.CharField(max_length=256, null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=["user_calendar_profile", "event_id"]),
        ]
        constraints = [
            models.UniqueConstraint(fields=["user_calendar_profile", "event_id"], name="unique_event_per_profile")
        ]

    def __str__(self):
        return f"Event {self.event_id} from {self.user_calendar_profile.user.email}"


class CalendarSubscription(TimeStampedModel):
    """
    Stores information about Google Calendar watch subscriptions.
    """

    user_calendar_profile = models.OneToOneField(
        UserCalendarProfile, on_delete=models.CASCADE, related_name="calendar_subscription"
    )
    subscription_id = models.CharField(max_length=255, default="")
    resource_id = models.CharField(max_length=255, default="")
    expire_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"Calendar subscription for {self.user_calendar_profile.user.email}"


class CalendarWebhookLog(TimeStampedModel):
    """
    Logs for Google Calendar webhook events.
    """

    SCHEDULED = "scheduled"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"

    STATUS_CHOICES = (
        (SCHEDULED, "Scheduled"),
        (PROCESSING, "Processing"),
        (PROCESSED, "Processed"),
        (FAILED, "Failed"),
    )

    user_calendar_profile = models.ForeignKey(UserCalendarProfile, on_delete=models.SET_NULL, null=True)
    subscription = models.ForeignKey(CalendarSubscription, on_delete=models.SET_NULL, null=True)
    status = models.CharField(max_length=32, choices=STATUS_CHOICES, default=SCHEDULED)
    received_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    finished_at = models.DateTimeField(null=True, blank=True)
    resource_state = models.CharField(max_length=32, null=True, blank=True)
    channel_id = models.CharField(max_length=255, null=True, blank=True)
    metadata = models.JSONField(default=dict)
    error_message = models.TextField(null=True, blank=True)

    def mark_as_processing(self):
        self.status = self.PROCESSING
        self.started_at = timezone.now()
        self.save(update_fields=["status", "started_at"])

    def mark_as_processed(self):
        self.status = self.PROCESSED
        self.finished_at = timezone.now()
        self.save(update_fields=["status", "finished_at"])

    def mark_as_failed(self, error_message=None):
        self.status = self.FAILED
        self.finished_at = timezone.now()
        if error_message:
            self.error_message = error_message
        self.save(update_fields=["status", "finished_at", "error_message"])

    def __str__(self):
        return f"Calendar webhook {self.channel_id} for {self.user_calendar_profile}"
