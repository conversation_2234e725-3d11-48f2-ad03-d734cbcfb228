name: Run Tests

on:
  pull_request:
    branches:
      - development
      - staging
      - main
env:
  DJ<PERSON><PERSON><PERSON>_SETTINGS_MODULE: execfn.settings.tests

concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: false

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:latest
        env:
          POSTGRES_DB: execfn_test
          POSTGRES_USER: execfn_test_user
          POSTGRES_PASSWORD: **********
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
      memcached:
        image: memcached
        ports:
          - 11211:11211

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9.16

      - name: Install Linux Dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libcurl4-openssl-dev libssl-dev

      - name: Install Poetry
        run: |
          python3 -m pip install --upgrade pip
          pip install poetry

      - name: Install Requirements
        run: |
          poetry install --no-root

      - name: Apply Migrations
        run: |
          poetry run ./manage.py migrate

      - name: Run Tests
        run: |
          poetry run pytest
